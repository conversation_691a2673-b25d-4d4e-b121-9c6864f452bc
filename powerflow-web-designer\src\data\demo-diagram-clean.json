{"name": "安徽移动淮南数据中心10kV变配电系统图", "description": "安徽移动淮南数据中心10kV变配电1#楼配电系统图", "size": {"width": 2000, "height": 1400}, "grid": {"visible": false, "size": 20, "snapToGrid": false, "majorGridSize": 100, "minorColor": "#e0e0e0", "majorColor": "#c0c0c0", "pattern": "lines"}, "viewport": {"position": {"x": 0, "y": 0}, "scale": 0.8}, "layers": [{"id": "layer-1", "name": "主电路层", "visible": true, "locked": false, "symbolInstanceIds": ["hv_feeder1", "hv_feeder2", "hv_switchgear1", "hv_switchgear2", "hv_tie_switch", "transformer1", "transformer2", "lv_switchgear1", "lv_switchgear2", "lv_tie_switch", "ups1", "ups2", "ups3", "ups4", "load_panel1", "load_panel2", "load_panel3", "load_panel4"], "connectionIds": ["conn1", "conn2", "conn3", "conn4", "conn5", "conn6", "conn7", "conn8", "conn9", "conn10", "conn11", "conn12", "conn13", "conn14", "conn15", "conn16", "conn17", "conn18"]}], "symbolInstances": {"hv_feeder1": {"id": "hv_feeder1", "definitionId": "circuit-breaker", "position": {"x": 300, "y": 50}, "rotation": 90, "scale": 1, "bindings": {"status": true, "current": 150}, "properties": {"name": "10kV进线间隔1"}}, "hv_feeder2": {"id": "hv_feeder2", "definitionId": "circuit-breaker", "position": {"x": 1200, "y": 50}, "rotation": 90, "scale": 1, "bindings": {"status": true, "current": 150}, "properties": {"name": "10kV进线间隔2"}}, "hv_switchgear1": {"id": "hv_switchgear1", "definitionId": "busbar", "position": {"x": 300, "y": 200}, "rotation": 0, "scale": 1.5, "bindings": {}, "properties": {"name": "10kV母线段1"}}, "hv_switchgear2": {"id": "hv_switchgear2", "definitionId": "busbar", "position": {"x": 1200, "y": 200}, "rotation": 0, "scale": 1.5, "bindings": {}, "properties": {"name": "10kV母线段2"}}, "hv_tie_switch": {"id": "hv_tie_switch", "definitionId": "load-switch", "position": {"x": 750, "y": 200}, "rotation": 0, "scale": 1, "bindings": {"status": false}, "properties": {"name": "10kV母联开关"}}, "transformer1": {"id": "transformer1", "definitionId": "transformer", "position": {"x": 300, "y": 400}, "rotation": 90, "scale": 1.2, "bindings": {"temperature": 45, "load": 75}, "properties": {"name": "10kV/0.4kV变压器1"}}, "transformer2": {"id": "transformer2", "definitionId": "transformer", "position": {"x": 1200, "y": 400}, "rotation": 90, "scale": 1.2, "bindings": {"temperature": 42, "load": 68}, "properties": {"name": "10kV/0.4kV变压器2"}}, "lv_switchgear1": {"id": "lv_switchgear1", "definitionId": "busbar", "position": {"x": 300, "y": 600}, "rotation": 0, "scale": 1.5, "bindings": {}, "properties": {"name": "0.4kV母线段1"}}, "lv_switchgear2": {"id": "lv_switchgear2", "definitionId": "busbar", "position": {"x": 1200, "y": 600}, "rotation": 0, "scale": 1.5, "bindings": {}, "properties": {"name": "0.4kV母线段2"}}, "lv_tie_switch": {"id": "lv_tie_switch", "definitionId": "load-switch", "position": {"x": 750, "y": 600}, "rotation": 0, "scale": 1, "bindings": {"status": false}, "properties": {"name": "0.4kV母联开关"}}, "ups1": {"id": "ups1", "definitionId": "generator", "position": {"x": 150, "y": 800}, "rotation": 0, "scale": 0.8, "bindings": {"power": 200, "status": "online"}, "properties": {"name": "UPS1"}}, "ups2": {"id": "ups2", "definitionId": "generator", "position": {"x": 450, "y": 800}, "rotation": 0, "scale": 0.8, "bindings": {"power": 200, "status": "online"}, "properties": {"name": "UPS2"}}, "ups3": {"id": "ups3", "definitionId": "generator", "position": {"x": 1050, "y": 800}, "rotation": 0, "scale": 0.8, "bindings": {"power": 200, "status": "online"}, "properties": {"name": "UPS3"}}, "ups4": {"id": "ups4", "definitionId": "generator", "position": {"x": 1350, "y": 800}, "rotation": 0, "scale": 0.8, "bindings": {"power": 200, "status": "online"}, "properties": {"name": "UPS4"}}, "load_panel1": {"id": "load_panel1", "definitionId": "load", "position": {"x": 100, "y": 1000}, "rotation": 0, "scale": 0.6, "bindings": {"power": 50}, "properties": {"name": "配电柜1"}}, "load_panel2": {"id": "load_panel2", "definitionId": "load", "position": {"x": 200, "y": 1000}, "rotation": 0, "scale": 0.6, "bindings": {"power": 45}, "properties": {"name": "配电柜2"}}, "load_panel3": {"id": "load_panel3", "definitionId": "load", "position": {"x": 1300, "y": 1000}, "rotation": 0, "scale": 0.6, "bindings": {"power": 53}, "properties": {"name": "配电柜3"}}, "load_panel4": {"id": "load_panel4", "definitionId": "load", "position": {"x": 1400, "y": 1000}, "rotation": 0, "scale": 0.6, "bindings": {"power": 49}, "properties": {"name": "配电柜4"}}}, "connections": {"conn1": {"id": "conn1", "source": {"symbolInstanceId": "hv_feeder1", "connectionPointId": "out"}, "target": {"symbolInstanceId": "hv_switchgear1", "connectionPointId": "top"}, "type": "cable", "lineType": "polyline", "label": "10kV进线1", "waypoints": [], "style": {"strokeColor": "#ff0000", "lineWidth": 3, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn2": {"id": "conn2", "source": {"symbolInstanceId": "hv_feeder2", "connectionPointId": "out"}, "target": {"symbolInstanceId": "hv_switchgear2", "connectionPointId": "top"}, "type": "cable", "lineType": "polyline", "label": "10kV进线2", "waypoints": [], "style": {"strokeColor": "#ff0000", "lineWidth": 3, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn3": {"id": "conn3", "source": {"symbolInstanceId": "hv_switchgear1", "connectionPointId": "right"}, "target": {"symbolInstanceId": "hv_tie_switch", "connectionPointId": "in"}, "type": "cable", "lineType": "polyline", "label": "10kV母联", "waypoints": [], "style": {"strokeColor": "#ff0000", "lineWidth": 3, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": true, "padding": 20}}, "conn4": {"id": "conn4", "source": {"symbolInstanceId": "hv_tie_switch", "connectionPointId": "out"}, "target": {"symbolInstanceId": "hv_switchgear2", "connectionPointId": "left"}, "type": "cable", "lineType": "polyline", "label": "", "waypoints": [], "style": {"strokeColor": "#ff0000", "lineWidth": 3, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": true, "padding": 20}}, "conn5": {"id": "conn5", "source": {"symbolInstanceId": "hv_switchgear1", "connectionPointId": "bottom"}, "target": {"symbolInstanceId": "transformer1", "connectionPointId": "primary"}, "type": "cable", "lineType": "polyline", "label": "10kV馈线1", "waypoints": [], "style": {"strokeColor": "#ff0000", "lineWidth": 3, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn6": {"id": "conn6", "source": {"symbolInstanceId": "hv_switchgear2", "connectionPointId": "bottom"}, "target": {"symbolInstanceId": "transformer2", "connectionPointId": "primary"}, "type": "cable", "lineType": "polyline", "label": "10kV馈线2", "waypoints": [], "style": {"strokeColor": "#ff0000", "lineWidth": 3, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn7": {"id": "conn7", "source": {"symbolInstanceId": "transformer1", "connectionPointId": "secondary"}, "target": {"symbolInstanceId": "lv_switchgear1", "connectionPointId": "top"}, "type": "cable", "lineType": "polyline", "label": "0.4kV出线1", "waypoints": [], "style": {"strokeColor": "#0000ff", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn8": {"id": "conn8", "source": {"symbolInstanceId": "transformer2", "connectionPointId": "secondary"}, "target": {"symbolInstanceId": "lv_switchgear2", "connectionPointId": "top"}, "type": "cable", "lineType": "polyline", "label": "0.4kV出线2", "waypoints": [], "style": {"strokeColor": "#0000ff", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn9": {"id": "conn9", "source": {"symbolInstanceId": "lv_switchgear1", "connectionPointId": "right"}, "target": {"symbolInstanceId": "lv_tie_switch", "connectionPointId": "in"}, "type": "cable", "lineType": "polyline", "label": "0.4kV母联", "waypoints": [], "style": {"strokeColor": "#0000ff", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": true, "padding": 20}}, "conn10": {"id": "conn10", "source": {"symbolInstanceId": "lv_tie_switch", "connectionPointId": "out"}, "target": {"symbolInstanceId": "lv_switchgear2", "connectionPointId": "left"}, "type": "cable", "lineType": "polyline", "label": "", "waypoints": [], "style": {"strokeColor": "#0000ff", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": true, "padding": 20}}, "conn11": {"id": "conn11", "source": {"symbolInstanceId": "lv_switchgear1", "connectionPointId": "bottom"}, "target": {"symbolInstanceId": "ups1", "connectionPointId": "out"}, "type": "cable", "lineType": "polyline", "label": "UPS1馈线", "waypoints": [], "style": {"strokeColor": "#0000ff", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn12": {"id": "conn12", "source": {"symbolInstanceId": "lv_switchgear1", "connectionPointId": "bottom"}, "target": {"symbolInstanceId": "ups2", "connectionPointId": "out"}, "type": "cable", "lineType": "polyline", "label": "UPS2馈线", "waypoints": [], "style": {"strokeColor": "#0000ff", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn13": {"id": "conn13", "source": {"symbolInstanceId": "lv_switchgear2", "connectionPointId": "bottom"}, "target": {"symbolInstanceId": "ups3", "connectionPointId": "out"}, "type": "cable", "lineType": "polyline", "label": "UPS3馈线", "waypoints": [], "style": {"strokeColor": "#0000ff", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn14": {"id": "conn14", "source": {"symbolInstanceId": "lv_switchgear2", "connectionPointId": "bottom"}, "target": {"symbolInstanceId": "ups4", "connectionPointId": "out"}, "type": "cable", "lineType": "polyline", "label": "UPS4馈线", "waypoints": [], "style": {"strokeColor": "#0000ff", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 20}}, "conn15": {"id": "conn15", "source": {"symbolInstanceId": "ups1", "connectionPointId": "out"}, "target": {"symbolInstanceId": "load_panel1", "connectionPointId": "in"}, "type": "cable", "lineType": "polyline", "label": "", "waypoints": [], "style": {"strokeColor": "#008000", "lineWidth": 1, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 15}}, "conn16": {"id": "conn16", "source": {"symbolInstanceId": "ups2", "connectionPointId": "out"}, "target": {"symbolInstanceId": "load_panel2", "connectionPointId": "in"}, "type": "cable", "lineType": "polyline", "label": "", "waypoints": [], "style": {"strokeColor": "#008000", "lineWidth": 1, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 15}}, "conn17": {"id": "conn17", "source": {"symbolInstanceId": "ups3", "connectionPointId": "out"}, "target": {"symbolInstanceId": "load_panel3", "connectionPointId": "in"}, "type": "cable", "lineType": "polyline", "label": "", "waypoints": [], "style": {"strokeColor": "#008000", "lineWidth": 1, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 15}}, "conn18": {"id": "conn18", "source": {"symbolInstanceId": "ups4", "connectionPointId": "out"}, "target": {"symbolInstanceId": "load_panel4", "connectionPointId": "in"}, "type": "cable", "lineType": "polyline", "label": "", "waypoints": [], "style": {"strokeColor": "#008000", "lineWidth": 1, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": false, "padding": 15}}}, "groups": {}, "selectedSymbolIds": [], "selectedConnectionIds": [], "selectedGroupIds": [], "version": 1}