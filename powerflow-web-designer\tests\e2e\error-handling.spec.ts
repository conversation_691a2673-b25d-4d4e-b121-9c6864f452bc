import { test, expect } from '@playwright/test';
import { 
  navigateToEditor, 
  navigateToViewer, 
  createNewDiagram 
} from './helpers';

test.describe('Error Handling', () => {
  test('should show error message when creating a diagram with empty name', async ({ page }) => {
    await navigateToEditor(page);
    
    // Click the New button in the toolbar
    await page.getByRole('button', { name: 'New' }).click();
    
    // Leave the diagram name empty
    await page.getByLabel('Diagram Name').fill('');
    
    // Click the Create button
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check that an error message is displayed
    await expect(page.locator('.error-message')).toBeVisible();
    await expect(page.locator('.error-message')).toContainText('Diagram name cannot be empty');
  });

  test('should show error message when trying to save without changes', async ({ page }) => {
    await createNewDiagram(page, 'No Changes Test Diagram');
    
    // Try to save without making any changes
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check that an error message is displayed
    await expect(page.locator('.notification')).toBeVisible();
    await expect(page.locator('.notification')).toContainText('No changes to save');
  });

  test('should handle invalid diagram ID in viewer', async ({ page }) => {
    // Navigate to the viewer with an invalid diagram ID
    await page.goto('/viewer/invalid-id');
    
    // Check that an error message is displayed
    await expect(page.locator('.error-message')).toBeVisible();
    await expect(page.locator('.error-message')).toContainText('Diagram not found');
  });

  test('should handle network errors when loading diagrams', async ({ page }) => {
    // Mock a network error
    await page.route('**/api/diagrams', route => route.abort('failed'));
    
    // Navigate to the viewer
    await navigateToViewer(page);
    
    // Check that an error message is displayed
    await expect(page.locator('.error-message')).toBeVisible();
    await expect(page.locator('.error-message')).toContainText('Failed to load diagrams');
  });

  test('should handle network errors when saving diagrams', async ({ page }) => {
    await createNewDiagram(page, 'Network Error Test Diagram');
    
    // Mock a network error for the save endpoint
    await page.route('**/api/diagrams/save', route => route.abort('failed'));
    
    // Try to save the diagram
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check that an error message is displayed
    await expect(page.locator('.error-message')).toBeVisible();
    await expect(page.locator('.error-message')).toContainText('Failed to save diagram');
  });

  test('should recover from errors and continue working', async ({ page }) => {
    await navigateToEditor(page);
    
    // Click the New button in the toolbar
    await page.getByRole('button', { name: 'New' }).click();
    
    // Leave the diagram name empty
    await page.getByLabel('Diagram Name').fill('');
    
    // Click the Create button
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check that an error message is displayed
    await expect(page.locator('.error-message')).toBeVisible();
    
    // Now enter a valid name
    await page.getByLabel('Diagram Name').fill('Recovery Test Diagram');
    
    // Click the Create button again
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check that the diagram is created
    await expect(page.locator('.diagram-canvas')).toBeVisible();
    
    // Verify that we can continue working
    await expect(page.getByRole('button', { name: 'Symbols' })).toBeVisible();
  });
});
