<template>
  <v-group>
    <!-- Flow animation (moving dots along the line) -->
    <template v-if="animationType === 'flow' && isEnabled">
      <v-circle
        v-for="(dot, index) in flowDots"
        :key="`flow-dot-${index}`"
        :config="{
          x: dot.x,
          y: dot.y,
          radius: getFlowDotRadius(),
          fill: getAnimationColor(),
          opacity: dot.opacity,
        }"
      />
    </template>

    <!-- Pulse animation (expanding circles at intervals) -->
    <template v-if="animationType === 'pulse' && isEnabled">
      <v-circle
        v-for="(pulse, index) in pulses"
        :key="`pulse-${index}`"
        :config="{
          x: pulse.x,
          y: pulse.y,
          radius: pulse.radius,
          stroke: getAnimationColor(),
          strokeWidth: 2,
          opacity: pulse.opacity,
        }"
      />
    </template>

    <!-- Dash animation (moving dash pattern) -->
    <template v-if="animationType === 'dash' && isEnabled">
      <v-line
        v-if="connectionType === 'straight' || connectionType === 'polyline' || connectionType === 'smart'"
        :config="{
          points: flattenedPoints,
          stroke: getAnimationColor(),
          strokeWidth: lineWidth,
          lineCap: lineCap,
          lineJoin: lineJoin,
          dash: getDashPattern(),
          dashOffset: dashOffset,
          opacity: 0.7,
        }"
      />
      <v-path
        v-else-if="connectionType === 'bezier'"
        :config="{
          data: bezierPath,
          stroke: getAnimationColor(),
          strokeWidth: lineWidth,
          lineCap: lineCap,
          lineJoin: lineJoin,
          dash: getDashPattern(),
          dashOffset: dashOffset,
          opacity: 0.7,
        }"
      />
    </template>
  </v-group>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import {
  PowerFlowAnimationType,
  PowerFlowDirection,
  PowerFlowAnimation,
  ConnectionLineType,
  LineCapStyle,
  LineJoinStyle
} from '@/types/connection';
import { Position } from '@/types/symbol';

// Props
const props = withDefaults(defineProps<{
  points: Position[];
  flattenedPoints: number[];
  bezierPath: string;
  connectionType: ConnectionLineType;
  animation: PowerFlowAnimation;
  lineWidth?: number;
  lineCap?: LineCapStyle;
  lineJoin?: LineJoinStyle;
  strokeColor: string;
}>(), {
  lineWidth: 1,
  lineCap: LineCapStyle.ROUND,
  lineJoin: LineJoinStyle.ROUND
});

// State
const animationFrame = ref<number | null>(null);
const lastTimestamp = ref<number>(0);
const flowDots = ref<Array<{ x: number; y: number; opacity: number; }>>([]); // Dots for flow animation
const pulses = ref<Array<{ x: number; y: number; radius: number; opacity: number; }>>([]); // Pulses for pulse animation
const dashOffset = ref<number>(0); // Dash offset for dash animation

// Computed
const animationType = computed(() => props.animation.type);
const animationDirection = computed(() => props.animation.direction);
const animationSpeed = computed(() => props.animation.speed);
const isEnabled = computed(() => props.animation.enabled);

// Get animation color (use specified color or fall back to stroke color)
const getAnimationColor = () => {
  return props.animation.color || props.strokeColor;
};

// Get flow dot radius based on line width
const getFlowDotRadius = () => {
  return Math.max(props.lineWidth * 0.8, 2);
};

// Get dash pattern for dash animation
const getDashPattern = () => {
  return [props.lineWidth * 4, props.lineWidth * 4];
};

// Calculate positions along the path
const getPositionAlongPath = (t: number): Position => {
  // t is a value between 0 and 1 representing the position along the path
  if (t < 0) t = 0;
  if (t > 1) t = 1;

  const points = props.points;

  if (points.length < 2) {
    return { x: 0, y: 0 };
  }

  if (props.connectionType === ConnectionLineType.BEZIER) {
    // For bezier curves, use cubic bezier interpolation
    if (points.length === 4) { // Cubic bezier with 2 control points
      const p0 = points[0];
      const p1 = points[1];
      const p2 = points[2];
      const p3 = points[3];

      // Cubic bezier formula
      const x = Math.pow(1 - t, 3) * p0.x +
                3 * Math.pow(1 - t, 2) * t * p1.x +
                3 * (1 - t) * Math.pow(t, 2) * p2.x +
                Math.pow(t, 3) * p3.x;

      const y = Math.pow(1 - t, 3) * p0.y +
                3 * Math.pow(1 - t, 2) * t * p1.y +
                3 * (1 - t) * Math.pow(t, 2) * p2.y +
                Math.pow(t, 3) * p3.y;

      return { x, y };
    } else if (points.length === 3) { // Quadratic bezier with 1 control point
      const p0 = points[0];
      const p1 = points[1];
      const p2 = points[2];

      // Quadratic bezier formula
      const x = Math.pow(1 - t, 2) * p0.x +
                2 * (1 - t) * t * p1.x +
                Math.pow(t, 2) * p2.x;

      const y = Math.pow(1 - t, 2) * p0.y +
                2 * (1 - t) * t * p1.y +
                Math.pow(t, 2) * p2.y;

      return { x, y };
    }
  }

  // For straight, polyline, and smart connections, use linear interpolation between segments
  let totalLength = 0;
  const segmentLengths: number[] = [];

  // Calculate the total length and segment lengths
  for (let i = 0; i < points.length - 1; i++) {
    const dx = points[i + 1].x - points[i].x;
    const dy = points[i + 1].y - points[i].y;
    const length = Math.sqrt(dx * dx + dy * dy);
    segmentLengths.push(length);
    totalLength += length;
  }

  // Find the segment that contains the point at position t
  const targetLength = t * totalLength;
  let currentLength = 0;

  for (let i = 0; i < segmentLengths.length; i++) {
    if (currentLength + segmentLengths[i] >= targetLength) {
      // This segment contains the point
      const segmentT = (targetLength - currentLength) / segmentLengths[i];
      const x = points[i].x + segmentT * (points[i + 1].x - points[i].x);
      const y = points[i].y + segmentT * (points[i + 1].y - points[i].y);
      return { x, y };
    }
    currentLength += segmentLengths[i];
  }

  // Fallback to the last point
  return points[points.length - 1];
};

// Animation loop
const animate = (timestamp: number) => {
  if (!lastTimestamp.value) {
    lastTimestamp.value = timestamp;
  }

  const deltaTime = timestamp - lastTimestamp.value;
  lastTimestamp.value = timestamp;

  // Skip if animation is disabled
  if (!isEnabled.value) {
    animationFrame.value = requestAnimationFrame(animate);
    return;
  }

  // Calculate animation speed factor (1-10 scale)
  const speedFactor = props.animation.speed / 5;

  // Update animation based on type
  if (animationType.value === PowerFlowAnimationType.FLOW) {
    updateFlowAnimation(deltaTime, speedFactor);
  } else if (animationType.value === PowerFlowAnimationType.PULSE) {
    updatePulseAnimation(deltaTime, speedFactor);
  } else if (animationType.value === PowerFlowAnimationType.DASH) {
    updateDashAnimation(deltaTime, speedFactor);
  }

  // Continue animation loop
  animationFrame.value = requestAnimationFrame(animate);
};

// Update flow animation
const updateFlowAnimation = (deltaTime: number, speedFactor: number) => {
  // Create new dots based on direction
  if (animationDirection.value === PowerFlowDirection.FORWARD ||
      animationDirection.value === PowerFlowDirection.BIDIRECTIONAL) {
    if (Math.random() < 0.05 * speedFactor) {
      flowDots.value.push({
        x: props.points[0].x,
        y: props.points[0].y,
        opacity: 0.8,
      });
    }
  }

  if (animationDirection.value === PowerFlowDirection.BACKWARD ||
      animationDirection.value === PowerFlowDirection.BIDIRECTIONAL) {
    if (Math.random() < 0.05 * speedFactor) {
      flowDots.value.push({
        x: props.points[props.points.length - 1].x,
        y: props.points[props.points.length - 1].y,
        opacity: 0.8,
      });
    }
  }

  // Update existing dots
  const updatedDots = [];
  for (const dot of flowDots.value) {
    // Find the closest point on the path
    let closestPoint = { x: dot.x, y: dot.y };
    let minDistance = Infinity;

    for (let t = 0; t <= 1; t += 0.01) {
      const point = getPositionAlongPath(t);
      const dx = point.x - dot.x;
      const dy = point.y - dot.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < minDistance) {
        minDistance = distance;
        closestPoint = point;
      }
    }

    // Move dot along the path
    const moveSpeed = 0.1 * speedFactor * (deltaTime / 16);
    const dx = closestPoint.x - dot.x;
    const dy = closestPoint.y - dot.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance > 0) {
      dot.x += (dx / distance) * moveSpeed;
      dot.y += (dy / distance) * moveSpeed;
    }

    // Fade out dots that reach the end
    const endDistance = Math.min(
      Math.sqrt(Math.pow(dot.x - props.points[0].x, 2) + Math.pow(dot.y - props.points[0].y, 2)),
      Math.sqrt(Math.pow(dot.x - props.points[props.points.length - 1].x, 2) + Math.pow(dot.y - props.points[props.points.length - 1].y, 2))
    );

    if (endDistance < 5) {
      dot.opacity -= 0.05;
    }

    // Keep dots that are still visible
    if (dot.opacity > 0) {
      updatedDots.push(dot);
    }
  }

  // Update the dots
  flowDots.value = updatedDots;
};

// Update pulse animation
const updatePulseAnimation = (deltaTime: number, speedFactor: number) => {
  // Create new pulses based on direction
  if (animationDirection.value === PowerFlowDirection.FORWARD ||
      animationDirection.value === PowerFlowDirection.BIDIRECTIONAL) {
    if (Math.random() < 0.02 * speedFactor) {
      pulses.value.push({
        x: props.points[0].x,
        y: props.points[0].y,
        radius: 2,
        opacity: 0.8,
      });
    }
  }

  if (animationDirection.value === PowerFlowDirection.BACKWARD ||
      animationDirection.value === PowerFlowDirection.BIDIRECTIONAL) {
    if (Math.random() < 0.02 * speedFactor) {
      pulses.value.push({
        x: props.points[props.points.length - 1].x,
        y: props.points[props.points.length - 1].y,
        radius: 2,
        opacity: 0.8,
      });
    }
  }

  // Update existing pulses
  const updatedPulses = [];
  for (const pulse of pulses.value) {
    // Expand pulse
    pulse.radius += 0.5 * speedFactor * (deltaTime / 16);
    pulse.opacity -= 0.01 * speedFactor * (deltaTime / 16);

    // Keep pulses that are still visible
    if (pulse.opacity > 0) {
      updatedPulses.push(pulse);
    }
  }

  // Update the pulses
  pulses.value = updatedPulses;
};

// Update dash animation
const updateDashAnimation = (deltaTime: number, speedFactor: number) => {
  // Update dash offset based on direction
  if (animationDirection.value === PowerFlowDirection.FORWARD) {
    dashOffset.value -= 0.5 * speedFactor * (deltaTime / 16);
  } else if (animationDirection.value === PowerFlowDirection.BACKWARD) {
    dashOffset.value += 0.5 * speedFactor * (deltaTime / 16);
  } else {
    // For bidirectional, alternate direction
    const time = Date.now() / 1000;
    dashOffset.value = Math.sin(time * speedFactor) * 10;
  }
};

// Start animation on mount
onMounted(() => {
  animationFrame.value = requestAnimationFrame(animate);
});

// Clean up on unmount
onUnmounted(() => {
  if (animationFrame.value !== null) {
    cancelAnimationFrame(animationFrame.value);
  }
});

// Watch for changes in animation properties
watch(
  () => props.animation,
  () => {
    // Reset animation state when animation properties change
    flowDots.value = [];
    pulses.value = [];
    dashOffset.value = 0;
  },
  { deep: true }
);
</script>
