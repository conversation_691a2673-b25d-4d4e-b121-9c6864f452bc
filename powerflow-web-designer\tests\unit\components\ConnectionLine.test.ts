import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import ConnectionLine from '../../../powerflow-web-designer/src/components/canvas/ConnectionLine.vue';
import { Connection, ConnectionLineType, ConnectionType } from '../../../powerflow-web-designer/src/types/connection';
import { Position } from '../../../powerflow-web-designer/src/types/symbol';

// Mock the Konva components
vi.mock('vue-konva', () => ({
  VGroup: {
    name: 'VGroup',
    template: '<div><slot /></div>',
    props: ['config']
  },
  VLine: {
    name: 'VLine',
    template: '<div></div>',
    props: ['config']
  },
  VPath: {
    name: 'VPath',
    template: '<div></div>',
    props: ['config']
  },
  VCircle: {
    name: 'VCircle',
    template: '<div></div>',
    props: ['config']
  },
  VText: {
    name: 'VText',
    template: '<div></div>',
    props: ['config']
  }
}));

// Mock the store
vi.mock('@/stores/diagram', () => ({
  useDiagramStore: () => ({
    currentDiagram: {
      grid: {
        snapToGrid: true,
        size: 20
      },
      symbolInstances: {},
      connections: {}
    },
    updateConnection: vi.fn()
  })
}));

// Mock the ConnectionWaypointEditor component
vi.mock('../../../powerflow-web-designer/src/components/canvas/ConnectionWaypointEditor.vue', () => ({
  default: {
    name: 'ConnectionWaypointEditor',
    template: '<div></div>',
    props: ['connection', 'sourcePosition', 'targetPosition', 'isSelected', 'isLocked', 'readOnly']
  }
}));

// Mock the WaypointContextMenu component
vi.mock('../../../powerflow-web-designer/src/components/menus/WaypointContextMenu.vue', () => ({
  default: {
    name: 'WaypointContextMenu',
    template: '<div></div>',
    props: ['connectionId', 'waypointIndex', 'position']
  }
}));

// Mock the utils
vi.mock('@/utils/connectionUtils', () => ({
  detectLineIntersections: vi.fn(),
  addWaypoint: vi.fn(),
  removeWaypoint: vi.fn(),
  calculateBezierControlPoints: vi.fn()
}));

vi.mock('@/utils/pathFinding', () => ({
  findPath: vi.fn(() => [
    { x: 0, y: 0 },
    { x: 50, y: 50 },
    { x: 100, y: 100 }
  ])
}));

vi.mock('@/utils/symbolLibrary', () => ({
  getSymbolDefinition: vi.fn()
}));

describe('ConnectionLine.vue', () => {
  let connection: Connection;
  let sourcePosition: Position;
  let targetPosition: Position;

  beforeEach(() => {
    connection = {
      id: 'test-connection',
      type: ConnectionType.CABLE,
      source: {
        symbolInstanceId: 'source-symbol',
        connectionPointId: 'out'
      },
      target: {
        symbolInstanceId: 'target-symbol',
        connectionPointId: 'in'
      },
      lineType: ConnectionLineType.STRAIGHT,
      style: {
        strokeColor: '#000000',
        lineWidth: 2
      }
    };

    sourcePosition = { x: 0, y: 0 };
    targetPosition = { x: 100, y: 100 };
  });

  it('renders a straight connection line', () => {
    const wrapper = mount(ConnectionLine, {
      props: {
        connection,
        sourcePosition,
        targetPosition,
        isSelected: false,
        isLocked: false,
        readOnly: false
      }
    });

    // Check that the component renders
    expect(wrapper.exists()).toBe(true);

    // Check that it renders a VLine component for straight lines
    expect(wrapper.findComponent({ name: 'VLine' }).exists()).toBe(true);
  });

  it('renders a polyline connection with waypoints', async () => {
    connection.lineType = ConnectionLineType.POLYLINE;
    connection.waypoints = [
      { x: 50, y: 0 },
      { x: 50, y: 100 }
    ];

    const wrapper = mount(ConnectionLine, {
      props: {
        connection,
        sourcePosition,
        targetPosition,
        isSelected: false,
        isLocked: false,
        readOnly: false
      }
    });

    // Check that the component renders
    expect(wrapper.exists()).toBe(true);

    // Check that it renders a VLine component for polylines
    expect(wrapper.findComponent({ name: 'VLine' }).exists()).toBe(true);
  });

  it('renders a bezier connection', async () => {
    connection.lineType = ConnectionLineType.BEZIER;
    connection.controlPoints = [
      { x: 30, y: 30 },
      { x: 70, y: 70 }
    ];

    const wrapper = mount(ConnectionLine, {
      props: {
        connection,
        sourcePosition,
        targetPosition,
        isSelected: false,
        isLocked: false,
        readOnly: false
      }
    });

    // Check that the component renders
    expect(wrapper.exists()).toBe(true);

    // Check that it renders a VPath component for bezier curves
    expect(wrapper.findComponent({ name: 'VPath' }).exists()).toBe(true);
  });

  it('renders a smart connection', async () => {
    connection.lineType = ConnectionLineType.SMART;

    const wrapper = mount(ConnectionLine, {
      props: {
        connection,
        sourcePosition,
        targetPosition,
        isSelected: false,
        isLocked: false,
        readOnly: false
      }
    });

    // Check that the component renders
    expect(wrapper.exists()).toBe(true);

    // Check that it renders a VLine component for smart connections
    expect(wrapper.findComponent({ name: 'VLine' }).exists()).toBe(true);
  });

  it('shows the waypoint editor when selected', async () => {
    const wrapper = mount(ConnectionLine, {
      props: {
        connection,
        sourcePosition,
        targetPosition,
        isSelected: true,
        isLocked: false,
        readOnly: false
      }
    });

    // Check that the waypoint editor is rendered
    expect(wrapper.findComponent({ name: 'ConnectionWaypointEditor' }).exists()).toBe(true);
  });

  it('emits select event when clicked', async () => {
    const wrapper = mount(ConnectionLine, {
      props: {
        connection,
        sourcePosition,
        targetPosition,
        isSelected: false,
        isLocked: false,
        readOnly: false
      }
    });

    // Simulate a click on the connection
    await wrapper.trigger('click');

    // Check that the select event is emitted
    expect(wrapper.emitted('select')).toBeTruthy();
    expect(wrapper.emitted('select')![0]).toEqual(['test-connection']);
  });

  it('emits deselect event when clicked while selected', async () => {
    const wrapper = mount(ConnectionLine, {
      props: {
        connection,
        sourcePosition,
        targetPosition,
        isSelected: true,
        isLocked: false,
        readOnly: false
      }
    });

    // Simulate a click on the connection
    await wrapper.trigger('click');

    // Check that the deselect event is emitted
    expect(wrapper.emitted('deselect')).toBeTruthy();
    expect(wrapper.emitted('deselect')![0]).toEqual(['test-connection']);
  });
});
