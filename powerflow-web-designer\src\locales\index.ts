import { ref, reactive, provide, inject } from 'vue';
import zh<PERSON><PERSON> from './zh_CN';

// Default locale is Chinese
const defaultLocale = 'zh_CN';

// Available locales
const locales = {
  zh_CN: zhCN,
};

// Create a reactive locale state
const currentLocale = ref(defaultLocale);
const messages = reactive(locales);

// Create a composable for the locale
export function useLocale() {
  const t = (key: string): string => {
    const keys = key.split('.');
    let result = messages[currentLocale.value];

    for (const k of keys) {
      if (result && result[k]) {
        result = result[k];
      } else {
        // If translation not found, return the key
        return key;
      }
    }

    return result;
  };

  const setLocale = (locale: string) => {
    if (locales[locale]) {
      currentLocale.value = locale;
    }
  };

  return {
    t,
    setLocale,
    currentLocale,
  };
}

// Create a provider for the locale
export function provideLocale() {
  const locale = useLocale();
  return locale;
}

// Create a consumer for the locale
export function useI18n() {
  return inject('locale') as ReturnType<typeof useLocale>;
}
