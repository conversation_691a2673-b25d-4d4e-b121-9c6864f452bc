<template>
  <div class="layer-panel">
    <div class="panel-header">
      <h3>图层</h3>
      <div class="panel-actions">
        <a-button
          type="primary"
          size="small"
          @click="handleAddLayer"
          :disabled="readOnly"
        >
          <template #icon><plus-outlined /></template>
          添加图层
        </a-button>
      </div>
    </div>
    <div class="panel-content">
      <a-list
        class="layer-list"
        :data-source="layers"
        :locale="{ emptyText: 'No layers' }"
      >
        <template #renderItem="{ item, index }">
          <a-list-item :class="{ 'active-layer': isActiveLayer(item.id) }">
            <div class="layer-item">
              <div class="layer-controls">
                <a-checkbox
                  :checked="item.visible"
                  @change="(e) => handleVisibilityChange(item.id, e.target.checked)"
                  :disabled="readOnly"
                />
                <a-button
                  type="text"
                  size="small"
                  :disabled="item.locked || readOnly"
                  @click="() => handleLockToggle(item.id)"
                >
                  <template #icon>
                    <lock-outlined v-if="item.locked" />
                    <unlock-outlined v-else />
                  </template>
                </a-button>
              </div>
              <div
                class="layer-name"
                @click="() => handleLayerSelect(item.id)"
              >
                <a-typography-paragraph
                  v-if="editingLayerId !== item.id"
                  :content="item.name"
                  :ellipsis="{ rows: 1 }"
                  :editable="
                    !readOnly && {
                      onChange: (value) => handleRenameLayer(item.id, value),
                      enterIcon: null,
                    }
                  "
                />
              </div>
              <div class="layer-actions">
                <a-dropdown :disabled="readOnly">
                  <template #overlay>
                    <a-menu>
                      <a-menu-item key="rename" @click="() => handleStartRename(item.id)">
                        <edit-outlined /> Rename
                      </a-menu-item>
                      <a-menu-item key="duplicate" @click="() => handleDuplicateLayer(item.id)">
                        <copy-outlined /> Duplicate
                      </a-menu-item>
                      <a-menu-item
                        key="delete"
                        @click="() => handleDeleteLayer(item.id)"
                        :disabled="layers.length <= 1"
                      >
                        <delete-outlined /> Delete
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item
                        key="move-up"
                        @click="() => handleMoveLayer(index, index - 1)"
                        :disabled="index === 0"
                      >
                        <arrow-up-outlined /> Move Up
                      </a-menu-item>
                      <a-menu-item
                        key="move-down"
                        @click="() => handleMoveLayer(index, index + 1)"
                        :disabled="index === layers.length - 1"
                      >
                        <arrow-down-outlined /> Move Down
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="text" size="small">
                    <template #icon><more-outlined /></template>
                  </a-button>
                </a-dropdown>
              </div>
            </div>
          </a-list-item>
        </template>
      </a-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { DiagramLayer } from '@/types/diagram';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  LockOutlined,
  UnlockOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MoreOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

// Props
const props = defineProps<{
  readOnly?: boolean;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const editingLayerId = ref<string | null>(null);
const activeLayerId = ref<string | null>(null);

// Computed
const layers = computed<DiagramLayer[]>(() => {
  if (!diagramStore.currentDiagram) return [];
  return [...diagramStore.currentDiagram.layers].reverse(); // Show top layers first
});

// Methods
const isActiveLayer = (layerId: string) => {
  return activeLayerId.value === layerId;
};

const handleLayerSelect = (layerId: string) => {
  activeLayerId.value = layerId;
  // You could also update the current active layer in the store if needed
};

const handleAddLayer = () => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Generate a new layer ID
  const layerId = `layer-${Date.now()}`;

  // Create a new layer
  const newLayer: DiagramLayer = {
    id: layerId,
    name: `Layer ${diagramStore.currentDiagram.layers.length + 1}`,
    visible: true,
    locked: false,
    symbolInstanceIds: [],
    connectionIds: [],
  };

  // Add the layer to the diagram
  diagramStore.currentDiagram.layers.push(newLayer);

  // Set as active layer
  activeLayerId.value = layerId;

  // Mark as modified
  diagramStore.modified = true;

  message.success('Layer added');
};

const handleDeleteLayer = (layerId: string) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if this is the only layer
  if (diagramStore.currentDiagram.layers.length <= 1) {
    message.error('Cannot delete the only layer');
    return;
  }

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Find the layer index
  const layerIndex = diagramStore.currentDiagram.layers.findIndex(
    layer => layer.id === layerId
  );

  if (layerIndex === -1) return;

  // Get the layer
  const layer = diagramStore.currentDiagram.layers[layerIndex];

  // Move all symbols and connections to the first layer
  const firstLayerId = diagramStore.currentDiagram.layers[0].id;
  if (firstLayerId !== layerId) {
    const firstLayer = diagramStore.currentDiagram.layers[0];
    firstLayer.symbolInstanceIds.push(...layer.symbolInstanceIds);
    firstLayer.connectionIds.push(...layer.connectionIds);
  } else {
    // If deleting the first layer, move to the second layer
    const secondLayer = diagramStore.currentDiagram.layers[1];
    secondLayer.symbolInstanceIds.push(...layer.symbolInstanceIds);
    secondLayer.connectionIds.push(...layer.connectionIds);
  }

  // Remove the layer
  diagramStore.currentDiagram.layers.splice(layerIndex, 1);

  // Update active layer if needed
  if (activeLayerId.value === layerId) {
    activeLayerId.value = diagramStore.currentDiagram.layers[0].id;
  }

  // Mark as modified
  diagramStore.modified = true;

  message.success('Layer deleted');
};

const handleRenameLayer = (layerId: string, newName: string) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Find the layer
  const layer = diagramStore.currentDiagram.layers.find(
    layer => layer.id === layerId
  );

  if (!layer) return;

  // Update the name
  layer.name = newName;

  // Mark as modified
  diagramStore.modified = true;

  // Clear editing state
  editingLayerId.value = null;
};

const handleStartRename = (layerId: string) => {
  editingLayerId.value = layerId;
};

const handleVisibilityChange = (layerId: string, visible: boolean) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Find the layer
  const layer = diagramStore.currentDiagram.layers.find(
    layer => layer.id === layerId
  );

  if (!layer) return;

  // Update visibility
  layer.visible = visible;

  // Mark as modified
  diagramStore.modified = true;
};

const handleLockToggle = (layerId: string) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Find the layer
  const layer = diagramStore.currentDiagram.layers.find(
    layer => layer.id === layerId
  );

  if (!layer) return;

  // Toggle lock state
  layer.locked = !layer.locked;

  // Mark as modified
  diagramStore.modified = true;
};

const handleDuplicateLayer = (layerId: string) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Find the layer
  const layer = diagramStore.currentDiagram.layers.find(
    layer => layer.id === layerId
  );

  if (!layer) return;

  // Create a new layer ID
  const newLayerId = `layer-${Date.now()}`;

  // Create a duplicate layer
  const newLayer: DiagramLayer = {
    id: newLayerId,
    name: `${layer.name} (Copy)`,
    visible: layer.visible,
    locked: layer.locked,
    symbolInstanceIds: [...layer.symbolInstanceIds],
    connectionIds: [...layer.connectionIds],
  };

  // Add the layer to the diagram
  diagramStore.currentDiagram.layers.push(newLayer);

  // Set as active layer
  activeLayerId.value = newLayerId;

  // Mark as modified
  diagramStore.modified = true;

  message.success('Layer duplicated');
};

const handleMoveLayer = (fromIndex: number, toIndex: number) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the indices are valid
  if (
    fromIndex < 0 ||
    fromIndex >= diagramStore.currentDiagram.layers.length ||
    toIndex < 0 ||
    toIndex >= diagramStore.currentDiagram.layers.length
  ) {
    return;
  }

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Get the actual indices (since we're displaying in reverse order)
  const actualFromIndex = diagramStore.currentDiagram.layers.length - 1 - fromIndex;
  const actualToIndex = diagramStore.currentDiagram.layers.length - 1 - toIndex;

  // Move the layer
  const layers = diagramStore.currentDiagram.layers;
  const [movedLayer] = layers.splice(actualFromIndex, 1);
  layers.splice(actualToIndex, 0, movedLayer);

  // Mark as modified
  diagramStore.modified = true;
};
</script>

<style scoped>
.layer-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  border-left: 1px solid #e8e8e8;
}

.panel-header {
  padding: 5px 8px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f5f5;
}

.panel-header h3 {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.layer-list {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
}

.layer-item {
  display: flex;
  align-items: center;
  width: 100%;
}

.layer-controls {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.layer-name {
  flex: 1;
  cursor: pointer;
  padding: 4px 0;
}

.layer-actions {
  margin-left: 8px;
}

.active-layer {
  background-color: #e6f7ff;
}

.rotate-90 {
  transform: rotate(90deg);
}
</style>
