// Chinese (Simplified) translations for PowerFlow Web Designer
export default {
  // Common
  app: {
    name: '电力流程设计器',
  },

  // Views
  views: {
    editor: {
      title: '电力流程编辑器',
    },
    viewer: {
      title: '电力流程查看器',
    },
    home: {
      title: '首页',
    },
    newDiagram: '新建图表',
  },

  // Toolbar
  toolbar: {
    new: '新建图表',
    save: '保存图表',
    export: '导出图表',
    undo: '撤销',
    redo: '重做',
    cut: '剪切',
    copy: '复制',
    paste: '粘贴',
    delete: '删除',
    zoomIn: '放大',
    zoomOut: '缩小',
    fitContent: '适应内容',
    resetView: '重置视图',
    viewMode: '查看模式',
    home: '首页',
    edit: '编辑',
    showGrid: '显示网格',
    hideGrid: '隐藏网格',
    enableSnapToGrid: '启用网格吸附',
    disableSnapToGrid: '禁用网格吸附',
  },

  // Context Menu
  contextMenu: {
    edit: '编辑属性',
    copy: '复制',
    cut: '剪切',
    paste: '粘贴',
    delete: '删除',
    group: '组合',
    ungroup: '取消组合',
    bringToFront: '置于顶层',
    sendToBack: '置于底层',
    align: '对齐',
    alignLeft: '左对齐',
    alignCenter: '居中对齐',
    alignRight: '右对齐',
    alignTop: '顶部对齐',
    alignMiddle: '中部对齐',
    alignBottom: '底部对齐',
    distribute: '分布',
    distributeHorizontally: '水平分布',
    distributeVertically: '垂直分布',
    lineType: '线型',
    straight: '直线',
    polyline: '折线',
    bezier: '贝塞尔曲线',
    selectAll: '全选',
    fitContent: '适应内容',
    resetView: '重置视图',
  },

  // Alignment Toolbar
  alignmentToolbar: {
    align: '对齐',
    alignLeft: '左对齐',
    alignCenter: '居中对齐',
    alignRight: '右对齐',
    alignTop: '顶部对齐',
    alignMiddle: '中部对齐',
    alignBottom: '底部对齐',
    distribute: '分布',
    distributeHorizontally: '水平分布',
    distributeVertically: '垂直分布',
    showGuides: '显示对齐参考线',
  },

  // Zoom Controls
  zoomControls: {
    zoomIn: '放大 (Ctrl++)',
    zoomOut: '缩小 (Ctrl+-)',
    zoomLevel: '缩放级别',
    fitContent: '适应内容',
    resetView: '重置视图',
    panUp: '向上平移',
    panDown: '向下平移',
    panLeft: '向左平移',
    panRight: '向右平移',
    fullScreen: '全屏',
    exitFullScreen: '退出全屏',
  },

  // Search
  search: {
    placeholder: '在图表中搜索',
    results: '个结果',
    symbol: '符号',
    connection: '连接',
  },

  // Export
  export: {
    title: '导出选项',
    exportAs: '导出为',
    png: '导出为 PNG',
    json: '导出为 JSON',
    svg: '导出为 SVG',
    fileName: '文件名',
    scale: '缩放',
    quality: '质量',
    low: '低',
    medium: '中',
    high: '高',
    backgroundColor: '背景颜色',
    includeGrid: '包含网格',
  },

  // Simulation Controls
  simulation: {
    start: '开始模拟',
    pause: '暂停模拟',
    stop: '停止模拟',
    step: '单步前进',
    speed: '模拟速度',
    time: '模拟时间',
    settings: '模拟设置',
    mode: '模拟模式',
    steadyState: '稳态',
    transient: '瞬态',
    fault: '故障分析',
    timeStep: '时间步长 (毫秒)',
    maxTime: '最大时间 (秒)',
    autoStop: '自动停止',
    showAnimation: '显示动画',
    reset: '重置模拟',
    exportResults: '导出结果',
    configure: '配置模拟',
    status: '状态',
    running: '运行中',
    paused: '已暂停',
    notRunning: '未运行',
  },

  // Keyboard Shortcuts
  keyboardShortcuts: {
    title: '键盘快捷键',
    action: '操作',
    shortcut: '快捷键',
  },

  // Waypoint Context Menu
  waypointContextMenu: {
    removeWaypoint: '删除路径点',
    addWaypointBefore: '在前面添加路径点',
    addWaypointAfter: '在后面添加路径点',
    smoothCorner: '平滑拐角',
  },

  // Connection Labels Panel
  connectionLabels: {
    title: '连接标签',
    addLabel: '添加标签',
    editLabel: '编辑标签',
    noLabels: '无标签',
    position: '位置',
    start: '起点',
    middle: '中点',
    end: '终点',
    custom: '自定义',
  },

  // Connection Label Editor
  connectionLabelEditor: {
    addLabel: '添加标签',
    editLabel: '编辑标签',
    labelText: '标签文本',
    position: '位置',
    positionPercentage: '位置百分比 (0-100)',
    alignment: '对齐方式',
    above: '上方',
    center: '居中',
    below: '下方',
    labelStyle: '标签样式',
    fontFamily: '字体',
    fontSize: '字号',
    fontWeight: '字重',
    fontStyle: '字体样式',
    textColor: '文本颜色',
    backgroundColor: '背景颜色',
    borderColor: '边框颜色',
    borderWidth: '边框宽度',
    borderRadius: '边框圆角',
  },

  // Panels and Tabs
  panels: {
    properties: '属性',
    layers: '图层',
    grid: '网格',
    symbolLibrary: '符号库',
    noElementSelected: '未选择元素',
    searchSymbols: '搜索符号',
  },

  // Property Panel
  propertyPanel: {
    // Common
    id: '标识符',
    name: '名称',
    type: '类型',
    position: '位置',
    positionX: 'X 坐标',
    positionY: 'Y 坐标',
    rotation: '旋转角度',
    scale: '缩放比例',
    width: '宽度',
    height: '高度',
    locked: '锁定',

    // Symbol Properties
    symbolProperties: '符号属性',
    style: '样式',
    fillColor: '填充颜色',
    strokeColor: '边框颜色',
    lineWidth: '线宽',
    bindings: '绑定',
    valueDisplays: '值显示',
    trendCharts: '趋势图',

    // Group Properties
    groupProperties: '组属性',
    symbols: '符号',

    // Connection Properties
    connectionProperties: '连接属性',
    connectionType: '连接类型',
    label: '标签',
    cable: '电缆',
    busbar: '母线',
    control: '控制',
    signal: '信号',

    // Connection Types
    straight: '直线',
    polyline: '折线',
    bezier: '贝塞尔曲线',

    // Line Styles
    lineStyle: '线型',
    lineColor: '线条颜色',
    lineThickness: '线条粗细',
    lineDash: '线条样式',
    solid: '实线',
    dashed: '虚线',
    dotted: '点线',

    // Arrows
    startArrow: '起点箭头',
    endArrow: '终点箭头',
    none: '无',
    arrow: '箭头',
    circle: '圆形',
    diamond: '菱形',
  },

  // Symbol Categories
  symbolCategories: {
    datacenter: '数据中心',
    switchgear: '开关设备',
    transformers: '变压器',
    busbars: '母线',
    generators: '发电机',
    loads: '负载',
    capacitors: '电容器',
    reactors: '电抗器',
    measurements: '测量设备',
    protection: '保护设备',
  },

  // Symbol Types
  symbolTypes: {
    // 开关设备
    circuitBreaker: '断路器',
    disconnectorSwitch: '隔离开关',
    earthSwitch: '接地开关',
    fuse: '熔断器',
    loadBreakSwitch: '负荷开关',

    // 变压器
    transformer: '变压器',
    autotransformer: '自耦变压器',
    threeWindingTransformer: '三绕组变压器',

    // 母线
    busbar: '母线',
    busCoupler: '母联',

    // 发电机
    generator: '发电机',
    synchronousGenerator: '同步发电机',
    asynchronousGenerator: '异步发电机',
    windGenerator: '风力发电机',
    solarPanel: '太阳能电池板',

    // 负载
    load: '负载',
    industrialLoad: '工业负载',
    residentialLoad: '民用负载',

    // 电容器
    capacitor: '电容器',
    capacitorBank: '电容器组',

    // 电抗器
    reactor: '电抗器',
    shuntReactor: '并联电抗器',
    seriesReactor: '串联电抗器',

    // 测量设备
    currentTransformer: '电流互感器',
    voltageTransformer: '电压互感器',
    powerMeter: '功率计',

    // 保护设备
    relay: '继电器',
    overCurrentRelay: '过流继电器',
    distanceRelay: '距离继电器',
    differentialRelay: '差动继电器',
  },

  // Mini Map
  miniMap: {
    title: '缩略图',
    show: '显示缩略图',
    hide: '隐藏缩略图',
  },
}
