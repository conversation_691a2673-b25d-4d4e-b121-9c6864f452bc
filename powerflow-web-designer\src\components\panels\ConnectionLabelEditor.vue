<template>
  <div class="connection-label-editor">
    <div class="editor-header">
      <h3>{{ isNewLabel ? 'Add Label' : 'Edit Label' }}</h3>
      <a-button type="text" @click="$emit('close')">
        <template #icon><close-outlined /></template>
      </a-button>
    </div>
    
    <div class="editor-content">
      <!-- Label Text -->
      <div class="form-group">
        <label>Label Text</label>
        <a-input v-model:value="labelText" placeholder="Enter label text" />
      </div>
      
      <!-- Label Position -->
      <div class="form-group">
        <label>Position</label>
        <a-select v-model:value="labelPosition" style="width: 100%">
          <a-select-option :value="LabelPosition.START">Start</a-select-option>
          <a-select-option :value="LabelPosition.MIDDLE">Middle</a-select-option>
          <a-select-option :value="LabelPosition.END">End</a-select-option>
          <a-select-option :value="LabelPosition.CUSTOM">Custom</a-select-option>
        </a-select>
      </div>
      
      <!-- Custom Position Percentage (only if custom position is selected) -->
      <div class="form-group" v-if="labelPosition === LabelPosition.CUSTOM">
        <label>Position Percentage (0-100)</label>
        <a-slider v-model:value="positionPercentage" :min="0" :max="100" />
      </div>
      
      <!-- Label Alignment -->
      <div class="form-group">
        <label>Alignment</label>
        <a-select v-model:value="labelAlignment" style="width: 100%">
          <a-select-option :value="LabelAlignment.ABOVE">Above</a-select-option>
          <a-select-option :value="LabelAlignment.CENTER">Center</a-select-option>
          <a-select-option :value="LabelAlignment.BELOW">Below</a-select-option>
        </a-select>
      </div>
      
      <!-- Label Style -->
      <a-collapse>
        <a-collapse-panel key="1" header="Label Style">
          <!-- Font Family -->
          <div class="form-group">
            <label>Font Family</label>
            <a-select v-model:value="labelStyle.fontFamily" style="width: 100%">
              <a-select-option value="Arial">Arial</a-select-option>
              <a-select-option value="Helvetica">Helvetica</a-select-option>
              <a-select-option value="Times New Roman">Times New Roman</a-select-option>
              <a-select-option value="Courier New">Courier New</a-select-option>
              <a-select-option value="Verdana">Verdana</a-select-option>
            </a-select>
          </div>
          
          <!-- Font Size -->
          <div class="form-group">
            <label>Font Size</label>
            <a-input-number v-model:value="labelStyle.fontSize" :min="8" :max="24" style="width: 100%" />
          </div>
          
          <!-- Font Weight -->
          <div class="form-group">
            <label>Font Weight</label>
            <a-select v-model:value="labelStyle.fontWeight" style="width: 100%">
              <a-select-option value="normal">Normal</a-select-option>
              <a-select-option value="bold">Bold</a-select-option>
            </a-select>
          </div>
          
          <!-- Font Style -->
          <div class="form-group">
            <label>Font Style</label>
            <a-select v-model:value="labelStyle.fontStyle" style="width: 100%">
              <a-select-option value="normal">Normal</a-select-option>
              <a-select-option value="italic">Italic</a-select-option>
            </a-select>
          </div>
          
          <!-- Text Color -->
          <div class="form-group">
            <label>Text Color</label>
            <a-input v-model:value="labelStyle.textColor" placeholder="#000000">
              <template #prefix>
                <div class="color-preview" :style="{ backgroundColor: labelStyle.textColor }"></div>
              </template>
            </a-input>
          </div>
          
          <!-- Background Color -->
          <div class="form-group">
            <label>Background Color</label>
            <a-input v-model:value="labelStyle.backgroundColor" placeholder="#ffffff">
              <template #prefix>
                <div class="color-preview" :style="{ backgroundColor: labelStyle.backgroundColor }"></div>
              </template>
            </a-input>
          </div>
          
          <!-- Border Color -->
          <div class="form-group">
            <label>Border Color</label>
            <a-input v-model:value="labelStyle.borderColor" placeholder="#cccccc">
              <template #prefix>
                <div class="color-preview" :style="{ backgroundColor: labelStyle.borderColor }"></div>
              </template>
            </a-input>
          </div>
          
          <!-- Border Width -->
          <div class="form-group">
            <label>Border Width</label>
            <a-input-number v-model:value="labelStyle.borderWidth" :min="0" :max="5" style="width: 100%" />
          </div>
          
          <!-- Border Radius -->
          <div class="form-group">
            <label>Border Radius</label>
            <a-input-number v-model:value="labelStyle.borderRadius" :min="0" :max="10" style="width: 100%" />
          </div>
          
          <!-- Padding -->
          <div class="form-group">
            <label>Padding</label>
            <a-input-number v-model:value="labelStyle.padding" :min="0" :max="10" style="width: 100%" />
          </div>
          
          <!-- Rotation -->
          <div class="form-group">
            <label>Rotation (degrees)</label>
            <a-input-number v-model:value="labelStyle.rotation" :min="-180" :max="180" style="width: 100%" />
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
    
    <div class="editor-footer">
      <a-button @click="$emit('close')">Cancel</a-button>
      <a-button type="primary" @click="saveLabel">Save</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { ConnectionLabel, LabelPosition, LabelAlignment, defaultLabelStyle } from '@/types/connection';
import { v4 as uuidv4 } from 'uuid';

// Props
const props = defineProps<{
  label?: ConnectionLabel;
  connectionId: string;
}>();

// Emits
const emit = defineEmits<{
  (e: 'save', label: ConnectionLabel): void;
  (e: 'close'): void;
}>();

// Computed
const isNewLabel = computed(() => !props.label);

// State
const labelText = ref(props.label?.text || '');
const labelPosition = ref(props.label?.position || LabelPosition.MIDDLE);
const labelAlignment = ref(props.label?.alignment || LabelAlignment.ABOVE);
const positionPercentage = ref(props.label?.positionPercentage || 50);
const labelStyle = ref({
  fontFamily: props.label?.style?.fontFamily || defaultLabelStyle.fontFamily,
  fontSize: props.label?.style?.fontSize || defaultLabelStyle.fontSize,
  fontWeight: props.label?.style?.fontWeight || defaultLabelStyle.fontWeight,
  fontStyle: props.label?.style?.fontStyle || defaultLabelStyle.fontStyle,
  textColor: props.label?.style?.textColor || defaultLabelStyle.textColor,
  backgroundColor: props.label?.style?.backgroundColor || defaultLabelStyle.backgroundColor,
  borderColor: props.label?.style?.borderColor || defaultLabelStyle.borderColor,
  borderWidth: props.label?.style?.borderWidth || defaultLabelStyle.borderWidth,
  borderRadius: props.label?.style?.borderRadius || defaultLabelStyle.borderRadius,
  padding: props.label?.style?.padding || defaultLabelStyle.padding,
  rotation: props.label?.style?.rotation || 0,
});

// Methods
const saveLabel = () => {
  const label: ConnectionLabel = {
    id: props.label?.id || `${props.connectionId}-label-${uuidv4()}`,
    text: labelText.value,
    position: labelPosition.value,
    alignment: labelAlignment.value,
    positionPercentage: labelPosition.value === LabelPosition.CUSTOM ? positionPercentage.value : undefined,
    offset: props.label?.offset,
    style: {
      fontFamily: labelStyle.value.fontFamily,
      fontSize: labelStyle.value.fontSize,
      fontWeight: labelStyle.value.fontWeight,
      fontStyle: labelStyle.value.fontStyle,
      textColor: labelStyle.value.textColor,
      backgroundColor: labelStyle.value.backgroundColor,
      borderColor: labelStyle.value.borderColor,
      borderWidth: labelStyle.value.borderWidth,
      borderRadius: labelStyle.value.borderRadius,
      padding: labelStyle.value.padding,
      rotation: labelStyle.value.rotation,
    },
  };
  
  emit('save', label);
};
</script>

<style scoped>
.connection-label-editor {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.editor-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}

.color-preview {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
}
</style>
