import { test, expect } from '@playwright/test';
import { 
  navigateToEditor, 
  createNewDiagram, 
  addSymbolToDiagram 
} from './helpers';

test.describe('Layer Management', () => {
  test('should display the layers panel', async ({ page }) => {
    await createNewDiagram(page, 'Layers Test Diagram');
    
    // Open the layers panel
    await page.getByRole('button', { name: 'Layers' }).click();
    
    // Check that the layers panel is visible
    await expect(page.locator('.layers-panel')).toBeVisible();
    
    // Check that the default layer is listed
    await expect(page.locator('.layers-panel')).toContainText('Default Layer');
  });

  test('should create a new layer', async ({ page }) => {
    await createNewDiagram(page, 'New Layer Test Diagram');
    
    // Open the layers panel
    await page.getByRole('button', { name: 'Layers' }).click();
    
    // Count initial layers
    const initialLayerCount = await page.locator('.layer-item').count();
    
    // Click the "Add Layer" button
    await page.locator('.layers-panel').getByRole('button', { name: 'Add Layer' }).click();
    
    // Enter the layer name
    await page.getByLabel('Layer Name').fill('Test Layer');
    
    // Click the Create button
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check that a new layer was added
    await expect(page.locator('.layer-item')).toHaveCount(initialLayerCount + 1);
    await expect(page.locator('.layers-panel')).toContainText('Test Layer');
  });

  test('should add a symbol to a specific layer', async ({ page }) => {
    await createNewDiagram(page, 'Symbol Layer Test Diagram');
    
    // Open the layers panel
    await page.getByRole('button', { name: 'Layers' }).click();
    
    // Create a new layer
    await page.locator('.layers-panel').getByRole('button', { name: 'Add Layer' }).click();
    await page.getByLabel('Layer Name').fill('Symbol Layer');
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Select the new layer
    await page.locator('.layer-item').filter({ hasText: 'Symbol Layer' }).click();
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Check that the symbol is on the correct layer
    const symbolLayer = await page.evaluate(() => {
      const symbol = document.querySelector('.symbol-instance');
      return symbol ? symbol.getAttribute('data-layer') : null;
    });
    
    expect(symbolLayer).toBe('Symbol Layer');
  });

  test('should toggle layer visibility', async ({ page }) => {
    await createNewDiagram(page, 'Layer Visibility Test Diagram');
    
    // Open the layers panel
    await page.getByRole('button', { name: 'Layers' }).click();
    
    // Create a new layer
    await page.locator('.layers-panel').getByRole('button', { name: 'Add Layer' }).click();
    await page.getByLabel('Layer Name').fill('Visibility Layer');
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Select the new layer
    await page.locator('.layer-item').filter({ hasText: 'Visibility Layer' }).click();
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Count visible symbols
    const initialVisibleSymbols = await page.locator('.symbol-instance:visible').count();
    
    // Toggle the layer visibility
    await page.locator('.layer-item').filter({ hasText: 'Visibility Layer' }).getByRole('button', { name: 'Toggle Visibility' }).click();
    
    // Check that the symbol is hidden
    await expect(page.locator('.symbol-instance:visible')).toHaveCount(initialVisibleSymbols - 1);
    
    // Toggle the layer visibility back
    await page.locator('.layer-item').filter({ hasText: 'Visibility Layer' }).getByRole('button', { name: 'Toggle Visibility' }).click();
    
    // Check that the symbol is visible again
    await expect(page.locator('.symbol-instance:visible')).toHaveCount(initialVisibleSymbols);
  });

  test('should delete a layer', async ({ page }) => {
    await createNewDiagram(page, 'Delete Layer Test Diagram');
    
    // Open the layers panel
    await page.getByRole('button', { name: 'Layers' }).click();
    
    // Create a new layer
    await page.locator('.layers-panel').getByRole('button', { name: 'Add Layer' }).click();
    await page.getByLabel('Layer Name').fill('Delete Layer');
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Count layers
    const layerCount = await page.locator('.layer-item').count();
    
    // Delete the layer
    await page.locator('.layer-item').filter({ hasText: 'Delete Layer' }).getByRole('button', { name: 'Delete Layer' }).click();
    
    // Confirm deletion
    await page.getByRole('button', { name: 'Confirm' }).click();
    
    // Check that the layer was deleted
    await expect(page.locator('.layer-item')).toHaveCount(layerCount - 1);
    await expect(page.locator('.layers-panel')).not.toContainText('Delete Layer');
  });
});
