import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import MultiSelectionToolbar from '@/components/toolbar/MultiSelectionToolbar.vue';
import Diagram<PERSON>enderer from '@/components/canvas/DiagramRenderer.vue';
import { useDiagramStore } from '@/stores/diagram';

// Mock Ant Design Vue components
vi.mock('ant-design-vue', () => ({
  Button: { name: 'AButton', template: '<button><slot /></button>' },
  ButtonGroup: { name: 'AButtonGroup', template: '<div><slot /></div>' },
  Tooltip: { name: 'ATooltip', template: '<div><slot /></div>' },
  Dropdown: { name: 'ADropdown', template: '<div><slot /></div>' },
  Menu: { name: 'AMenu', template: '<div><slot /></div>' },
  MenuItem: { name: 'AMenuItem', template: '<div><slot /></div>' },
  message: {
    success: vi.fn(),
    info: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock Vue Konva components
vi.mock('vue-konva', () => ({
  Stage: { name: 'VStage', template: '<div><slot /></div>' },
  Layer: { name: 'VLayer', template: '<div><slot /></div>' },
  Rect: { name: 'VRect', template: '<div></div>' },
  Circle: { name: 'VCircle', template: '<div></div>' },
  Line: { name: 'VLine', template: '<div></div>' },
  Group: { name: 'VGroup', template: '<div><slot /></div>' },
  Transformer: { name: 'VTransformer', template: '<div></div>' },
}));

// Mock icons
vi.mock('@ant-design/icons-vue', () => ({
  CloseOutlined: { name: 'CloseOutlined', template: '<span>×</span>' },
  SelectOutlined: { name: 'SelectOutlined', template: '<span>⬚</span>' },
  SwapOutlined: { name: 'SwapOutlined', template: '<span>⇄</span>' },
  CopyOutlined: { name: 'CopyOutlined', template: '<span>📋</span>' },
  ScissorOutlined: { name: 'ScissorOutlined', template: '<span>✂</span>' },
  DeleteOutlined: { name: 'DeleteOutlined', template: '<span>🗑</span>' },
  VerticalAlignTopOutlined: { name: 'VerticalAlignTopOutlined', template: '<span>⬆</span>' },
  VerticalAlignBottomOutlined: { name: 'VerticalAlignBottomOutlined', template: '<span>⬇</span>' },
  GroupOutlined: { name: 'GroupOutlined', template: '<span>📦</span>' },
  UngroupOutlined: { name: 'UngroupOutlined', template: '<span>📤</span>' },
  AlignLeftOutlined: { name: 'AlignLeftOutlined', template: '<span>⬅</span>' },
  AlignCenterOutlined: { name: 'AlignCenterOutlined', template: '<span>⬌</span>' },
  AlignRightOutlined: { name: 'AlignRightOutlined', template: '<span>➡</span>' },
  ColumnWidthOutlined: { name: 'ColumnWidthOutlined', template: '<span>↔</span>' },
  ColumnHeightOutlined: { name: 'ColumnHeightOutlined', template: '<span>↕</span>' },
  DownOutlined: { name: 'DownOutlined', template: '<span>▼</span>' },
}));

describe('Multi-Selection Functionality', () => {
  let pinia: any;
  let diagramStore: any;

  beforeEach(() => {
    pinia = createPinia();
    setActivePinia(pinia);
    diagramStore = useDiagramStore();
    
    // Create a test diagram with some symbols
    diagramStore.createDiagram('Test Diagram');
    
    // Add test symbols
    const testSymbol1 = {
      id: 'symbol1',
      definitionId: 'test-symbol',
      position: { x: 100, y: 100 },
      scale: 1,
      rotation: 0,
    };
    
    const testSymbol2 = {
      id: 'symbol2',
      definitionId: 'test-symbol',
      position: { x: 200, y: 200 },
      scale: 1,
      rotation: 0,
    };
    
    diagramStore.currentDiagram.symbolInstances = {
      symbol1: testSymbol1,
      symbol2: testSymbol2,
    };
    
    // Add symbols to layer
    diagramStore.currentDiagram.layers[0].symbolInstanceIds = ['symbol1', 'symbol2'];
  });

  describe('MultiSelectionToolbar', () => {
    it('should not show toolbar when no items are selected', () => {
      const wrapper = mount(MultiSelectionToolbar, {
        global: {
          plugins: [pinia],
        },
      });

      expect(wrapper.find('.multi-selection-toolbar').exists()).toBe(false);
    });

    it('should show toolbar when items are selected', () => {
      // Select some symbols
      diagramStore.currentDiagram.selectedSymbolIds = ['symbol1', 'symbol2'];

      const wrapper = mount(MultiSelectionToolbar, {
        global: {
          plugins: [pinia],
        },
      });

      expect(wrapper.find('.multi-selection-toolbar').exists()).toBe(true);
      expect(wrapper.text()).toContain('2 symbols selected');
    });

    it('should emit events when action buttons are clicked', async () => {
      // Select some symbols
      diagramStore.currentDiagram.selectedSymbolIds = ['symbol1', 'symbol2'];

      const wrapper = mount(MultiSelectionToolbar, {
        global: {
          plugins: [pinia],
        },
      });

      // Test copy button
      await wrapper.find('[data-testid="copy-button"]').trigger('click');
      expect(wrapper.emitted('copy')).toBeTruthy();

      // Test delete button
      await wrapper.find('[data-testid="delete-button"]').trigger('click');
      expect(wrapper.emitted('delete')).toBeTruthy();
    });

    it('should show alignment controls when multiple symbols are selected', () => {
      // Select multiple symbols
      diagramStore.currentDiagram.selectedSymbolIds = ['symbol1', 'symbol2'];

      const wrapper = mount(MultiSelectionToolbar, {
        global: {
          plugins: [pinia],
        },
      });

      expect(wrapper.text()).toContain('Align');
      expect(wrapper.text()).toContain('Distribute');
    });

    it('should show correct selection count text', () => {
      // Test different selection combinations
      diagramStore.currentDiagram.selectedSymbolIds = ['symbol1'];
      diagramStore.currentDiagram.selectedConnectionIds = ['conn1', 'conn2'];

      const wrapper = mount(MultiSelectionToolbar, {
        global: {
          plugins: [pinia],
        },
      });

      expect(wrapper.text()).toContain('1 symbol, 2 connections selected');
    });
  });

  describe('Selection Rectangle Logic', () => {
    it('should correctly identify symbols within selection rectangle', () => {
      // Mock symbol definition
      const mockDefinition = {
        id: 'test-symbol',
        name: 'Test Symbol',
        dimensions: { width: 50, height: 50 },
        connectionPoints: [],
      };

      // Mock getSymbolDefinition function
      const getSymbolDefinition = vi.fn().mockReturnValue(mockDefinition);

      // Test rectangle that should include symbol1 (at 100,100)
      const selectionRect = { x: 90, y: 90, width: 30, height: 30 };
      
      // Symbol bounds: x: 100, y: 100, width: 50, height: 50
      const symbolBounds = { x: 100, y: 100, width: 50, height: 50 };

      // Test intersection logic
      const isIntersecting = !(
        selectionRect.x + selectionRect.width < symbolBounds.x ||
        symbolBounds.x + symbolBounds.width < selectionRect.x ||
        selectionRect.y + selectionRect.height < symbolBounds.y ||
        symbolBounds.y + symbolBounds.height < selectionRect.y
      );

      expect(isIntersecting).toBe(true);
    });

    it('should handle viewport transformations correctly', () => {
      // Test that selection coordinates are properly transformed
      // when the viewport is scaled or translated
      const viewportScale = 1.5;
      const viewportOffset = { x: 50, y: 50 };
      
      // Screen coordinates
      const screenRect = { x: 100, y: 100, width: 100, height: 100 };
      
      // Convert to world coordinates
      const worldRect = {
        x: (screenRect.x + viewportOffset.x) / viewportScale,
        y: (screenRect.y + viewportOffset.y) / viewportScale,
        width: screenRect.width / viewportScale,
        height: screenRect.height / viewportScale,
      };

      expect(worldRect.x).toBeCloseTo(100);
      expect(worldRect.y).toBeCloseTo(100);
      expect(worldRect.width).toBeCloseTo(66.67, 1);
      expect(worldRect.height).toBeCloseTo(66.67, 1);
    });
  });

  describe('Keyboard Multi-Selection', () => {
    it('should handle Ctrl+click for multi-selection', () => {
      // Initially select one symbol
      diagramStore.currentDiagram.selectedSymbolIds = ['symbol1'];

      // Simulate Ctrl+click on second symbol
      const isCtrlPressed = true;
      const currentSelection = [...diagramStore.currentDiagram.selectedSymbolIds];
      
      if (isCtrlPressed) {
        if (!currentSelection.includes('symbol2')) {
          currentSelection.push('symbol2');
        }
      }

      expect(currentSelection).toEqual(['symbol1', 'symbol2']);
    });

    it('should toggle selection when Ctrl+clicking already selected item', () => {
      // Initially select both symbols
      diagramStore.currentDiagram.selectedSymbolIds = ['symbol1', 'symbol2'];

      // Simulate Ctrl+click on already selected symbol
      const isCtrlPressed = true;
      let currentSelection = [...diagramStore.currentDiagram.selectedSymbolIds];
      
      if (isCtrlPressed) {
        const index = currentSelection.indexOf('symbol1');
        if (index !== -1) {
          currentSelection.splice(index, 1);
        }
      }

      expect(currentSelection).toEqual(['symbol2']);
    });
  });

  describe('Selection State Management', () => {
    it('should clear other selections when selecting symbols', () => {
      // Set up mixed selection
      diagramStore.currentDiagram.selectedSymbolIds = [];
      diagramStore.currentDiagram.selectedConnectionIds = ['conn1'];
      diagramStore.currentDiagram.selectedGroupIds = ['group1'];

      // Select a symbol (should clear other selections)
      diagramStore.currentDiagram.selectedSymbolIds = ['symbol1'];
      diagramStore.currentDiagram.selectedConnectionIds = [];
      diagramStore.currentDiagram.selectedGroupIds = [];

      expect(diagramStore.currentDiagram.selectedSymbolIds).toEqual(['symbol1']);
      expect(diagramStore.currentDiagram.selectedConnectionIds).toEqual([]);
      expect(diagramStore.currentDiagram.selectedGroupIds).toEqual([]);
    });

    it('should maintain selection state across operations', () => {
      // Select multiple symbols
      diagramStore.currentDiagram.selectedSymbolIds = ['symbol1', 'symbol2'];

      // Perform an operation (like alignment)
      const selectedSymbols = diagramStore.currentDiagram.selectedSymbolIds
        .map(id => diagramStore.currentDiagram.symbolInstances[id])
        .filter(Boolean);

      expect(selectedSymbols).toHaveLength(2);
      expect(selectedSymbols[0].id).toBe('symbol1');
      expect(selectedSymbols[1].id).toBe('symbol2');
    });
  });
});
