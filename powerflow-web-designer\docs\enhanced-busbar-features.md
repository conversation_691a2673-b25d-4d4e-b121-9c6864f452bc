# 增强母线图元功能

## 概述

本文档描述了PowerFlow Web Designer中增强的母线图元功能，专为IDC数据中心供电系统设计，支持多设备并联连接的复杂电力拓扑。

## 新增功能

### 1. 动态连接点系统

#### 特性
- **可配置连接点数量**：支持1-10个连接点
- **自动分布**：连接点可自动均匀分布在母线两侧
- **手动间距**：支持手动设置连接点间距
- **双向连接**：所有连接点支持双向电力流动

#### 实现
```typescript
// 生成动态连接点
const connectionPoints = generateBusbarConnectionPoints(
  BusbarOrientation.HORIZONTAL,
  { width: 200, height: 60 },
  3, // 连接点数量
  true // 自动分布
);
```

### 2. 方向支持

#### 水平母线 (busbar-horizontal)
- **尺寸**：长度200px，宽度20px（可配置）
- **连接点**：分布在上下两侧
- **适用场景**：主要配电母线、负载分配

#### 垂直母线 (busbar-vertical)
- **尺寸**：宽度20px，长度200px（可配置）
- **连接点**：分布在左右两侧
- **适用场景**：馈线母线、设备连接

### 3. 母线属性编辑器

#### 配置选项
- **方向选择**：水平/垂直
- **尺寸设置**：长度(50-500px)、宽度(10-50px)
- **连接点配置**：数量(1-10)、间距(20-100px)
- **自动分布**：开启/关闭自动均匀分布
- **实时预览**：配置变更的可视化预览

#### 使用方法
1. 选择母线图元
2. 在属性面板中找到"母线属性"部分
3. 调整配置参数
4. 点击"应用更改"

### 4. 增强的发电设备

#### 柴油发电机 (diesel-generator)
- **专用图标**：包含燃油箱指示器
- **扩展属性**：
  - 燃油液位 (%)
  - 运行时间 (小时)
  - 发动机温度 (°C)
  - 运行状态：离线/启动中/同步中/运行/故障/维护

#### 市电输入 (mains-power-input)
- **多路输出**：L1、L2、接地
- **监测参数**：
  - 进线电压 (V)
  - 电网频率 (Hz)
  - 进线电流 (A)

## 技术实现

### 核心文件结构

```
src/
├── types/symbol.ts              # 母线类型定义
├── utils/
│   ├── symbolLibrary.ts         # 增强的符号库
│   └── busbarUtils.ts          # 母线工具函数
├── components/
│   ├── canvas/
│   │   ├── SymbolInstance.vue   # 支持动态连接点
│   │   └── ConnectionCreator.vue # 连接点识别
│   └── panels/
│       ├── PropertyPanel.vue    # 集成母线编辑器
│       └── BusbarPropertiesEditor.vue # 专用编辑器
└── stores/diagram.ts            # 符号实例更新方法
```

### 关键接口

#### BusbarProperties
```typescript
interface BusbarProperties {
  orientation: BusbarOrientation;
  length: number;
  width: number;
  connectionPointCount: number;
  connectionPointSpacing: number;
  autoDistributePoints: boolean;
}
```

#### 工具函数
```typescript
// 生成连接点
generateBusbarConnectionPoints(orientation, dimensions, count, autoDistribute)

// 获取连接点位置
getBusbarConnectionPointPosition(instance, definition, pointId)

// 生成SVG
generateBusbarSVG(properties)

// 验证属性
validateBusbarProperties(properties)
```

## 使用场景

### IDC数据中心供电系统

#### 典型拓扑
```
市电1 ──┐
        ├── 水平母线 ── UPS1 ── 服务器机柜1
市电2 ──┤              ├── UPS2 ── 服务器机柜2
        │              └── UPS3 ── 服务器机柜3
柴发1 ──┤
        │
柴发2 ──┘
```

#### 配置建议
- **主母线**：水平方向，6-8个连接点
- **分支母线**：垂直方向，3-4个连接点
- **连接点间距**：根据设备密度调整

## 兼容性

### 向后兼容
- 保留原有`busbar`符号定义
- 现有图纸无需修改
- 渐进式升级支持

### 正交连接路由
- 自动适配母线多连接点
- 避免连接线重叠
- 优化路径计算

## 最佳实践

### 设计建议
1. **母线方向**：根据电力流向选择合适方向
2. **连接点数量**：预留20%余量用于扩展
3. **间距设置**：确保连接线不重叠
4. **颜色编码**：不同电压等级使用不同颜色

### 性能优化
1. **连接点限制**：避免超过10个连接点
2. **SVG缓存**：相同配置的母线共享SVG
3. **渲染优化**：大型图纸中使用简化显示

## 故障排除

### 常见问题
1. **连接点不显示**：检查母线属性配置
2. **连接失败**：确认连接点类型匹配
3. **性能问题**：减少连接点数量或简化SVG

### 调试工具
- 浏览器开发者工具查看SVG渲染
- 控制台日志跟踪连接点生成
- 属性面板实时预览功能

## 未来扩展

### 计划功能
- 母线负载均衡显示
- 动态电流流向动画
- 母线保护配置界面
- 三维母线渲染支持
