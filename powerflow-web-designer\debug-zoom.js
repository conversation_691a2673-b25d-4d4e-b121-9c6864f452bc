// Debug script for zoom functionality
// Run this in the browser console to test zoom behavior

console.log('=== PowerFlow Zoom Functionality Debug ===');

// Check if the diagram store is available
if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__ && window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps) {
  console.log('Vue DevTools detected');
  
  // Get the Vue app instance
  const app = window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps[0];
  if (app) {
    console.log('Vue app found:', app);
    
    // Try to access the diagram store
    const store = app.config.globalProperties.$pinia;
    if (store) {
      console.log('Pinia store found:', store);
    }
  }
}

// Check for zoom-related elements
const zoomButtons = {
  zoomIn: document.querySelector('[title*="Zoom In"], [title*="放大"]'),
  zoomOut: document.querySelector('[title*="Zoom Out"], [title*="缩小"]'),
  fitContent: document.querySelector('[title*="Fit Content"], [title*="适应内容"]'),
  resetView: document.querySelector('[title*="Reset View"], [title*="重置视图"]')
};

console.log('Zoom buttons found:', zoomButtons);

// Check for canvas elements
const canvas = document.querySelector('.diagram-canvas');
const stage = document.querySelector('canvas');

console.log('Canvas elements:', {
  diagramCanvas: !!canvas,
  konvaStage: !!stage,
  stageSize: stage ? { width: stage.width, height: stage.height } : null
});

// Function to test zoom programmatically
window.testZoom = function(action) {
  console.log(`Testing zoom action: ${action}`);
  
  const button = zoomButtons[action];
  if (button) {
    button.click();
    console.log(`${action} button clicked`);
    
    // Check viewport after zoom
    setTimeout(() => {
      // This would need to be adapted based on actual store structure
      console.log('Viewport after zoom:', 'Check diagram store viewport property');
    }, 100);
  } else {
    console.error(`Button for ${action} not found`);
  }
};

// Function to test keyboard shortcuts
window.testKeyboardZoom = function(key) {
  console.log(`Testing keyboard zoom: Ctrl+${key}`);
  
  const event = new KeyboardEvent('keydown', {
    key: key,
    ctrlKey: true,
    bubbles: true
  });
  
  document.dispatchEvent(event);
  console.log(`Ctrl+${key} event dispatched`);
};

// Test all zoom functions
window.runZoomTests = function() {
  console.log('=== Running Zoom Tests ===');
  
  // Test toolbar buttons
  Object.keys(zoomButtons).forEach(action => {
    setTimeout(() => testZoom(action), 1000);
  });
  
  // Test keyboard shortcuts
  const shortcuts = ['+', '-', '0', '1'];
  shortcuts.forEach((key, index) => {
    setTimeout(() => testKeyboardZoom(key), 5000 + (index * 1000));
  });
};

console.log('Debug functions available:');
console.log('- testZoom(action): Test specific zoom action (zoomIn, zoomOut, fitContent, resetView)');
console.log('- testKeyboardZoom(key): Test keyboard shortcut ("+", "-", "0", "1")');
console.log('- runZoomTests(): Run all zoom tests automatically');
console.log('');
console.log('Example usage:');
console.log('testZoom("zoomIn")');
console.log('testKeyboardZoom("+")');
console.log('runZoomTests()');
