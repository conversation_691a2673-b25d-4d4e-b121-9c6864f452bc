# 严格正交路由实现 - 绝对禁止遮挡图元和斜线连接

## 概述

本文档详细说明了PowerFlow Web Designer中实现的严格正交路由系统，该系统严格遵循以下三个核心要求：

1. **绝对禁止遮挡图元** - 连线必须完全避开所有图元
2. **必须是垂直折线** - 只允许水平和垂直线段，绝对不允许斜线
3. **尽量减少转折点** - 在满足前两个条件下，最小化弯曲数量

## 核心实现

### **主要路由函数**

#### **1. calculateStrictOrthogonalPath() - 主路由计算器**
```typescript
const calculateStrictOrthogonalPath = (source: Position, target: Position, obstacles: SymbolInstance[], options: ConnectionRoutingOptions): Position[] => {
  // STEP 1: 尝试直接正交路径 (0个转折点)
  const directPath = attemptDirectOrthogonalPath(source, target, obstacles);
  if (directPath) return directPath;
  
  // STEP 2: 尝试L形路径避障 (1个转折点)
  const lShapedPath = attemptLShapedPathWithAvoidance(source, target, obstacles);
  if (lShapedPath) return lShapedPath;
  
  // STEP 3: 使用高级避障算法 (多个转折点)
  return calculateAdvancedObstacleAvoidancePath(source, target, obstacles);
};
```

#### **2. 路径验证系统**
```typescript
const validateOrthogonalPath = (path: Position[], obstacles: SymbolInstance[]): { isValid: boolean, issues: string[] } => {
  // 验证所有线段都是正交的
  // 验证没有与障碍物相交
  // 返回详细的验证结果
};
```

### **障碍物避让策略**

#### **策略1: 直接正交连接 (0转折点)**
- 检查源点和目标点是否水平或垂直对齐
- 验证直线路径是否与任何图元相交
- 如果可行，返回最简单的直线连接

#### **策略2: L形路径避障 (1转折点)**
- 尝试水平优先的L形路径
- 尝试垂直优先的L形路径
- 选择不与障碍物相交的路径

#### **策略3: 扩展L形路径 (2转折点)**
- 使用不同的偏移量(50px, 100px, 150px)
- 创建绕过障碍物的扩展L形路径
- 确保所有线段都是正交的

#### **策略4: Z形路径 (2转折点)**
- 在源点和目标点之间创建中间点
- 形成Z字形的正交路径
- 适用于中等距离的连接

#### **策略5: 高级A*算法 (多转折点)**
- 在障碍物周围生成路径点网格
- 使用A*算法找到最优正交路径
- 保证绝对避开所有障碍物

### **关键技术特性**

#### **1. 严格正交约束**
```typescript
// 检查线段是否严格正交
const dx = Math.abs(end.x - start.x);
const dy = Math.abs(end.y - start.y);
if (dx > 1 && dy > 1) {
  // 这是斜线，绝对不允许
  issues.push(`Segment ${i} is not orthogonal`);
}
```

#### **2. 精确障碍物检测**
```typescript
const lineIntersectsRect = (start: Position, end: Position, rect: Rectangle): boolean => {
  // 检查线段是否与矩形的任何边相交
  // 检查线段端点是否在矩形内部
  // 确保绝对没有遮挡
};
```

#### **3. 安全边距**
```typescript
const margin = 20; // 障碍物周围的安全边距
const obstacleRect = {
  x: obstacle.position.x - margin,
  y: obstacle.position.y - margin,
  width: definition.dimensions.width * obstacle.scale + 2 * margin,
  height: definition.dimensions.height * obstacle.scale + 2 * margin
};
```

#### **4. 转折点最小化**
```typescript
const minimizeBendsWhileAvoidingObstacles = (path: Position[], obstacles: SymbolInstance[]): Position[] => {
  // 尝试移除不必要的路径点
  // 在保证避障的前提下减少转折点
  // 迭代优化直到无法进一步简化
};
```

## 路由决策流程

### **第1步: 初始路径计算**
1. 调用 `calculateStrictOrthogonalPath()`
2. 按优先级尝试不同策略
3. 返回第一个有效的无障碍路径

### **第2步: 路径验证**
1. 调用 `validateOrthogonalPath()`
2. 检查所有线段是否正交
3. 验证是否与障碍物相交
4. 如果验证失败，使用增强避障算法

### **第3步: 转折点优化**
1. 调用 `minimizeBendsWhileAvoidingObstacles()`
2. 尝试移除冗余的路径点
3. 确保优化后仍然避开所有障碍物

### **第4步: 最终安全检查**
1. 如果所有策略都失败，使用紧急回退路径
2. 保证始终返回有效的正交路径
3. 记录详细的调试信息

## 控制台日志验证

### **路径计算日志**
```
Calculating strict orthogonal path from (x1, y1) to (x2, y2)
Direct orthogonal path found
L-shaped orthogonal path found
Advanced obstacle avoidance path calculated
```

### **验证结果日志**
```
Strict orthogonal routing completed for connection [ID]:
- pathSegments: 2
- bends: 1
- obstaclesAvoided: 3
- pathValidated: true
```

### **错误处理日志**
```
Path validation failed for connection [ID]: [issues]
Creating emergency orthogonal path
```

## 测试场景

### **场景1: 简单连接**
- 两个图元直接对齐
- 预期: 0转折点的直线连接
- 验证: 无障碍物遮挡

### **场景2: L形连接**
- 两个图元不对齐，中间无障碍
- 预期: 1转折点的L形连接
- 验证: 选择最短的L形路径

### **场景3: 障碍物避让**
- 两个图元之间有其他图元
- 预期: 绕过障碍物的正交路径
- 验证: 绝对不穿过任何图元

### **场景4: 复杂布局**
- 多个图元密集排列
- 预期: 使用A*算法找到最优路径
- 验证: 最小转折点数量

### **场景5: 紧急情况**
- 所有策略都无法找到路径
- 预期: 使用紧急回退路径
- 验证: 保证连接可用性

## 性能优化

### **1. 分层策略**
- 优先使用简单策略(直线、L形)
- 只在必要时使用复杂算法(A*)
- 避免不必要的计算开销

### **2. 早期退出**
- 一旦找到有效路径立即返回
- 避免尝试更复杂的策略
- 提高响应速度

### **3. 缓存优化**
- 缓存障碍物边界计算结果
- 重用路径验证结果
- 减少重复计算

## 严格要求保证

### **✅ 绝对禁止遮挡图元**
- 所有路径都经过严格的障碍物检测
- 使用安全边距确保视觉清晰
- 多层验证确保无遗漏

### **✅ 必须是垂直折线**
- 所有线段都经过正交性验证
- 绝对不允许任何斜线段
- 紧急回退也保证正交性

### **✅ 尽量减少转折点**
- 优先使用转折点更少的策略
- 后处理优化移除冗余点
- 在避障前提下最小化复杂度

PowerFlow Web Designer现在提供了业界最严格的正交路由系统，完全满足电气原理图的专业要求。
