<template>
  <v-group
    :config="{
      x: group.position.x,
      y: group.position.y,
      rotation: group.rotation,
      draggable: !isLocked && !readOnly,
      id: group.id,
    }"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
    @dragmove="handleDragMove"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Group outline -->
    <v-rect
      :config="{
        width: group.size.width,
        height: group.size.height,
        stroke: isSelected ? '#1890ff' : '#d9d9d9',
        strokeWidth: isSelected ? 2 : 1,
        dash: [5, 5],
        fill: isSelected ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
        cornerRadius: 4,
      }"
    />

    <!-- Group label -->
    <v-text
      :config="{
        text: group.name,
        fontSize: 12,
        fontFamily: 'Arial',
        fill: '#666',
        x: 5,
        y: -16,
      }"
    />

    <!-- Resize handles (only shown when selected) -->
    <template v-if="isSelected && !isLocked && !readOnly">
      <v-circle
        v-for="(handle, index) in resizeHandles"
        :key="index"
        :config="{
          x: handle.x,
          y: handle.y,
          radius: 6,
          fill: '#fff',
          stroke: '#1890ff',
          strokeWidth: 1,
          draggable: true,
        }"
        @dragmove="(e) => handleResizeHandleDragMove(index, e)"
      />
    </template>
  </v-group>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { DiagramGroup } from '@/types/diagram';
import { Position } from '@/types/symbol';

// Props
const props = defineProps<{
  group: DiagramGroup;
  isSelected: boolean;
  isLocked: boolean;
  readOnly?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'select', id: string): void;
  (e: 'deselect', id: string): void;
  (e: 'move', id: string, x: number, y: number): void;
  (e: 'resize', id: string, width: number, height: number): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const isHovered = ref(false);
const isDragging = ref(false);
const dragStartPosition = ref({ x: 0, y: 0 });

// Computed
const resizeHandles = computed(() => {
  const width = props.group.size.width;
  const height = props.group.size.height;
  
  return [
    { x: 0, y: 0 }, // Top-left
    { x: width / 2, y: 0 }, // Top-center
    { x: width, y: 0 }, // Top-right
    { x: width, y: height / 2 }, // Middle-right
    { x: width, y: height }, // Bottom-right
    { x: width / 2, y: height }, // Bottom-center
    { x: 0, y: height }, // Bottom-left
    { x: 0, y: height / 2 }, // Middle-left
  ];
});

// Methods
const handleClick = (e: any) => {
  e.cancelBubble = true;
  
  if (props.isLocked || props.readOnly) return;
  
  if (!props.isSelected) {
    emit('select', props.group.id);
  }
};

const handleMouseEnter = () => {
  isHovered.value = true;
};

const handleMouseLeave = () => {
  isHovered.value = false;
};

const handleDragStart = (e: any) => {
  if (props.isLocked || props.readOnly) return;
  
  isDragging.value = true;
  dragStartPosition.value = { x: e.target.x(), y: e.target.y() };
  
  // Select the group if not already selected
  if (!props.isSelected) {
    emit('select', props.group.id);
  }
};

const handleDragEnd = (e: any) => {
  if (props.isLocked || props.readOnly) return;
  
  isDragging.value = false;
  
  // Snap to grid if enabled
  const grid = diagramStore.currentDiagram?.grid;
  if (grid?.snapToGrid) {
    const gridSize = grid.size;
    const x = Math.round(e.target.x() / gridSize) * gridSize;
    const y = Math.round(e.target.y() / gridSize) * gridSize;
    
    e.target.position({ x, y });
    emit('move', props.group.id, x, y);
  } else {
    emit('move', props.group.id, e.target.x(), e.target.y());
  }
};

const handleDragMove = (e: any) => {
  if (props.isLocked || props.readOnly) return;
  
  // Move all symbols in the group
  if (diagramStore.currentDiagram) {
    const dx = e.target.x() - dragStartPosition.value.x;
    const dy = e.target.y() - dragStartPosition.value.y;
    
    // Update positions of all symbols in the group
    props.group.symbolInstanceIds.forEach(symbolId => {
      const symbol = diagramStore.currentDiagram?.symbolInstances[symbolId];
      if (symbol) {
        symbol.position.x += dx;
        symbol.position.y += dy;
      }
    });
    
    // Update drag start position
    dragStartPosition.value = { x: e.target.x(), y: e.target.y() };
  }
};

const handleResizeHandleDragMove = (handleIndex: number, e: any) => {
  if (props.isLocked || props.readOnly) return;
  
  const width = props.group.size.width;
  const height = props.group.size.height;
  
  // Calculate new size based on which handle is being dragged
  let newWidth = width;
  let newHeight = height;
  let newX = props.group.position.x;
  let newY = props.group.position.y;
  
  switch (handleIndex) {
    case 0: // Top-left
      newWidth = width + (0 - e.target.x());
      newHeight = height + (0 - e.target.y());
      newX = props.group.position.x + e.target.x();
      newY = props.group.position.y + e.target.y();
      break;
    case 1: // Top-center
      newHeight = height + (0 - e.target.y());
      newY = props.group.position.y + e.target.y();
      break;
    case 2: // Top-right
      newWidth = e.target.x();
      newHeight = height + (0 - e.target.y());
      newY = props.group.position.y + e.target.y();
      break;
    case 3: // Middle-right
      newWidth = e.target.x();
      break;
    case 4: // Bottom-right
      newWidth = e.target.x();
      newHeight = e.target.y();
      break;
    case 5: // Bottom-center
      newHeight = e.target.y();
      break;
    case 6: // Bottom-left
      newWidth = width + (0 - e.target.x());
      newHeight = e.target.y();
      newX = props.group.position.x + e.target.x();
      break;
    case 7: // Middle-left
      newWidth = width + (0 - e.target.x());
      newX = props.group.position.x + e.target.x();
      break;
  }
  
  // Ensure minimum size
  newWidth = Math.max(50, newWidth);
  newHeight = Math.max(50, newHeight);
  
  // Snap to grid if enabled
  const grid = diagramStore.currentDiagram?.grid;
  if (grid?.snapToGrid) {
    const gridSize = grid.size;
    newWidth = Math.round(newWidth / gridSize) * gridSize;
    newHeight = Math.round(newHeight / gridSize) * gridSize;
    newX = Math.round(newX / gridSize) * gridSize;
    newY = Math.round(newY / gridSize) * gridSize;
  }
  
  // Update group position and size
  emit('move', props.group.id, newX, newY);
  emit('resize', props.group.id, newWidth, newHeight);
  
  // Reset handle position
  e.target.position({ x: resizeHandles.value[handleIndex].x, y: resizeHandles.value[handleIndex].y });
};
</script>
