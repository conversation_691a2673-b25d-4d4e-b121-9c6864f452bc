<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoom Functionality Test - Standalone</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .toolbar {
            background: #1890ff;
            padding: 10px 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .btn {
            background: rgba(255,255,255,0.15);
            border: 1px solid rgba(255,255,255,0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: rgba(255,255,255,0.25);
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .canvas-container {
            position: relative;
            width: 100%;
            height: 600px;
            overflow: hidden;
            background: #fafafa;
            border-top: 1px solid #e8e8e8;
        }
        .canvas {
            position: absolute;
            width: 2000px;
            height: 2000px;
            background: white;
            border: 1px solid #ddd;
            transform-origin: 0 0;
            transition: transform 0.2s ease;
        }
        .symbol {
            position: absolute;
            width: 60px;
            height: 60px;
            background: #1890ff;
            border: 2px solid #0050b3;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: move;
        }
        .info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .instructions {
            padding: 20px;
            background: #f9f9f9;
            border-top: 1px solid #e8e8e8;
        }
        .shortcuts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .shortcut {
            background: #333;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="toolbar">
            <button class="btn" onclick="zoomIn()" title="Zoom In">🔍+</button>
            <button class="btn" onclick="zoomOut()" title="Zoom Out">🔍-</button>
            <button class="btn" onclick="fitContent()" title="Fit Content">⛶</button>
            <button class="btn" onclick="resetView()" title="Reset View">⌂</button>
            <span style="color: white; margin-left: 20px;">Use Ctrl + +/- for keyboard shortcuts</span>
        </div>
        
        <div class="canvas-container" id="canvasContainer">
            <div class="canvas" id="canvas">
                <div class="symbol" style="left: 200px; top: 150px;">S1</div>
                <div class="symbol" style="left: 400px; top: 300px;">S2</div>
                <div class="symbol" style="left: 600px; top: 200px;">S3</div>
                <div class="symbol" style="left: 300px; top: 450px;">S4</div>
            </div>
            <div class="info" id="info">
                Scale: 100%<br>
                Position: (0, 0)
            </div>
        </div>
        
        <div class="instructions">
            <h3>Zoom Functionality Test</h3>
            <p>This standalone test simulates the zoom functionality implemented in PowerFlow Web Designer.</p>
            
            <div class="shortcuts">
                <div><span class="shortcut">Ctrl + +</span> Zoom In</div>
                <div><span class="shortcut">Ctrl + -</span> Zoom Out</div>
                <div><span class="shortcut">Ctrl + 0</span> Reset View</div>
                <div><span class="shortcut">Ctrl + 1</span> Fit Content</div>
            </div>
            
            <h4>Expected Behavior:</h4>
            <ul>
                <li><strong>Zoom In/Out:</strong> Should zoom toward/from the center of the visible area</li>
                <li><strong>Fit Content:</strong> Should fit all symbols with padding</li>
                <li><strong>Reset View:</strong> Should return to 100% scale at origin</li>
                <li><strong>Smooth Transitions:</strong> No jumping or sudden position changes</li>
            </ul>
        </div>
    </div>

    <script>
        // Viewport state (simulating the diagram store)
        let viewport = {
            position: { x: 0, y: 0 },
            scale: 1
        };

        const canvas = document.getElementById('canvas');
        const info = document.getElementById('info');
        const container = document.getElementById('canvasContainer');

        // Update canvas transform and info display
        function updateCanvas() {
            const transform = `translate(${viewport.position.x}px, ${viewport.position.y}px) scale(${viewport.scale})`;
            canvas.style.transform = transform;
            
            info.innerHTML = `
                Scale: ${Math.round(viewport.scale * 100)}%<br>
                Position: (${Math.round(viewport.position.x)}, ${Math.round(viewport.position.y)})
            `;
        }

        // Zoom In - matches our improved implementation
        function zoomIn() {
            const currentScale = viewport.scale;
            const newScale = Math.min(currentScale * 1.2, 5);
            
            // Get container center
            const centerX = container.clientWidth / 2;
            const centerY = container.clientHeight / 2;
            
            // Calculate zoom point (center-based)
            const mousePointTo = {
                x: (centerX - viewport.position.x) / currentScale,
                y: (centerY - viewport.position.y) / currentScale,
            };
            
            // Update viewport
            viewport.position.x = centerX / newScale - mousePointTo.x;
            viewport.position.y = centerY / newScale - mousePointTo.y;
            viewport.scale = newScale;
            
            updateCanvas();
        }

        // Zoom Out - matches our improved implementation
        function zoomOut() {
            const currentScale = viewport.scale;
            const newScale = Math.max(currentScale / 1.2, 0.1);
            
            // Get container center
            const centerX = container.clientWidth / 2;
            const centerY = container.clientHeight / 2;
            
            // Calculate zoom point (center-based)
            const mousePointTo = {
                x: (centerX - viewport.position.x) / currentScale,
                y: (centerY - viewport.position.y) / currentScale,
            };
            
            // Update viewport
            viewport.position.x = centerX / newScale - mousePointTo.x;
            viewport.position.y = centerY / newScale - mousePointTo.y;
            viewport.scale = newScale;
            
            updateCanvas();
        }

        // Reset View - matches our implementation
        function resetView() {
            viewport.position.x = 0;
            viewport.position.y = 0;
            viewport.scale = 1;
            updateCanvas();
        }

        // Fit Content - simplified version of our implementation
        function fitContent() {
            const symbols = document.querySelectorAll('.symbol');
            if (symbols.length === 0) {
                resetView();
                return;
            }

            let minX = Infinity, minY = Infinity;
            let maxX = -Infinity, maxY = -Infinity;

            symbols.forEach(symbol => {
                const rect = symbol.getBoundingClientRect();
                const canvasRect = canvas.getBoundingClientRect();
                
                // Convert to canvas coordinates
                const x = (rect.left - canvasRect.left) / viewport.scale;
                const y = (rect.top - canvasRect.top) / viewport.scale;
                
                minX = Math.min(minX, x);
                minY = Math.min(minY, y);
                maxX = Math.max(maxX, x + 60); // symbol width
                maxY = Math.max(maxY, y + 60); // symbol height
            });

            // Add padding
            const padding = 100;
            minX -= padding;
            minY -= padding;
            maxX += padding;
            maxY += padding;

            // Calculate scale to fit
            const contentWidth = maxX - minX;
            const contentHeight = maxY - minY;
            const scaleX = container.clientWidth / contentWidth;
            const scaleY = container.clientHeight / contentHeight;
            const scale = Math.min(scaleX, scaleY, 3); // max 300%

            // Center the content
            const centerX = (minX + maxX) / 2;
            const centerY = (minY + maxY) / 2;

            viewport.position.x = centerX - container.clientWidth / (2 * scale);
            viewport.position.y = centerY - container.clientHeight / (2 * scale);
            viewport.scale = scale;

            updateCanvas();
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case '+':
                    case '=':
                        event.preventDefault();
                        zoomIn();
                        break;
                    case '-':
                        event.preventDefault();
                        zoomOut();
                        break;
                    case '0':
                        event.preventDefault();
                        resetView();
                        break;
                    case '1':
                        event.preventDefault();
                        fitContent();
                        break;
                }
            }
        });

        // Initialize
        updateCanvas();
    </script>
</body>
</html>
