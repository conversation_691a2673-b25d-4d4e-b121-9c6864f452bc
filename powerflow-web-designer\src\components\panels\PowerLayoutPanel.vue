<template>
  <div class="power-layout-panel">
    <a-collapse v-model:activeKey="activeKeys" :bordered="false">
      <a-collapse-panel key="1" header="电力系统布局优化">
        <a-form layout="vertical" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
          <!-- Layout Type Selection -->
          <a-form-item label="布局类型">
            <a-radio-group v-model:value="layoutOptions.type" button-style="solid">
              <a-radio-button value="radial">放射状</a-radio-button>
              <a-radio-button value="cascade">梯级</a-radio-button>
              <a-radio-button value="ring">环形</a-radio-button>
              <a-radio-button value="busbar">母线-分支</a-radio-button>
            </a-radio-group>
          </a-form-item>

          <!-- Direction Selection (for cascade and busbar layouts) -->
          <a-form-item
            v-if="layoutOptions.type === 'cascade' || layoutOptions.type === 'busbar'"
            label="方向"
          >
            <a-radio-group v-model:value="layoutOptions.direction" button-style="solid">
              <a-radio-button value="horizontal">水平</a-radio-button>
              <a-radio-button value="vertical">垂直</a-radio-button>
            </a-radio-group>
          </a-form-item>

          <!-- Spacing Slider -->
          <a-form-item label="间距">
            <a-slider
              v-model:value="layoutOptions.spacing"
              :min="50"
              :max="300"
              :step="10"
            />
          </a-form-item>

          <!-- Radius Slider (for radial and ring layouts) -->
          <a-form-item
            v-if="layoutOptions.type === 'radial' || layoutOptions.type === 'ring'"
            label="半径"
          >
            <a-slider
              v-model:value="layoutOptions.radius"
              :min="100"
              :max="500"
              :step="20"
            />
          </a-form-item>

          <!-- Angle Range (for radial layout) -->
          <a-form-item
            v-if="layoutOptions.type === 'radial'"
            label="角度范围"
          >
            <a-slider
              v-model:value="angleRange"
              range
              :min="0"
              :max="360"
              :step="15"
              @change="updateAngleRange"
            />
          </a-form-item>

          <!-- Options -->
          <a-form-item label="选项">
            <a-checkbox v-model:checked="layoutOptions.optimizeConnections">优化连线</a-checkbox>
            <a-checkbox v-model:checked="layoutOptions.alignToGrid">对齐到网格</a-checkbox>
            <a-checkbox v-model:checked="preserveSelected">保持选中图元位置</a-checkbox>
          </a-form-item>

          <!-- Apply Button -->
          <a-form-item>
            <a-button
              type="primary"
              :disabled="!canApplyLayout"
              @click="applyLayout"
            >
              应用布局
            </a-button>
            <a-button
              style="margin-left: 8px"
              :disabled="!canApplyLayout"
              @click="applyToSelected"
            >
              应用到选中区域
            </a-button>
          </a-form-item>
        </a-form>
      </a-collapse-panel>

      <a-collapse-panel key="2" header="连线优化">
        <a-form layout="vertical" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
          <a-form-item>
            <a-button
              type="primary"
              :disabled="!hasConnections"
              @click="optimizeAllConnections"
            >
              优化所有连线
            </a-button>
            <a-button
              style="margin-left: 8px"
              :disabled="!hasSelectedConnections"
              @click="optimizeSelectedConnections"
            >
              优化选中连线
            </a-button>
          </a-form-item>
        </a-form>
      </a-collapse-panel>

      <a-collapse-panel key="3" header="对齐与分布">
        <a-form layout="vertical" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
          <!-- Distribute Evenly -->
          <a-form-item label="均匀分布">
            <a-button-group>
              <a-button
                :disabled="!canDistribute"
                @click="distributeHorizontally"
              >
                <template #icon><column-height-outlined /></template>
                水平均匀分布
              </a-button>
              <a-button
                :disabled="!canDistribute"
                @click="distributeVertically"
              >
                <template #icon><column-width-outlined /></template>
                垂直均匀分布
              </a-button>
            </a-button-group>
          </a-form-item>

          <!-- Align to Grid -->
          <a-form-item label="对齐到网格">
            <a-button
              :disabled="!hasSelectedSymbols"
              @click="alignSelectedToGrid"
            >
              <template #icon><border-outlined /></template>
              对齐选中图元到网格
            </a-button>
          </a-form-item>
        </a-form>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import {
  BorderOutlined,
  ColumnHeightOutlined,
  ColumnWidthOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useDiagramStore } from '@/stores/diagram';
import {
  PowerLayoutType,
  PowerLayoutOptions,
  defaultLayoutOptions,
  applyPowerLayout,
  optimizeSelectedArea,
  distributeSymbolsEvenly,
  alignSymbolsToGrid,
  optimizeConnections
} from '@/utils/powerLayoutUtils';
import type { Connection } from '@/types/connection';

// Store
const diagramStore = useDiagramStore();

// State
const activeKeys = ref<string[]>(['1']);
const layoutOptions = ref<PowerLayoutOptions>({
  ...defaultLayoutOptions,
  type: PowerLayoutType.RADIAL,
  spacing: 150,
  direction: 'horizontal',
  radius: 300,
  startAngle: 0,
  endAngle: 360,
  optimizeConnections: true,
  alignToGrid: true,
  preservePositions: [],
});
const angleRange = ref<[number, number]>([0, 360]);
const preserveSelected = ref<boolean>(false);

// Computed properties
const canApplyLayout = computed(() => {
  return diagramStore.currentDiagram &&
         Object.keys(diagramStore.currentDiagram.symbolInstances).length > 0;
});

const hasConnections = computed(() => {
  return diagramStore.currentDiagram &&
         Object.keys(diagramStore.currentDiagram.connections).length > 0;
});

const hasSelectedSymbols = computed(() => {
  return diagramStore.currentDiagram &&
         diagramStore.currentDiagram.selectedSymbolIds.length > 0;
});

const hasSelectedConnections = computed(() => {
  return diagramStore.currentDiagram &&
         diagramStore.currentDiagram.selectedConnectionIds.length > 0;
});

const canDistribute = computed(() => {
  return hasSelectedSymbols.value &&
         diagramStore.currentDiagram!.selectedSymbolIds.length >= 3;
});

// Watch for changes to preserveSelected
watch(preserveSelected, (newValue) => {
  if (newValue && diagramStore.currentDiagram) {
    // Set selected symbols as preserved positions
    layoutOptions.value.preservePositions = [...diagramStore.currentDiagram.selectedSymbolIds];
  } else {
    // Clear preserved positions
    layoutOptions.value.preservePositions = [];
  }
});

// Methods
function updateAngleRange(range: [number, number]) {
  layoutOptions.value.startAngle = range[0];
  layoutOptions.value.endAngle = range[1];
}

function applyLayout() {
  if (!diagramStore.currentDiagram) return;

  try {
    // Save current state to undo stack
    diagramStore.saveToUndoStack();

    // Get all symbols and connections
    const symbols = Object.values(diagramStore.currentDiagram.symbolInstances);
    const connections = diagramStore.currentDiagram.connections;

    // Apply layout
    const { symbolPositions, updatedConnections } = applyPowerLayout(
      symbols,
      connections,
      layoutOptions.value
    );

    // Update symbol positions
    symbolPositions.forEach(sp => {
      if (diagramStore.currentDiagram!.symbolInstances[sp.id]) {
        diagramStore.currentDiagram!.symbolInstances[sp.id].position = sp.position;
      }
    });

    // Update connections
    Object.entries(updatedConnections).forEach(([id, connection]) => {
      if (diagramStore.currentDiagram!.connections[id]) {
        diagramStore.currentDiagram!.connections[id] = connection;
      }
    });

    // Mark diagram as modified
    diagramStore.markAsModified();

    message.success('布局应用成功');
  } catch (error) {
    console.error('Failed to apply layout:', error);
    message.error('布局应用失败');
  }
}

function applyToSelected() {
  if (!diagramStore.currentDiagram || !hasSelectedSymbols.value) return;

  try {
    // Save current state to undo stack
    diagramStore.saveToUndoStack();

    // Get all symbols and connections
    const symbols = Object.values(diagramStore.currentDiagram.symbolInstances);
    const connections = diagramStore.currentDiagram.connections;
    const selectedSymbolIds = diagramStore.currentDiagram.selectedSymbolIds;

    // Apply layout to selected area
    const { symbolPositions, updatedConnections } = optimizeSelectedArea(
      symbols,
      connections,
      selectedSymbolIds,
      layoutOptions.value
    );

    // Update symbol positions
    symbolPositions.forEach(sp => {
      if (diagramStore.currentDiagram!.symbolInstances[sp.id]) {
        diagramStore.currentDiagram!.symbolInstances[sp.id].position = sp.position;
      }
    });

    // Update connections
    Object.entries(updatedConnections).forEach(([id, connection]) => {
      if (diagramStore.currentDiagram!.connections[id]) {
        diagramStore.currentDiagram!.connections[id] = connection;
      }
    });

    // Mark diagram as modified
    diagramStore.markAsModified();

    message.success('选中区域布局优化成功');
  } catch (error) {
    console.error('Failed to optimize selected area:', error);
    message.error('选中区域布局优化失败');
  }
}

function optimizeAllConnections() {
  if (!diagramStore.currentDiagram || !hasConnections.value) return;

  try {
    // Save current state to undo stack
    diagramStore.saveToUndoStack();

    // Get all symbols and connections
    const symbols = Object.values(diagramStore.currentDiagram.symbolInstances);
    const connections = diagramStore.currentDiagram.connections;

    // Create symbol positions array for the optimization function
    const symbolPositions = symbols.map(s => ({
      id: s.id,
      position: { ...s.position }
    }));

    // Optimize connections
    const updatedConnections = optimizeConnections(symbols, connections, symbolPositions);

    // Update connections
    Object.entries(updatedConnections).forEach(([id, connection]) => {
      if (diagramStore.currentDiagram!.connections[id]) {
        diagramStore.currentDiagram!.connections[id] = connection;
      }
    });

    // Mark diagram as modified
    diagramStore.markAsModified();

    message.success('连线优化成功');
  } catch (error) {
    console.error('Failed to optimize connections:', error);
    message.error('连线优化失败');
  }
}

function optimizeSelectedConnections() {
  if (!diagramStore.currentDiagram || !hasSelectedConnections.value) return;

  try {
    // Save current state to undo stack
    diagramStore.saveToUndoStack();

    // Get all symbols and selected connections
    const symbols = Object.values(diagramStore.currentDiagram.symbolInstances);
    const selectedConnectionIds = diagramStore.currentDiagram.selectedConnectionIds;

    // Filter connections to only include selected ones
    const selectedConnections: Record<string, Connection> = {};
    selectedConnectionIds.forEach(id => {
      if (diagramStore.currentDiagram!.connections[id]) {
        selectedConnections[id] = diagramStore.currentDiagram!.connections[id];
      }
    });

    // Create symbol positions array for the optimization function
    const symbolPositions = symbols.map(s => ({
      id: s.id,
      position: { ...s.position }
    }));

    // Optimize selected connections
    const updatedConnections = optimizeConnections(symbols, selectedConnections, symbolPositions);

    // Update connections
    Object.entries(updatedConnections).forEach(([id, connection]) => {
      if (diagramStore.currentDiagram!.connections[id]) {
        diagramStore.currentDiagram!.connections[id] = connection;
      }
    });

    // Mark diagram as modified
    diagramStore.markAsModified();

    message.success('选中连线优化成功');
  } catch (error) {
    console.error('Failed to optimize selected connections:', error);
    message.error('选中连线优化失败');
  }
}

function distributeHorizontally() {
  if (!diagramStore.currentDiagram || !canDistribute.value) return;

  try {
    // Save current state to undo stack
    diagramStore.saveToUndoStack();

    // Get selected symbols
    const selectedSymbolIds = diagramStore.currentDiagram.selectedSymbolIds;
    const selectedSymbols = selectedSymbolIds.map(id =>
      diagramStore.currentDiagram!.symbolInstances[id]
    ).filter(Boolean);

    // Distribute symbols horizontally
    const symbolPositions = distributeSymbolsEvenly(selectedSymbols, 'horizontal');

    // Update symbol positions
    symbolPositions.forEach(sp => {
      if (diagramStore.currentDiagram!.symbolInstances[sp.id]) {
        diagramStore.currentDiagram!.symbolInstances[sp.id].position = sp.position;
      }
    });

    // Mark diagram as modified
    diagramStore.markAsModified();

    message.success('水平均匀分布成功');
  } catch (error) {
    console.error('Failed to distribute horizontally:', error);
    message.error('水平均匀分布失败');
  }
}

function distributeVertically() {
  if (!diagramStore.currentDiagram || !canDistribute.value) return;

  try {
    // Save current state to undo stack
    diagramStore.saveToUndoStack();

    // Get selected symbols
    const selectedSymbolIds = diagramStore.currentDiagram.selectedSymbolIds;
    const selectedSymbols = selectedSymbolIds.map(id =>
      diagramStore.currentDiagram!.symbolInstances[id]
    ).filter(Boolean);

    // Distribute symbols vertically
    const symbolPositions = distributeSymbolsEvenly(selectedSymbols, 'vertical');

    // Update symbol positions
    symbolPositions.forEach(sp => {
      if (diagramStore.currentDiagram!.symbolInstances[sp.id]) {
        diagramStore.currentDiagram!.symbolInstances[sp.id].position = sp.position;
      }
    });

    // Mark diagram as modified
    diagramStore.markAsModified();

    message.success('垂直均匀分布成功');
  } catch (error) {
    console.error('Failed to distribute vertically:', error);
    message.error('垂直均匀分布失败');
  }
}

function alignSelectedToGrid() {
  if (!diagramStore.currentDiagram || !hasSelectedSymbols.value) return;

  try {
    // Save current state to undo stack
    diagramStore.saveToUndoStack();

    // Get selected symbols
    const selectedSymbolIds = diagramStore.currentDiagram.selectedSymbolIds;
    const selectedSymbols = selectedSymbolIds.map(id =>
      diagramStore.currentDiagram!.symbolInstances[id]
    ).filter(Boolean);

    // Align symbols to grid
    const gridSize = diagramStore.currentDiagram.grid.size || 20;
    const symbolPositions = alignSymbolsToGrid(selectedSymbols, gridSize);

    // Update symbol positions
    symbolPositions.forEach(sp => {
      if (diagramStore.currentDiagram!.symbolInstances[sp.id]) {
        diagramStore.currentDiagram!.symbolInstances[sp.id].position = sp.position;
      }
    });

    // Mark diagram as modified
    diagramStore.markAsModified();

    message.success('对齐到网格成功');
  } catch (error) {
    console.error('Failed to align to grid:', error);
    message.error('对齐到网格失败');
  }
}
</script>

<style scoped>
.power-layout-panel {
  padding: 8px;
}

.ant-form-item {
  margin-bottom: 12px;
}
</style>
