<template>
  <v-group>
    <!-- Background for better readability -->
    <v-rect
      :config="{
        x: 0,
        y: 0,
        width,
        height,
        fill: backgroundColor,
        cornerRadius: 3,
        opacity: backgroundOpacity,
      }"
    />

    <!-- Chart title -->
    <v-text
      v-if="showTitle"
      :config="{
        text: title,
        fontSize: titleFontSize,
        fontFamily,
        fill: textColor,
        align: 'center',
        x: 0,
        y: -titleFontSize - 2,
        width,
      }"
    />

    <!-- Chart placeholder (simplified for now) -->
    <v-rect
      :config="{
        x: padding,
        y: padding,
        width: width - padding * 2,
        height: height - padding * 2,
        fill: '#f0f0f0',
        stroke: '#d0d0d0',
        strokeWidth: 1,
      }"
    />

    <!-- Chart data visualization using simple lines -->
    <v-group
      :config="{
        x: padding,
        y: padding,
      }"
    >
      <v-line
        v-if="chartPoints.length > 1"
        :config="{
          points: chartPoints,
          stroke: lineColor,
          strokeWidth: 2,
          lineCap: 'round',
          lineJoin: 'round',
        }"
      />
    </v-group>

    <!-- Current value display -->
    <v-text
      v-if="showCurrentValue && currentValue !== null"
      :config="{
        text: formattedCurrentValue,
        fontSize: valueFontSize,
        fontFamily,
        fill: textColor,
        align: 'right',
        x: 0,
        y: height + 2,
        width,
      }"
    />
  </v-group>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';

// Props
const props = defineProps<{
  data: number[];
  labels?: string[];
  title?: string;
  width?: number;
  height?: number;
  padding?: number;
  backgroundColor?: string;
  backgroundOpacity?: number;
  lineColor?: string;
  fillColor?: string;
  textColor?: string;
  fontFamily?: string;
  titleFontSize?: number;
  valueFontSize?: number;
  showTitle?: boolean;
  showCurrentValue?: boolean;
  valueFormat?: string;
  valuePrecision?: number;
  valueUnit?: string;
  minValue?: number;
  maxValue?: number;
  autoScale?: boolean;
}>();

// Default values
const width = computed(() => props.width || 120);
const height = computed(() => props.height || 60);
const padding = computed(() => props.padding || 4);
const backgroundColor = computed(() => props.backgroundColor || '#ffffff');
const backgroundOpacity = computed(() => props.backgroundOpacity !== undefined ? props.backgroundOpacity : 0.7);
const lineColor = computed(() => props.lineColor || '#1890ff');
const fillColor = computed(() => props.fillColor || 'rgba(24, 144, 255, 0.2)');
const textColor = computed(() => props.textColor || '#333333');
const fontFamily = computed(() => props.fontFamily || 'Arial');
const titleFontSize = computed(() => props.titleFontSize || 12);
const valueFontSize = computed(() => props.valueFontSize || 12);
const showTitle = computed(() => props.showTitle !== undefined ? props.showTitle : true);
const showCurrentValue = computed(() => props.showCurrentValue !== undefined ? props.showCurrentValue : true);
const title = computed(() => props.title || 'Trend');
const valueFormat = computed(() => props.valueFormat || 'decimal');
const valuePrecision = computed(() => props.valuePrecision || 2);
const valueUnit = computed(() => props.valueUnit || '');
const minValue = computed(() => props.minValue !== undefined ? props.minValue : null);
const maxValue = computed(() => props.maxValue !== undefined ? props.maxValue : null);
const autoScale = computed(() => props.autoScale !== undefined ? props.autoScale : true);

// State
// Removed Chart.js dependencies to fix Konva compatibility issues

// Computed
const currentValue = computed(() => {
  if (!props.data || props.data.length === 0) return null;
  return props.data[props.data.length - 1];
});

const formattedCurrentValue = computed(() => {
  if (currentValue.value === null) return 'N/A';

  // Format the value
  let formattedValue = '';
  switch (valueFormat.value.toLowerCase()) {
    case 'integer':
      formattedValue = Math.round(currentValue.value).toString();
      break;
    case 'decimal':
      formattedValue = currentValue.value.toFixed(valuePrecision.value);
      break;
    case 'percentage':
      formattedValue = `${(currentValue.value * 100).toFixed(valuePrecision.value)}%`;
      break;
    case 'scientific':
      formattedValue = currentValue.value.toExponential(valuePrecision.value);
      break;
    default:
      formattedValue = currentValue.value.toFixed(valuePrecision.value);
  }

  // Add unit if provided
  if (valueUnit.value) {
    formattedValue += ` ${valueUnit.value}`;
  }

  return formattedValue;
});

// Chart points for Konva line rendering
const chartPoints = computed(() => {
  if (!props.data || props.data.length === 0) return [];

  const chartWidth = width.value - padding.value * 2;
  const chartHeight = height.value - padding.value * 2;

  // Calculate min and max values for scaling
  const dataMin = minValue.value !== null ? minValue.value : Math.min(...props.data);
  const dataMax = maxValue.value !== null ? maxValue.value : Math.max(...props.data);
  const dataRange = dataMax - dataMin || 1; // Avoid division by zero

  // Convert data points to screen coordinates
  const points: number[] = [];
  props.data.forEach((value, index) => {
    const x = (index / (props.data.length - 1)) * chartWidth;
    const y = chartHeight - ((value - dataMin) / dataRange) * chartHeight;
    points.push(x, y);
  });

  return points;
});

// Methods
const onCanvasReady = (element: HTMLElement) => {
  // Find the canvas element
  canvasElement.value = element.querySelector('canvas');
  if (canvasElement.value) {
    // Create the chart
    createChart();
  }
};

const createChart = () => {
  if (!canvasElement.value) return;
  
  // Destroy existing chart if any
  if (chartInstance.value) {
    chartInstance.value.destroy();
  }
  
  // Prepare chart data
  const chartData: ChartData = {
    labels: props.labels || Array(props.data.length).fill(''),
    datasets: [
      {
        label: title.value,
        data: props.data,
        borderColor: lineColor.value,
        backgroundColor: fillColor.value,
        borderWidth: 1,
        pointRadius: 0,
        tension: 0.4,
        fill: true,
      },
    ],
  };
  
  // Chart options
  const chartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
    scales: {
      x: {
        display: false,
      },
      y: {
        display: false,
        min: minValue.value !== null ? minValue.value : undefined,
        max: maxValue.value !== null ? maxValue.value : undefined,
        beginAtZero: !autoScale.value,
      },
    },
    animation: false,
  };
  
  // Create chart configuration
  const config: ChartConfiguration = {
    type: 'line',
    data: chartData,
    options: chartOptions,
  };
  
  // Create the chart
  chartInstance.value = new Chart(canvasElement.value, config);
};

// Watch for data changes to update the chart
watch(() => props.data, () => {
  if (chartInstance.value) {
    chartInstance.value.data.datasets[0].data = props.data;
    chartInstance.value.update();
  }
}, { deep: true });

// Watch for min/max value changes
watch([minValue, maxValue, autoScale], () => {
  if (chartInstance.value) {
    const scales = chartInstance.value.options.scales as any;
    if (scales && scales.y) {
      scales.y.min = minValue.value !== null ? minValue.value : undefined;
      scales.y.max = maxValue.value !== null ? maxValue.value : undefined;
      scales.y.beginAtZero = !autoScale.value;
      chartInstance.value.update();
    }
  }
});

// Cleanup on unmount
onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.destroy();
  }
});
</script>
