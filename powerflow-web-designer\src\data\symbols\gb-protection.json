[{"id": "lightning-arrester", "category": "protection", "name": "避雷器", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"50\" y1=\"0\" x2=\"50\" y2=\"20\" stroke=\"currentColor\" stroke-width=\"3\"/><polygon points=\"40,20 60,20 55,35 65,35 45,55 50,40 40,40\" fill=\"currentColor\"/><line x1=\"50\" y1=\"55\" x2=\"50\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"40\" y1=\"70\" x2=\"60\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"43\" y1=\"75\" x2=\"57\" y2=\"75\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"46\" y1=\"80\" x2=\"54\" y2=\"80\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "line", "position": {"x": 50, "y": 0}, "type": "input", "label": "线路"}, {"id": "earth", "position": {"x": 50, "y": 100}, "type": "output", "label": "接地"}], "bindingSlots": [{"id": "leakage_current", "name": "泄漏电流", "dataType": "number", "defaultValue": 0, "description": "避雷器泄漏电流 (mA)"}, {"id": "operating_voltage", "name": "工作电压", "dataType": "number", "defaultValue": 0, "description": "避雷器工作电压 (kV)"}, {"id": "status", "name": "状态", "dataType": "enum", "defaultValue": "normal", "enumValues": ["normal", "operating", "fault"], "description": "避雷器状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "surge-protector", "category": "protection", "name": "浪涌保护器", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"50\" y1=\"0\" x2=\"50\" y2=\"20\" stroke=\"currentColor\" stroke-width=\"3\"/><rect x=\"30\" y=\"20\" width=\"40\" height=\"30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"40\" font-size=\"12\" text-anchor=\"middle\" fill=\"currentColor\">SPD</text><line x1=\"50\" y1=\"50\" x2=\"50\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"40\" y1=\"70\" x2=\"60\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"43\" y1=\"75\" x2=\"57\" y2=\"75\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"46\" y1=\"80\" x2=\"54\" y2=\"80\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "line", "position": {"x": 50, "y": 0}, "type": "input", "label": "线路"}, {"id": "earth", "position": {"x": 50, "y": 100}, "type": "output", "label": "接地"}], "bindingSlots": [{"id": "status", "name": "状态", "dataType": "enum", "defaultValue": "normal", "enumValues": ["normal", "operating", "degraded", "fault"], "description": "浪涌保护器状态"}, {"id": "surge_count", "name": "浪涌次数", "dataType": "number", "defaultValue": 0, "description": "累计浪涌次数"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "overcurrent-relay", "category": "protection", "name": "过流继电器", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"20\" y=\"20\" width=\"60\" height=\"60\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"40\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">过流</text><text x=\"50\" y=\"55\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">继电器</text><circle cx=\"35\" cy=\"65\" r=\"5\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><circle cx=\"65\" cy=\"65\" r=\"5\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"0\" y1=\"30\" x2=\"20\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"30\" x2=\"100\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"80\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "current_in", "position": {"x": 0, "y": 30}, "type": "input", "label": "电流输入"}, {"id": "current_out", "position": {"x": 100, "y": 30}, "type": "output", "label": "电流输出"}, {"id": "trip_out", "position": {"x": 50, "y": 100}, "type": "output", "label": "跳闸输出"}], "bindingSlots": [{"id": "current", "name": "电流", "dataType": "number", "defaultValue": 0, "description": "检测电流 (A)"}, {"id": "pickup_current", "name": "动作电流", "dataType": "number", "defaultValue": 100, "description": "继电器动作电流 (A)"}, {"id": "status", "name": "状态", "dataType": "enum", "defaultValue": "normal", "enumValues": ["normal", "pickup", "trip"], "description": "继电器状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "differential-relay", "category": "protection", "name": "差动继电器", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"20\" y=\"20\" width=\"60\" height=\"60\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"40\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">差动</text><text x=\"50\" y=\"55\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">继电器</text><line x1=\"0\" y1=\"30\" x2=\"20\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"30\" x2=\"100\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"70\" x2=\"20\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"70\" x2=\"100\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"80\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "ct1_in", "position": {"x": 0, "y": 30}, "type": "input", "label": "CT1输入"}, {"id": "ct1_out", "position": {"x": 100, "y": 30}, "type": "output", "label": "CT1输出"}, {"id": "ct2_in", "position": {"x": 0, "y": 70}, "type": "input", "label": "CT2输入"}, {"id": "ct2_out", "position": {"x": 100, "y": 70}, "type": "output", "label": "CT2输出"}, {"id": "trip_out", "position": {"x": 50, "y": 100}, "type": "output", "label": "跳闸输出"}], "bindingSlots": [{"id": "differential_current", "name": "差流", "dataType": "number", "defaultValue": 0, "description": "差动电流 (A)"}, {"id": "restraint_current", "name": "制动电流", "dataType": "number", "defaultValue": 0, "description": "制动电流 (A)"}, {"id": "status", "name": "状态", "dataType": "enum", "defaultValue": "normal", "enumValues": ["normal", "pickup", "trip", "block"], "description": "继电器状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}]