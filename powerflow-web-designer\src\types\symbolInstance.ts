/**
 * Symbol instance types for PowerFlow Web Designer
 */

import type { Position, SymbolDefinition, BindingSlot } from './symbol';

// Binding value for a symbol instance
export interface BindingValue {
  slotId: string; // References the binding slot ID in the symbol definition
  value: any;
  deviceId?: string; // Optional reference to a device ID for real-time data
  measurementId?: string; // Optional reference to a measurement point for real-time data
}

// Symbol instance interface
export interface SymbolInstance {
  id: string;
  definitionId: string; // References the symbol definition ID
  position: Position;
  rotation: number; // Rotation in degrees
  scale: number; // Scale factor
  bindings: Record<string, BindingValue>; // Map of binding slot ID to binding value
  properties: Record<string, any>; // Instance-specific property overrides
  selected?: boolean; // UI state - whether the symbol is selected
}

// Factory function to create a new symbol instance
export function createSymbolInstance(
  id: string,
  definition: SymbolDefinition,
  position: Position,
  rotation: number = 0,
  scale: number = 1,
  bindings: Record<string, BindingValue> = {},
  properties: Record<string, any> = {}
): SymbolInstance {
  // Initialize default bindings from definition's binding slots
  const defaultBindings: Record<string, BindingValue> = {};
  definition.bindingSlots.forEach((slot: BindingSlot) => {
    defaultBindings[slot.id] = {
      slotId: slot.id,
      value: slot.defaultValue,
    };
  });

  return {
    id,
    definitionId: definition.id,
    position,
    rotation,
    scale,
    bindings: { ...defaultBindings, ...bindings },
    properties,
  };
}

// Function to get the absolute position of a connection point on a symbol instance
export function getConnectionPointPosition(
  instance: SymbolInstance,
  definition: SymbolDefinition,
  connectionPointId: string
): Position | null {
  const connectionPoint = definition.connectionPoints.find(cp => cp.id === connectionPointId);
  if (!connectionPoint) return null;

  // Calculate the rotated and scaled position
  const radians = (instance.rotation * Math.PI) / 180;
  const cos = Math.cos(radians);
  const sin = Math.sin(radians);

  // Adjust for the symbol's center point
  const centerX = definition.dimensions.width / 2;
  const centerY = definition.dimensions.height / 2;

  // Calculate the connection point position relative to the center
  const relX = (connectionPoint.position.x - centerX) * instance.scale;
  const relY = (connectionPoint.position.y - centerY) * instance.scale;

  // Rotate the point around the center
  const rotatedX = relX * cos - relY * sin;
  const rotatedY = relX * sin + relY * cos;

  // Add the instance position to get the absolute position
  return {
    x: instance.position.x + rotatedX + centerX * instance.scale,
    y: instance.position.y + rotatedY + centerY * instance.scale,
  };
}
