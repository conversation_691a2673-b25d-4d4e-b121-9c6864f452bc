<template>
  <div class="connection-annotation-editor">
    <div class="editor-header">
      <h3>{{ isNewAnnotation ? 'Add Annotation' : 'Edit Annotation' }}</h3>
      <a-button type="text" @click="$emit('close')">
        <template #icon><close-outlined /></template>
      </a-button>
    </div>
    
    <div class="editor-content">
      <!-- Annotation Type -->
      <div class="form-group">
        <label>Annotation Type</label>
        <a-select v-model:value="annotationType" style="width: 100%">
          <a-select-option :value="AnnotationType.TEXT">Text</a-select-option>
          <a-select-option :value="AnnotationType.ICON">Icon</a-select-option>
          <a-select-option :value="AnnotationType.ARROW">Arrow</a-select-option>
          <a-select-option :value="AnnotationType.CUSTOM">Custom</a-select-option>
        </a-select>
      </div>
      
      <!-- Annotation Content -->
      <div class="form-group">
        <label>{{ contentLabel }}</label>
        <template v-if="annotationType === AnnotationType.ICON">
          <a-select v-model:value="annotationContent" style="width: 100%">
            <a-select-option value="arrow-right">Arrow Right</a-select-option>
            <a-select-option value="arrow-left">Arrow Left</a-select-option>
            <a-select-option value="arrow-up">Arrow Up</a-select-option>
            <a-select-option value="arrow-down">Arrow Down</a-select-option>
            <a-select-option value="circle">Circle</a-select-option>
            <a-select-option value="square">Square</a-select-option>
            <a-select-option value="triangle">Triangle</a-select-option>
            <a-select-option value="star">Star</a-select-option>
            <a-select-option value="check">Check</a-select-option>
            <a-select-option value="times">X</a-select-option>
            <a-select-option value="info">Info</a-select-option>
            <a-select-option value="warning">Warning</a-select-option>
            <a-select-option value="error">Error</a-select-option>
            <a-select-option value="question">Question</a-select-option>
          </a-select>
        </template>
        <template v-else>
          <a-input v-model:value="annotationContent" :placeholder="`Enter ${contentLabel.toLowerCase()}`" />
        </template>
      </div>
      
      <!-- Annotation Position -->
      <div class="form-group">
        <label>Position</label>
        <a-select v-model:value="annotationPosition" style="width: 100%">
          <a-select-option :value="LabelPosition.START">Start</a-select-option>
          <a-select-option :value="LabelPosition.MIDDLE">Middle</a-select-option>
          <a-select-option :value="LabelPosition.END">End</a-select-option>
          <a-select-option :value="LabelPosition.CUSTOM">Custom</a-select-option>
        </a-select>
      </div>
      
      <!-- Custom Position Percentage (only if custom position is selected) -->
      <div class="form-group" v-if="annotationPosition === LabelPosition.CUSTOM">
        <label>Position Percentage (0-100)</label>
        <a-slider v-model:value="positionPercentage" :min="0" :max="100" />
      </div>
      
      <!-- Annotation Style -->
      <a-collapse>
        <a-collapse-panel key="1" header="Annotation Style">
          <!-- Text Color -->
          <div class="form-group">
            <label>Text/Icon Color</label>
            <a-input v-model:value="annotationStyle.textColor" placeholder="#000000">
              <template #prefix>
                <div class="color-preview" :style="{ backgroundColor: annotationStyle.textColor }"></div>
              </template>
            </a-input>
          </div>
          
          <!-- Background Color -->
          <div class="form-group">
            <label>Background Color</label>
            <a-input v-model:value="annotationStyle.backgroundColor" placeholder="#ffffff">
              <template #prefix>
                <div class="color-preview" :style="{ backgroundColor: annotationStyle.backgroundColor }"></div>
              </template>
            </a-input>
          </div>
          
          <!-- Border Color -->
          <div class="form-group">
            <label>Border Color</label>
            <a-input v-model:value="annotationStyle.borderColor" placeholder="#cccccc">
              <template #prefix>
                <div class="color-preview" :style="{ backgroundColor: annotationStyle.borderColor }"></div>
              </template>
            </a-input>
          </div>
          
          <!-- Border Width -->
          <div class="form-group">
            <label>Border Width</label>
            <a-input-number v-model:value="annotationStyle.borderWidth" :min="0" :max="5" style="width: 100%" />
          </div>
          
          <!-- Border Radius -->
          <div class="form-group">
            <label>Border Radius</label>
            <a-input-number v-model:value="annotationStyle.borderRadius" :min="0" :max="10" style="width: 100%" />
          </div>
          
          <!-- Padding -->
          <div class="form-group">
            <label>Padding</label>
            <a-input-number v-model:value="annotationStyle.padding" :min="0" :max="10" style="width: 100%" />
          </div>
          
          <!-- Rotation -->
          <div class="form-group">
            <label>Rotation (degrees)</label>
            <a-input-number v-model:value="annotationStyle.rotation" :min="-180" :max="180" style="width: 100%" />
          </div>
          
          <!-- Scale -->
          <div class="form-group">
            <label>Scale</label>
            <a-input-number v-model:value="annotationStyle.scale" :min="0.5" :max="3" :step="0.1" style="width: 100%" />
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
    
    <div class="editor-footer">
      <a-button @click="$emit('close')">Cancel</a-button>
      <a-button type="primary" @click="saveAnnotation">Save</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import { ConnectionAnnotation, AnnotationType, LabelPosition, defaultAnnotationStyle } from '@/types/connection';
import { v4 as uuidv4 } from 'uuid';

// Props
const props = defineProps<{
  annotation?: ConnectionAnnotation;
  connectionId: string;
}>();

// Emits
const emit = defineEmits<{
  (e: 'save', annotation: ConnectionAnnotation): void;
  (e: 'close'): void;
}>();

// Computed
const isNewAnnotation = computed(() => !props.annotation);

const contentLabel = computed(() => {
  switch (annotationType.value) {
    case AnnotationType.TEXT:
      return 'Text';
    case AnnotationType.ICON:
      return 'Icon';
    case AnnotationType.ARROW:
      return 'Arrow Direction';
    case AnnotationType.CUSTOM:
      return 'Content';
    default:
      return 'Content';
  }
});

// State
const annotationType = ref(props.annotation?.type || AnnotationType.TEXT);
const annotationContent = ref(props.annotation?.content || '');
const annotationPosition = ref(props.annotation?.position || LabelPosition.MIDDLE);
const positionPercentage = ref(props.annotation?.positionPercentage || 50);
const annotationStyle = ref({
  textColor: props.annotation?.style?.textColor || defaultAnnotationStyle.textColor,
  backgroundColor: props.annotation?.style?.backgroundColor || defaultAnnotationStyle.backgroundColor,
  borderColor: props.annotation?.style?.borderColor || defaultAnnotationStyle.borderColor,
  borderWidth: props.annotation?.style?.borderWidth || defaultAnnotationStyle.borderWidth,
  borderRadius: props.annotation?.style?.borderRadius || defaultAnnotationStyle.borderRadius,
  padding: props.annotation?.style?.padding || defaultAnnotationStyle.padding,
  rotation: props.annotation?.style?.rotation || 0,
  scale: props.annotation?.style?.scale || 1,
});

// Watch for changes in annotation type
watch(annotationType, (newType) => {
  // Set default content based on type
  if (newType === AnnotationType.ICON && !annotationContent.value) {
    annotationContent.value = 'arrow-right';
  } else if (newType === AnnotationType.ARROW && !annotationContent.value) {
    annotationContent.value = 'right';
  }
});

// Methods
const saveAnnotation = () => {
  const annotation: ConnectionAnnotation = {
    id: props.annotation?.id || `${props.connectionId}-annotation-${uuidv4()}`,
    type: annotationType.value,
    content: annotationContent.value,
    position: annotationPosition.value,
    positionPercentage: annotationPosition.value === LabelPosition.CUSTOM ? positionPercentage.value : undefined,
    offset: props.annotation?.offset,
    style: {
      textColor: annotationStyle.value.textColor,
      backgroundColor: annotationStyle.value.backgroundColor,
      borderColor: annotationStyle.value.borderColor,
      borderWidth: annotationStyle.value.borderWidth,
      borderRadius: annotationStyle.value.borderRadius,
      padding: annotationStyle.value.padding,
      rotation: annotationStyle.value.rotation,
      scale: annotationStyle.value.scale,
    },
  };
  
  emit('save', annotation);
};
</script>

<style scoped>
.connection-annotation-editor {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.editor-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.editor-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}

.color-preview {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
}
</style>
