<template>
  <v-label
    :config="{
      x: position.x,
      y: position.y,
      text: label.text,
      fontFamily: labelStyle.fontFamily,
      fontSize: labelStyle.fontSize,
      fontStyle: labelStyle.fontStyle,
      fontVariant: labelStyle.fontWeight === 'bold' ? 'bold' : 'normal',
      fill: labelStyle.textColor,
      padding: labelStyle.padding,
      rotation: labelStyle.rotation || 0,
      cornerRadius: labelStyle.borderRadius,
      align: 'center',
      verticalAlign: 'middle',
      width: 'auto',
      height: 'auto',
      listening: true,
      draggable: !readOnly,
    }"
    @dragstart="handleDragStart"
    @dragmove="handleDragMove"
    @dragend="handleDragEnd"
    @click="handleClick"
    @dblclick="handleDoubleClick"
  />
  <v-tag
    v-if="labelStyle.backgroundColor || labelStyle.borderColor"
    :config="{
      x: position.x,
      y: position.y,
      fill: labelStyle.backgroundColor,
      stroke: labelStyle.borderColor,
      strokeWidth: labelStyle.borderWidth,
      cornerRadius: labelStyle.borderRadius,
      width: labelWidth,
      height: labelHeight,
      rotation: labelStyle.rotation || 0,
      offsetX: labelWidth / 2,
      offsetY: labelHeight / 2,
      shadowColor: selected ? '#1890ff' : undefined,
      shadowBlur: selected ? 5 : undefined,
      shadowOffset: selected ? { x: 0, y: 0 } : undefined,
      shadowOpacity: selected ? 0.5 : undefined,
      listening: false,
    }"
  />
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ConnectionLabel, LabelPosition, LabelAlignment } from '@/types/connection';
import { Position } from '@/types/symbol';

// Props
const props = defineProps<{
  label: ConnectionLabel;
  points: Position[];
  readOnly?: boolean;
  selected?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:label', label: ConnectionLabel): void;
  (e: 'select', labelId: string): void;
  (e: 'edit', labelId: string): void;
}>();

// State
const position = ref<Position>({ x: 0, y: 0 });
const labelWidth = ref(0);
const labelHeight = ref(0);
const dragStartPosition = ref<Position | null>(null);

// Computed
const labelStyle = computed(() => {
  return {
    fontFamily: props.label.style?.fontFamily || 'Arial',
    fontSize: props.label.style?.fontSize || 12,
    fontWeight: props.label.style?.fontWeight || 'normal',
    fontStyle: props.label.style?.fontStyle || 'normal',
    textColor: props.label.style?.textColor || '#000000',
    backgroundColor: props.label.style?.backgroundColor || '#ffffff',
    borderColor: props.label.style?.borderColor || '#cccccc',
    borderWidth: props.label.style?.borderWidth || 1,
    borderRadius: props.label.style?.borderRadius || 4,
    padding: props.label.style?.padding || 4,
    rotation: props.label.style?.rotation || 0,
  };
});

// Calculate the position of the label based on the connection points
const calculateLabelPosition = () => {
  if (!props.points || props.points.length < 2) {
    return { x: 0, y: 0 };
  }

  let pos: Position;
  const points = props.points;

  // Calculate position based on label position type
  switch (props.label.position) {
    case LabelPosition.START:
      pos = calculatePositionAtPercentage(0);
      break;
    case LabelPosition.END:
      pos = calculatePositionAtPercentage(100);
      break;
    case LabelPosition.CUSTOM:
      pos = calculatePositionAtPercentage(props.label.positionPercentage || 50);
      break;
    case LabelPosition.MIDDLE:
    default:
      pos = calculatePositionAtPercentage(50);
      break;
  }

  // Apply alignment
  const alignment = props.label.alignment || LabelAlignment.ABOVE;
  const segment = findSegmentAtPercentage(props.label.positionPercentage || 50);
  
  if (segment) {
    const { start, end } = segment;
    const angle = Math.atan2(end.y - start.y, end.x - start.x);
    const perpendicular = angle + Math.PI / 2;
    
    const distance = 15; // Distance from the line
    
    if (alignment === LabelAlignment.ABOVE) {
      pos.x += Math.cos(perpendicular) * distance;
      pos.y += Math.sin(perpendicular) * distance;
    } else if (alignment === LabelAlignment.BELOW) {
      pos.x -= Math.cos(perpendicular) * distance;
      pos.y -= Math.sin(perpendicular) * distance;
    }
  }

  // Apply custom offset if provided
  if (props.label.offset) {
    pos.x += props.label.offset.x;
    pos.y += props.label.offset.y;
  }

  return pos;
};

// Calculate position at a specific percentage along the connection
const calculatePositionAtPercentage = (percentage: number): Position => {
  const points = props.points;
  if (!points || points.length < 2) {
    return { x: 0, y: 0 };
  }

  // Calculate total length of the connection
  let totalLength = 0;
  const segmentLengths: number[] = [];

  for (let i = 0; i < points.length - 1; i++) {
    const length = calculateDistance(points[i], points[i + 1]);
    segmentLengths.push(length);
    totalLength += length;
  }

  // Calculate target distance
  const targetDistance = (percentage / 100) * totalLength;

  // Find the segment and position
  let currentDistance = 0;
  for (let i = 0; i < segmentLengths.length; i++) {
    const segmentLength = segmentLengths[i];
    
    if (currentDistance + segmentLength >= targetDistance) {
      // Calculate position within this segment
      const segmentPercentage = (targetDistance - currentDistance) / segmentLength;
      const start = points[i];
      const end = points[i + 1];
      
      return {
        x: start.x + (end.x - start.x) * segmentPercentage,
        y: start.y + (end.y - start.y) * segmentPercentage,
      };
    }
    
    currentDistance += segmentLength;
  }

  // Fallback to the last point
  return points[points.length - 1];
};

// Find the segment at a specific percentage along the connection
const findSegmentAtPercentage = (percentage: number): { start: Position, end: Position } | null => {
  const points = props.points;
  if (!points || points.length < 2) {
    return null;
  }

  // Calculate total length of the connection
  let totalLength = 0;
  const segmentLengths: number[] = [];

  for (let i = 0; i < points.length - 1; i++) {
    const length = calculateDistance(points[i], points[i + 1]);
    segmentLengths.push(length);
    totalLength += length;
  }

  // Calculate target distance
  const targetDistance = (percentage / 100) * totalLength;

  // Find the segment
  let currentDistance = 0;
  for (let i = 0; i < segmentLengths.length; i++) {
    const segmentLength = segmentLengths[i];
    
    if (currentDistance + segmentLength >= targetDistance) {
      return {
        start: points[i],
        end: points[i + 1],
      };
    }
    
    currentDistance += segmentLength;
  }

  // Fallback to the last segment
  return {
    start: points[points.length - 2],
    end: points[points.length - 1],
  };
};

// Calculate distance between two points
const calculateDistance = (p1: Position, p2: Position): number => {
  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
};

// Event handlers
const handleDragStart = (e: any) => {
  if (props.readOnly) return;
  
  dragStartPosition.value = { ...position.value };
};

const handleDragMove = (e: any) => {
  if (props.readOnly) return;
  
  const newPos = { x: e.target.x(), y: e.target.y() };
  position.value = newPos;
};

const handleDragEnd = (e: any) => {
  if (props.readOnly) return;
  
  const newPos = { x: e.target.x(), y: e.target.y() };
  
  // Calculate the offset from the original position
  if (dragStartPosition.value) {
    const dx = newPos.x - dragStartPosition.value.x;
    const dy = newPos.y - dragStartPosition.value.y;
    
    // Update the label with the new offset
    const updatedLabel = { 
      ...props.label,
      offset: { 
        x: (props.label.offset?.x || 0) + dx,
        y: (props.label.offset?.y || 0) + dy,
      }
    };
    
    emit('update:label', updatedLabel);
  }
  
  dragStartPosition.value = null;
};

const handleClick = (e: any) => {
  emit('select', props.label.id);
};

const handleDoubleClick = (e: any) => {
  emit('edit', props.label.id);
};

// Calculate initial position
onMounted(() => {
  position.value = calculateLabelPosition();
  
  // Estimate label dimensions based on text length and font size
  const textLength = props.label.text.length;
  const fontSize = labelStyle.value.fontSize;
  const padding = labelStyle.value.padding;
  
  labelWidth.value = textLength * fontSize * 0.6 + padding * 2;
  labelHeight.value = fontSize * 1.2 + padding * 2;
});

// Watch for changes in points or label
watch(() => props.points, () => {
  position.value = calculateLabelPosition();
}, { deep: true });

watch(() => props.label, () => {
  position.value = calculateLabelPosition();
  
  // Update label dimensions
  const textLength = props.label.text.length;
  const fontSize = labelStyle.value.fontSize;
  const padding = labelStyle.value.padding;
  
  labelWidth.value = textLength * fontSize * 0.6 + padding * 2;
  labelHeight.value = fontSize * 1.2 + padding * 2;
}, { deep: true });
</script>
