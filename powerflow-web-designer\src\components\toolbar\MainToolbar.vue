<template>
  <div class="main-toolbar">
    <div class="toolbar-section file-operations">
      <a-button-group>
        <a-tooltip title="画面管理">
          <a-button @click="$emit('manage-diagrams')">
            <template #icon><folder-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="$locale.t('toolbar.save')">
          <a-button @click="$emit('save')">
            <template #icon><save-outlined /></template>
          </a-button>
        </a-tooltip>
      </a-button-group>
    </div>

    <div class="toolbar-section edit-operations">
      <a-button-group>
        <a-tooltip :title="$locale.t('toolbar.undo')">
          <a-button
            :disabled="!canUndo"
            @click="$emit('undo')"
          >
            <template #icon><undo-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="$locale.t('toolbar.redo')">
          <a-button
            :disabled="!canRedo"
            @click="$emit('redo')"
          >
            <template #icon><redo-outlined /></template>
          </a-button>
        </a-tooltip>
      </a-button-group>

      <a-button-group style="margin-left: 4px;">
        <a-tooltip :title="$locale.t('toolbar.cut')">
          <a-button
            :disabled="!hasSelection"
            @click="$emit('cut')"
          >
            <template #icon><scissor-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="$locale.t('toolbar.copy')">
          <a-button
            :disabled="!hasSelection"
            @click="$emit('copy')"
          >
            <template #icon><copy-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="$locale.t('toolbar.paste')">
          <a-button
            :disabled="!canPaste"
            @click="$emit('paste')"
          >
            <template #icon><file-text-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="$locale.t('toolbar.delete')">
          <a-button
            :disabled="!hasSelection"
            @click="$emit('delete')"
          >
            <template #icon><delete-outlined /></template>
          </a-button>
        </a-tooltip>
      </a-button-group>

      <!-- Rotation controls -->
      <a-button-group style="margin-left: 4px;" v-if="hasSelection">
        <a-tooltip title="Rotate Counterclockwise (90°)">
          <a-button
            :disabled="!hasSelection"
            @click="$emit('rotate-counterclockwise')"
          >
            <template #icon><rotate-left-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="Rotate Clockwise (90°)">
          <a-button
            :disabled="!hasSelection"
            @click="$emit('rotate-clockwise')"
          >
            <template #icon><rotate-right-outlined /></template>
          </a-button>
        </a-tooltip>
      </a-button-group>

      <!-- Text tool -->
      <a-button-group style="margin-left: 4px;">
        <a-tooltip title="Add Text Element">
          <a-button
            :class="{ 'text-tool-active': textToolActive }"
            @click="$emit('toggle-text-tool')"
          >
            <template #icon><font-size-outlined /></template>
          </a-button>
        </a-tooltip>
      </a-button-group>

      <!-- Multi-selection indicator -->
      <div v-if="hasMultipleSelection" class="selection-indicator">
        <span class="selection-count">{{ selectionCountText }}</span>
      </div>
    </div>

    <div class="toolbar-section view-operations">
      <a-button-group>
        <a-tooltip :title="$locale.t('toolbar.zoomIn')">
          <a-button @click="$emit('zoom-in')">
            <template #icon><zoom-in-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="$locale.t('toolbar.zoomOut')">
          <a-button @click="$emit('zoom-out')">
            <template #icon><zoom-out-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="$locale.t('toolbar.fitContent')">
          <a-button @click="$emit('fit-content')">
            <template #icon><fullscreen-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="$locale.t('toolbar.resetView')">
          <a-button @click="$emit('reset-view')">
            <template #icon><border-outlined /></template>
          </a-button>
        </a-tooltip>


      </a-button-group>

      <a-button-group style="margin-left: 4px;">
        <a-tooltip :title="gridVisible ? 'Hide Grid' : 'Show Grid'">
          <a-button @click="toggleGridVisibility">
            <template #icon><border-outlined :style="{ color: gridVisible ? '#1890ff' : '' }" /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="snapToGrid ? 'Disable Snap to Grid' : 'Enable Snap to Grid'">
          <a-button @click="toggleSnapToGrid">
            <template #icon><align-center-outlined :style="{ color: snapToGrid ? '#1890ff' : '' }" /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="Grid Settings">
          <a-button @click="$emit('show-grid-settings')">
            <template #icon><setting-outlined /></template>
          </a-button>
        </a-tooltip>
      </a-button-group>

      <!-- Panel toggle controls -->
      <a-button-group style="margin-left: 4px;">
        <a-tooltip title="Toggle Symbol Library Panel">
          <a-button @click="$emit('toggle-left-panel')">
            <template #icon><menu-unfold-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="Toggle Properties Panel">
          <a-button @click="$emit('toggle-right-panel')">
            <template #icon><menu-fold-outlined /></template>
          </a-button>
        </a-tooltip>
      </a-button-group>
    </div>

    <div class="toolbar-divider"></div>

    <div class="toolbar-section simulation-section">
      <simulation-button />
    </div>

    <div class="toolbar-section navigation">
      <a-button-group>
        <a-tooltip :title="$locale.t('toolbar.viewMode')">
          <a-button @click="$emit('view')">
            <template #icon><eye-outlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip :title="$locale.t('toolbar.home')">
          <a-button @click="$emit('home')">
            <template #icon><home-outlined /></template>
          </a-button>
        </a-tooltip>
        <slot name="keyboard-shortcuts"></slot>
      </a-button-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  FolderOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  ScissorOutlined,
  CopyOutlined,
  FileTextOutlined,
  DeleteOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  BorderOutlined,
  EyeOutlined,
  HomeOutlined,
  AlignCenterOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  FontSizeOutlined
} from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';
import SimulationButton from './SimulationButton.vue';

// Props
interface Props {
  textToolActive?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  textToolActive: false,
});

// Define emits
const emit = defineEmits<{
  (e: 'manage-diagrams'): void;
  (e: 'save'): void;
  (e: 'undo'): void;
  (e: 'redo'): void;
  (e: 'cut'): void;
  (e: 'copy'): void;
  (e: 'paste'): void;
  (e: 'delete'): void;
  (e: 'zoom-in'): void;
  (e: 'zoom-out'): void;
  (e: 'fit-content'): void;
  (e: 'reset-view'): void;
  (e: 'rotate-clockwise'): void;
  (e: 'rotate-counterclockwise'): void;
  (e: 'toggle-text-tool'): void;
  (e: 'toggle-left-panel'): void;
  (e: 'toggle-right-panel'): void;
  (e: 'show-grid-settings'): void;
  (e: 'view'): void;
  (e: 'home'): void;
}>();

// Store
const diagramStore = useDiagramStore();

// Computed
const canUndo = computed(() => diagramStore.canUndo);
const canRedo = computed(() => diagramStore.canRedo);
const hasSelection = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  if (!diagramStore.currentDiagram.selectedSymbolIds ||
      !diagramStore.currentDiagram.selectedConnectionIds ||
      !diagramStore.currentDiagram.selectedTextElementIds ||
      !diagramStore.currentDiagram.selectedGroupIds) return false;

  return (
    diagramStore.currentDiagram.selectedSymbolIds.length > 0 ||
    diagramStore.currentDiagram.selectedConnectionIds.length > 0 ||
    diagramStore.currentDiagram.selectedTextElementIds.length > 0 ||
    diagramStore.currentDiagram.selectedGroupIds.length > 0
  );
});

const hasMultipleSelection = computed(() => {
  if (!diagramStore.currentDiagram) return false;

  const totalSelected = (
    (diagramStore.currentDiagram.selectedSymbolIds?.length || 0) +
    (diagramStore.currentDiagram.selectedConnectionIds?.length || 0) +
    (diagramStore.currentDiagram.selectedTextElementIds?.length || 0) +
    (diagramStore.currentDiagram.selectedGroupIds?.length || 0)
  );

  return totalSelected > 1;
});

const selectionCountText = computed(() => {
  if (!diagramStore.currentDiagram || !hasSelection.value) return '';

  const symbolCount = diagramStore.currentDiagram.selectedSymbolIds?.length || 0;
  const connectionCount = diagramStore.currentDiagram.selectedConnectionIds?.length || 0;
  const textElementCount = diagramStore.currentDiagram.selectedTextElementIds?.length || 0;
  const groupCount = diagramStore.currentDiagram.selectedGroupIds?.length || 0;

  const total = symbolCount + connectionCount + textElementCount + groupCount;
  return `${total} selected`;
});

// This would be implemented with a clipboard store in a real application
const canPaste = computed(() => true);

// Grid settings
const gridVisible = computed(() => {
  if (!diagramStore.currentDiagram) return true;
  return diagramStore.currentDiagram.grid.visible;
});

const snapToGrid = computed(() => {
  if (!diagramStore.currentDiagram) return true;
  return diagramStore.currentDiagram.grid.snapToGrid;
});

// Grid toggle methods
const toggleGridVisibility = () => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.updateGridSettings({ visible: !gridVisible.value });
};

const toggleSnapToGrid = () => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.updateGridSettings({ snapToGrid: !snapToGrid.value });
};
</script>

<style scoped>
.main-toolbar {
  display: flex;
  align-items: center;
  background-color: var(--header-background-color);
  padding: 4px 12px;
  height: 36px;
  overflow: visible;
}

.toolbar-section {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

/* 确保按钮组之间有足够的间距 */
:deep(.ant-btn-group) {
  margin-right: 4px;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 12px;
}

.simulation-section {
  flex: 1;
}

.navigation {
  margin-left: auto;
}

/* 为了在蓝色背景上更好地显示按钮 */
:deep(.ant-btn) {
  color: white;
  border-color: rgba(255, 255, 255, 0.7);
  background-color: rgba(255, 255, 255, 0.15);
  height: 28px;
  padding: 0 8px;
}

/* Selection indicator styles */
.selection-indicator {
  display: flex;
  align-items: center;
  margin-left: 8px;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.selection-count {
  color: white;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

:deep(.ant-btn:hover) {
  color: white;
  border-color: white;
  background-color: rgba(255, 255, 255, 0.25);
}

:deep(.ant-btn[disabled]) {
  color: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 确保图标正常显示 */
:deep(.anticon) {
  font-size: 14px;
  vertical-align: middle;
  line-height: 1;
}

/* Text tool active state */
:deep(.text-tool-active) {
  background-color: rgba(24, 144, 255, 0.3) !important;
  border-color: #1890ff !important;
}
</style>
