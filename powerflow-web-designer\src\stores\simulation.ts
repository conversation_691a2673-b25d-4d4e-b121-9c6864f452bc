import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useDiagramStore } from './diagram';
import { PowerFlowAnimationType, PowerFlowDirection } from '@/types/connection';

// Simulation mode
export enum SimulationMode {
  STEADY_STATE = 'steady-state',
  TRANSIENT = 'transient',
  FAULT = 'fault',
}

// Simulation settings
export interface SimulationSettings {
  mode: SimulationMode | string;
  timeStep: number; // in milliseconds
  maxTime: number; // in seconds
  autoStop: boolean;
  showAnimation: boolean;
}

// Simulation state for a connection
export interface ConnectionSimulationState {
  connectionId: string;
  power: number; // Power flow in the connection (positive or negative)
  current: number; // Current in the connection
  voltage: number; // Voltage across the connection
  losses: number; // Power losses in the connection
  direction: PowerFlowDirection; // Direction of power flow
}

// Simulation state for a symbol
export interface SymbolSimulationState {
  symbolInstanceId: string;
  voltage: number; // Voltage at the symbol
  power: number; // Power at the symbol
  status: 'on' | 'off' | 'fault'; // Status of the symbol
  temperature: number; // Temperature of the symbol
  efficiency: number; // Efficiency of the symbol
  // Historical data for trend charts
  history?: {
    voltage: number[];
    power: number[];
    temperature: number[];
    efficiency: number[];
    [key: string]: number[];
  };
}

// Simulation results at a specific time
export interface SimulationTimeStep {
  time: number; // Simulation time in seconds
  connections: Record<string, ConnectionSimulationState>; // Connection states indexed by connection ID
  symbols: Record<string, SymbolSimulationState>; // Symbol states indexed by symbol instance ID
}

// Simulation results
export interface SimulationResults {
  diagramId: string;
  settings: SimulationSettings;
  timeSteps: SimulationTimeStep[];
}

// Default simulation settings
const defaultSettings: SimulationSettings = {
  mode: SimulationMode.STEADY_STATE,
  timeStep: 100, // 100ms
  maxTime: 60, // 60 seconds
  autoStop: true,
  showAnimation: true,
};

export const useSimulationStore = defineStore('simulation', () => {
  // Stores
  const diagramStore = useDiagramStore();

  // State
  const isRunning = ref(false);
  const isPaused = ref(false);
  const currentTime = ref(0);
  const simulationSpeed = ref(5); // 1-10 scale
  const settings = ref<SimulationSettings>({ ...defaultSettings });
  const results = ref<SimulationResults | null>(null);
  const simulationInterval = ref<number | null>(null);

  // Computed
  const hasResults = computed(() => results.value !== null && results.value.timeSteps.length > 0);

  // Get the current simulation state
  const getCurrentState = computed(() => {
    if (!results.value || results.value.timeSteps.length === 0) {
      return null;
    }

    return results.value.timeSteps[results.value.timeSteps.length - 1];
  });

  // Start the simulation
  const startSimulation = (options: { diagramId: string; speed?: number; settings?: Partial<SimulationSettings> }) => {
    // Stop any existing simulation
    stopSimulation();

    // Update settings if provided
    if (options.settings) {
      settings.value = {
        ...settings.value,
        ...options.settings,
      };
    }

    // Update speed if provided
    if (options.speed !== undefined) {
      simulationSpeed.value = options.speed;
    }

    // Initialize results
    results.value = {
      diagramId: options.diagramId,
      settings: { ...settings.value },
      timeSteps: [],
    };

    // Reset time
    currentTime.value = 0;

    // Set state
    isRunning.value = true;
    isPaused.value = false;

    // Initialize the simulation
    initializeSimulation(options.diagramId);

    // Start the simulation loop
    const timeStep = settings.value.timeStep / 1000; // Convert to seconds
    const interval = Math.max(10, settings.value.timeStep / simulationSpeed.value);

    simulationInterval.value = window.setInterval(() => {
      if (!isRunning.value || isPaused.value) return;

      // Update the simulation
      updateSimulation(timeStep);

      // Check if we've reached the max time
      if (settings.value.autoStop && currentTime.value >= settings.value.maxTime) {
        stopSimulation();
      }
    }, interval);
  };

  // Pause the simulation
  const pauseSimulation = () => {
    isPaused.value = true;
  };

  // Resume the simulation
  const resumeSimulation = () => {
    if (!isRunning.value) return;
    isPaused.value = false;
  };

  // Stop the simulation
  const stopSimulation = () => {
    isRunning.value = false;
    isPaused.value = false;

    if (simulationInterval.value !== null) {
      clearInterval(simulationInterval.value);
      simulationInterval.value = null;
    }

    // Apply final state to the diagram
    if (results.value && results.value.timeSteps.length > 0) {
      applySimulationState(results.value.timeSteps[results.value.timeSteps.length - 1]);
    }
  };

  // Reset the simulation
  const resetSimulation = () => {
    stopSimulation();
    currentTime.value = 0;
    results.value = null;

    // Reset animation on all connections
    if (diagramStore.currentDiagram) {
      Object.values(diagramStore.currentDiagram.connections).forEach(connection => {
        if (connection.style.animation) {
          diagramStore.updateConnection({
            ...connection,
            style: {
              ...connection.style,
              animation: {
                ...connection.style.animation,
                enabled: false,
              },
            },
          });
        }
      });
    }
  };

  // Step the simulation forward by one time step
  const stepSimulation = (options: { diagramId: string; timeStep?: number }) => {
    if (!results.value) {
      // Initialize the simulation if it hasn't been started
      startSimulation({ diagramId: options.diagramId });
      pauseSimulation();
    }

    const timeStep = options.timeStep || settings.value.timeStep / 1000;
    updateSimulation(timeStep);
  };

  // Set the simulation speed
  const setSimulationSpeed = (speed: number) => {
    simulationSpeed.value = speed;

    // Update the interval if the simulation is running
    if (isRunning.value && simulationInterval.value !== null) {
      clearInterval(simulationInterval.value);

      const interval = Math.max(10, settings.value.timeStep / simulationSpeed.value);
      simulationInterval.value = window.setInterval(() => {
        if (!isRunning.value || isPaused.value) return;

        // Update the simulation
        const timeStep = settings.value.timeStep / 1000;
        updateSimulation(timeStep);

        // Check if we've reached the max time
        if (settings.value.autoStop && currentTime.value >= settings.value.maxTime) {
          stopSimulation();
        }
      }, interval);
    }
  };

  // Update simulation settings
  const updateSettings = (newSettings: Partial<SimulationSettings>) => {
    settings.value = {
      ...settings.value,
      ...newSettings,
    };
  };

  // Get simulation results
  const getResults = () => {
    return results.value;
  };

  // Initialize the simulation
  const initializeSimulation = (diagramId: string) => {
    const diagram = diagramStore.getDiagram(diagramId);
    if (!diagram) return;

    // Initialize connection states
    const connections: Record<string, ConnectionSimulationState> = {};
    Object.values(diagram.connections).forEach(connection => {
      connections[connection.id] = {
        connectionId: connection.id,
        power: 0,
        current: 0,
        voltage: 0,
        losses: 0,
        direction: PowerFlowDirection.FORWARD,
      };
    });

    // Initialize symbol states
    const symbols: Record<string, SymbolSimulationState> = {};
    Object.values(diagram.symbolInstances).forEach(symbol => {
      symbols[symbol.id] = {
        symbolInstanceId: symbol.id,
        voltage: 0,
        power: 0,
        status: 'off',
        temperature: 25, // Ambient temperature
        efficiency: 1, // 100% efficiency,
        // Initialize history arrays for trend charts
        history: {
          voltage: [],
          power: [],
          temperature: [],
          efficiency: [],
        }
      };
    });

    // Add initial time step
    if (results.value) {
      results.value.timeSteps.push({
        time: 0,
        connections,
        symbols,
      });
    }
  };

  // Update the simulation by one time step
  const updateSimulation = (timeStep: number) => {
    if (!results.value || results.value.timeSteps.length === 0) return;

    // Get the current state
    const currentState = results.value.timeSteps[results.value.timeSteps.length - 1];

    // Create a new state for this time step
    const newTime = currentTime.value + timeStep;
    const newConnections = { ...currentState.connections };
    const newSymbols = { ...currentState.symbols };

    // Simulate power flow (this is a simplified example)
    // In a real application, this would involve solving power flow equations
    Object.values(newConnections).forEach(connection => {
      // Simple random fluctuation for demo purposes
      connection.power = Math.max(0, connection.power + (Math.random() - 0.5) * 10);
      connection.current = connection.power / 1000; // Simplified I = P/V
      connection.voltage = 1000; // Assume 1kV
      connection.losses = connection.current * connection.current * 0.1; // Simplified P_loss = I²R

      // Update direction based on power flow
      if (connection.power > 0) {
        connection.direction = PowerFlowDirection.FORWARD;
      } else if (connection.power < 0) {
        connection.direction = PowerFlowDirection.BACKWARD;
      } else {
        connection.direction = PowerFlowDirection.BIDIRECTIONAL;
      }

      // Update the connection animation in the diagram
      const diagramConnection = diagramStore.currentDiagram?.connections[connection.connectionId];
      if (diagramConnection && settings.value.showAnimation) {
        diagramStore.updateConnection({
          ...diagramConnection,
          style: {
            ...diagramConnection.style,
            animation: {
              type: PowerFlowAnimationType.FLOW,
              direction: connection.direction,
              speed: Math.min(10, Math.max(1, Math.abs(connection.power) / 10)),
              enabled: true,
            },
          },
        });
      }
    });

    // Update symbol states
    Object.values(newSymbols).forEach(symbol => {
      // Simple random fluctuation for demo purposes
      symbol.voltage = 1000 + (Math.random() - 0.5) * 50;
      symbol.power = Math.max(0, symbol.power + (Math.random() - 0.5) * 20);
      symbol.temperature = 25 + symbol.power / 100;
      symbol.status = symbol.power > 10 ? 'on' : 'off';

      // Update history arrays for trend charts
      if (symbol.history) {
        // Add current values to history
        symbol.history.voltage.push(symbol.voltage);
        symbol.history.power.push(symbol.power);
        symbol.history.temperature.push(symbol.temperature);
        symbol.history.efficiency.push(symbol.efficiency);

        // Limit history length to prevent memory issues (keep last 100 points)
        const maxHistoryLength = 100;
        if (symbol.history.voltage.length > maxHistoryLength) {
          symbol.history.voltage = symbol.history.voltage.slice(-maxHistoryLength);
          symbol.history.power = symbol.history.power.slice(-maxHistoryLength);
          symbol.history.temperature = symbol.history.temperature.slice(-maxHistoryLength);
          symbol.history.efficiency = symbol.history.efficiency.slice(-maxHistoryLength);
        }
      }
    });

    // Add the new time step to the results
    results.value.timeSteps.push({
      time: newTime,
      connections: newConnections,
      symbols: newSymbols,
    });

    // Update the current time
    currentTime.value = newTime;

    // Apply the new state to the diagram
    applySimulationState({
      time: newTime,
      connections: newConnections,
      symbols: newSymbols,
    });
  };

  // Apply a simulation state to the diagram
  const applySimulationState = (state: SimulationTimeStep) => {
    if (!diagramStore.currentDiagram) return;

    // Update connection animations
    Object.values(state.connections).forEach(connection => {
      const diagramConnection = diagramStore.currentDiagram?.connections[connection.connectionId];
      if (diagramConnection && settings.value.showAnimation) {
        diagramStore.updateConnection({
          ...diagramConnection,
          style: {
            ...diagramConnection.style,
            animation: {
              type: PowerFlowAnimationType.FLOW,
              direction: connection.direction,
              speed: Math.min(10, Math.max(1, Math.abs(connection.power) / 10)),
              enabled: isRunning.value && !isPaused.value,
              color: getColorForPower(connection.power),
            },
          },
        });
      }
    });

    // Update symbol states (in a real application, this would update visual properties)
    // This is left as a placeholder for future implementation
  };

  // Helper function to get color based on power level
  const getColorForPower = (power: number): string => {
    if (power <= 0) return '#cccccc'; // No power flow
    if (power < 50) return '#00ff00'; // Low power (green)
    if (power < 100) return '#ffff00'; // Medium power (yellow)
    return '#ff0000'; // High power (red)
  };

  // Get historical data for a specific symbol and parameter
  const getSymbolHistory = (symbolId: string, parameter: string, maxPoints: number = 100): number[] => {
    if (!results.value || results.value.timeSteps.length === 0) return [];

    // Get the current state
    const currentState = getCurrentState.value;
    if (!currentState || !currentState.symbols[symbolId]) return [];

    // Get the symbol state
    const symbolState = currentState.symbols[symbolId];

    // Check if history is available
    if (!symbolState.history || !symbolState.history[parameter]) return [];

    // Return the history (limited to maxPoints)
    return symbolState.history[parameter].slice(-maxPoints);
  };

  return {
    // State
    isRunning,
    isPaused,
    currentTime,
    simulationSpeed,
    settings,

    // Computed
    hasResults,
    getCurrentState,

    // Actions
    startSimulation,
    pauseSimulation,
    resumeSimulation,
    stopSimulation,
    resetSimulation,
    stepSimulation,
    setSimulationSpeed,
    updateSettings,
    getResults,
    getSymbolHistory,
  };
});
