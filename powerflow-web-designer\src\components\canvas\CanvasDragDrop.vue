<template>
  <div
    class="canvas-drag-drop"
    @dragover="handleDragOver"
    @drop="handleDrop"
    @dragenter="handleDragEnter"
    @dragleave="handleDragLeave"
  >
    <slot></slot>

    <!-- Drop indicator overlay -->
    <div
      v-if="isDraggingOver"
      class="drop-indicator"
      :style="{
        left: `${dropIndicatorPosition.x}px`,
        top: `${dropIndicatorPosition.y}px`
      }"
    >
      <div class="drop-indicator-inner"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { Position } from '@/types/symbol';

// Add TypeScript declaration for the global window property
declare global {
  interface Window {
    dragOverWarningLogged?: boolean;
  }
}

// Store
const diagramStore = useDiagramStore();

// Props
const props = defineProps<{
  stageRef: any;
}>();

// We're not using stageRef anymore for drag and drop operations
// This is just a placeholder for compatibility
const isStageRefValid = computed(() => {
  return false; // Always return false to use our direct DOM method
});

// Log when stageRef changes, but we don't use it for drag and drop
watch(() => props.stageRef, (newValue) => {
  if (newValue) {
    console.log('CanvasDragDrop: stageRef changed, but using direct DOM method for drag and drop');
  }
});

// State
const isDraggingOver = ref(false);
const dropIndicatorPosition = ref<Position>({ x: 0, y: 0 });
const currentDragData = ref<any>(null);

// Methods
const handleDragOver = (event: DragEvent) => {
  event.preventDefault();

  // 确保 dataTransfer 效果设置为 copy，允许拖放
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy';
  }

  try {
    // Always use direct DOM method for calculating indicator position
    // This ensures consistent behavior regardless of stage reference status

    // 获取画布元素
    const canvasElement = event.currentTarget as HTMLElement;
    if (!canvasElement) {
      console.warn('Canvas element not found for dragOver');
      return;
    }

    // 获取画布的位置和大小
    const rect = canvasElement.getBoundingClientRect();

    // 计算相对于画布的位置
    let indicatorX = event.clientX - rect.left;
    let indicatorY = event.clientY - rect.top;

    // 考虑缩放和平移
    if (diagramStore.currentDiagram && diagramStore.currentDiagram.viewport) {
      const viewport = diagramStore.currentDiagram.viewport;
      indicatorX = indicatorX / viewport.scale + viewport.position.x;
      indicatorY = indicatorY / viewport.scale + viewport.position.y;
    }

    // Ensure we have valid coordinates
    if (isNaN(indicatorX) || isNaN(indicatorY) ||
        !isFinite(indicatorX) || !isFinite(indicatorY)) {
      // Use default position in the middle of the canvas
      indicatorX = rect.width / 2;
      indicatorY = rect.height / 2;
    }

    // 更新指示器位置
    dropIndicatorPosition.value = {
      x: indicatorX,
      y: indicatorY,
    };
  } catch (error) {
    console.error('Error in handleDragOver:', error);
  }
};

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();

  // 确保 dataTransfer 效果设置为 copy，允许拖放
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy';

    // 在 Firefox 中，无法在 dragenter 事件中读取数据
    // 所以我们只检查是否有我们期望的数据类型
    const types = event.dataTransfer.types;
    if (types.includes('application/json')) {
      isDraggingOver.value = true;
      return;
    }

    // 尝试读取数据（在 Chrome 中可能有效）
    try {
      const data = event.dataTransfer.getData('application/json');
      if (data) {
        const parsedData = JSON.parse(data);
        if (parsedData.type === 'symbol') {
          isDraggingOver.value = true;
          currentDragData.value = parsedData;
        }
      }
    } catch (error) {
      // 在某些浏览器中，在 dragenter 事件中无法读取数据
      // 所以我们忽略这个错误，只是设置 isDraggingOver 为 true
      isDraggingOver.value = true;
      console.log('Cannot read drag data in dragenter, will try in drop event');
    }
  }
};

const handleDragLeave = (event: DragEvent) => {
  // Check if we're leaving the canvas
  const relatedTarget = event.relatedTarget as HTMLElement;
  if (!relatedTarget || !event.currentTarget?.contains(relatedTarget)) {
    isDraggingOver.value = false;
    currentDragData.value = null;
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();

  console.log('Drop event triggered');

  if (!diagramStore.currentDiagram) {
    console.warn('No current diagram available');
    isDraggingOver.value = false;
    currentDragData.value = null;
    return;
  }

  // Always use the fallback method for calculating drop position
  // This ensures consistent behavior regardless of stage reference status
  console.log('Using direct method to calculate drop position');

  try {
    // 计算放置位置
    let dropX: number = 100; // Default position
    let dropY: number = 100; // Default position

    // 获取画布元素
    const canvasElement = event.currentTarget as HTMLElement;
    if (!canvasElement) {
      console.warn('Canvas element not found');
      isDraggingOver.value = false;
      currentDragData.value = null;
      return;
    }

    // 获取画布的位置和大小
    const rect = canvasElement.getBoundingClientRect();

    // 计算相对于画布的位置
    dropX = event.clientX - rect.left;
    dropY = event.clientY - rect.top;

    console.log('Raw drop position:', dropX, dropY);

    // 考虑缩放和平移
    if (diagramStore.currentDiagram.viewport) {
      const viewport = diagramStore.currentDiagram.viewport;
      dropX = dropX / viewport.scale + viewport.position.x;
      dropY = dropY / viewport.scale + viewport.position.y;
      console.log('Adjusted for viewport:', dropX, dropY);
    }

    // Ensure we have valid coordinates
    if (isNaN(dropX) || isNaN(dropY) || !isFinite(dropX) || !isFinite(dropY)) {
      console.warn('Invalid drop coordinates:', dropX, dropY);
      dropX = 100; // Default fallback position
      dropY = 100;
      console.log('Using fallback coordinates:', dropX, dropY);
    }

    console.log('Final drop position:', dropX, dropY);

    // Get the symbol data
    if (event.dataTransfer) {
      const data = event.dataTransfer.getData('application/json');
      console.log('Drop data:', data);

      if (data) {
        try {
          const parsedData = JSON.parse(data);
          if (parsedData.type === 'symbol' && parsedData.symbolId) {
            // Get the symbol definition from the diagram store
            const symbolDef = diagramStore.getSymbolDefinition(parsedData.symbolId);
            if (symbolDef) {
              console.log('Symbol definition found:', symbolDef.name);

              // Snap to grid if enabled
              const grid = diagramStore.currentDiagram.grid;
              let finalX = dropX;
              let finalY = dropY;

              if (grid.snapToGrid) {
                const gridSize = grid.size;
                finalX = Math.round(dropX / gridSize) * gridSize;
                finalY = Math.round(dropY / gridSize) * gridSize;
              }

              console.log('Final position:', finalX, finalY);

              // Add the symbol instance to the diagram
              const symbolInstanceId = diagramStore.addSymbolInstance(parsedData.symbolId, finalX, finalY);
              console.log('Symbol instance added to diagram with ID:', symbolInstanceId);

              // Verify the symbol was actually added
              if (symbolInstanceId && diagramStore.currentDiagram) {
                const addedSymbol = diagramStore.currentDiagram.symbolInstances[symbolInstanceId];
                if (addedSymbol) {
                  console.log('Symbol successfully added and verified:', addedSymbol);
                } else {
                  console.error('Symbol was not found after addition');
                }
              } else {
                console.error('Failed to add symbol instance');
              }
            } else {
              console.warn('Symbol definition not found for ID:', parsedData.symbolId);
            }
          } else {
            console.warn('Invalid drop data format or missing symbolId');
          }
        } catch (error) {
          console.error('Error parsing drop data:', error);
        }
      } else {
        console.warn('No data received in drop event');
      }
    } else {
      console.warn('No dataTransfer object in drop event');
    }
  } catch (error) {
    console.error('Error in handleDrop:', error);
  } finally {
    // Reset drag state
    isDraggingOver.value = false;
    currentDragData.value = null;
  }
};
</script>

<style scoped>
.canvas-drag-drop {
  width: 100%;
  height: 100%;
  position: relative;
}

.drop-indicator {
  position: absolute;
  width: 30px;
  height: 30px;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1000;
}

.drop-indicator-inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px dashed var(--primary-color);
  background-color: rgba(24, 144, 255, 0.1);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}
</style>
