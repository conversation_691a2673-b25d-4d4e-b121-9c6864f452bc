<template>
  <div class="value-display-editor">
    <div class="section-title">数值显示</div>

    <div v-if="selectedSymbol">
      <!-- Value displays list -->
      <div class="value-displays-list">
        <a-list
          size="small"
          :data-source="valueDisplays"
          :locale="{ emptyText: '未配置数值显示' }"
        >
          <template #renderItem="{ item, index }">
            <a-list-item>
              <div class="display-item">
                <span>{{ getBindingName(item.bindingId) }}</span>
                <div class="display-actions">
                  <a-button
                    type="text"
                    size="small"
                    @click="() => editDisplay(index)"
                  >
                    <template #icon><EditOutlined /></template>
                  </a-button>
                  <a-button
                    type="text"
                    size="small"
                    danger
                    @click="() => removeDisplay(index)"
                  >
                    <template #icon><DeleteOutlined /></template>
                  </a-button>
                </div>
              </div>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <!-- Add new display button -->
      <div class="add-display">
        <a-button
          type="primary"
          size="small"
          @click="addNewDisplay"
        >
          <template #icon><PlusOutlined /></template>
          添加数值显示
        </a-button>
      </div>

      <!-- Edit display modal -->
      <a-modal
        v-model:visible="isEditModalVisible"
        title="编辑数值显示"
        @ok="saveDisplay"
        @cancel="cancelEdit"
        :okButtonProps="{ disabled: !isValidDisplay }"
      >
        <a-form layout="vertical" size="small">
          <!-- Binding selection -->
          <a-form-item label="绑定">
            <a-select
              v-model:value="editingDisplay.bindingId"
              style="width: 100%"
              placeholder="选择绑定"
              size="small"
            >
              <a-select-option
                v-for="slot in symbolBindingSlots"
                :key="slot.id"
                :value="slot.id"
              >
                {{ slot.name }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <!-- Position -->
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="X位置">
                <a-input-number
                  v-model:value="editingDisplay.position.x"
                  style="width: 100%"
                  :min="-100"
                  :max="100"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="Y位置">
                <a-input-number
                  v-model:value="editingDisplay.position.y"
                  style="width: 100%"
                  :min="-100"
                  :max="100"
                  size="small"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Format and precision -->
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="格式">
                <a-select
                  v-model:value="editingDisplay.format"
                  style="width: 100%"
                  placeholder="选择格式"
                  size="small"
                >
                  <a-select-option value="integer">整数</a-select-option>
                  <a-select-option value="decimal">小数</a-select-option>
                  <a-select-option value="percentage">百分比</a-select-option>
                  <a-select-option value="scientific">科学计数</a-select-option>
                  <a-select-option value="binary">二进制</a-select-option>
                  <a-select-option value="hex">十六进制</a-select-option>
                  <a-select-option value="currency">货币</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="精度">
                <a-input-number
                  v-model:value="editingDisplay.precision"
                  style="width: 100%"
                  :min="0"
                  :max="10"
                  size="small"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Unit -->
          <a-form-item label="单位">
            <a-input
              v-model:value="editingDisplay.unit"
              placeholder="例如: kW, V, A"
              size="small"
            />
          </a-form-item>

          <!-- Font settings -->
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="字体大小">
                <a-input-number
                  v-model:value="editingDisplay.fontSize"
                  style="width: 100%"
                  :min="8"
                  :max="24"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="文本颜色">
                <a-input
                  v-model:value="editingDisplay.textColor"
                  placeholder="#000000"
                  size="small"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Background settings -->
          <a-form-item>
            <a-checkbox
              v-model:checked="editingDisplay.showBackground"
            >
              显示背景
            </a-checkbox>
          </a-form-item>

          <a-row :gutter="8" v-if="editingDisplay.showBackground">
            <a-col :span="12">
              <a-form-item label="背景颜色">
                <a-input
                  v-model:value="editingDisplay.backgroundColor"
                  placeholder="#ffffff"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="透明度">
                <a-slider
                  v-model:value="editingDisplay.backgroundOpacity"
                  :min="0"
                  :max="1"
                  :step="0.1"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Size settings -->
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="宽度">
                <a-input-number
                  v-model:value="editingDisplay.width"
                  style="width: 100%"
                  :min="20"
                  :max="200"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="高度">
                <a-input-number
                  v-model:value="editingDisplay.height"
                  style="width: 100%"
                  :min="10"
                  :max="100"
                  size="small"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
    </div>

    <div v-else class="no-selection">
      未选择符号
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { SymbolInstance } from '@/types/symbolInstance';
import { BindingSlot, Position } from '@/types/symbol';

import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';

// Interface for value display
interface ValueDisplay {
  bindingId: string;
  position: Position;
  format?: string;
  precision?: number;
  unit?: string;
  fontSize?: number;
  textColor?: string;
  showBackground?: boolean;
  backgroundColor?: string;
  backgroundOpacity?: number;
  width?: number;
  height?: number;
}

// Store
const diagramStore = useDiagramStore();

// State
const isEditModalVisible = ref(false);
const editingDisplayIndex = ref<number | null>(null);
const editingDisplay = ref<ValueDisplay>({
  bindingId: '',
  position: { x: 0, y: 0 },
  format: 'decimal',
  precision: 2,
  unit: '',
  fontSize: 14,
  textColor: '#333333',
  showBackground: true,
  backgroundColor: '#ffffff',
  backgroundOpacity: 0.7,
  width: 60,
  height: 20
});

// Computed
const selectedSymbol = computed<SymbolInstance | null>(() => {
  if (!diagramStore.currentDiagram) return null;
  if (!diagramStore.currentDiagram.selectedSymbolIds) return null;
  if (diagramStore.currentDiagram.selectedSymbolIds.length === 0) return null;

  const symbolId = diagramStore.currentDiagram.selectedSymbolIds[0];
  return diagramStore.currentDiagram.symbolInstances[symbolId] || null;
});

const symbolBindingSlots = computed<BindingSlot[]>(() => {
  if (!selectedSymbol.value) return [];

  const definition = diagramStore.getSymbolDefinition(selectedSymbol.value.definitionId);
  if (!definition) return [];

  return definition.bindingSlots;
});

const valueDisplays = computed<ValueDisplay[]>(() => {
  if (!selectedSymbol.value) return [];

  const displays = selectedSymbol.value.properties.valueDisplays;
  if (!displays || !Array.isArray(displays)) return [];

  return displays as ValueDisplay[];
});

const isValidDisplay = computed(() => {
  return editingDisplay.value.bindingId !== '';
});

// Methods
const getBindingName = (bindingId: string): string => {
  const slot = symbolBindingSlots.value.find(s => s.id === bindingId);
  return slot ? slot.name : bindingId;
};

const addNewDisplay = () => {
  // Reset editing display to defaults
  editingDisplay.value = {
    bindingId: symbolBindingSlots.value.length > 0 ? symbolBindingSlots.value[0].id : '',
    position: { x: 0, y: 0 },
    format: 'decimal',
    precision: 2,
    unit: '',
    fontSize: 14,
    textColor: '#333333',
    showBackground: true,
    backgroundColor: '#ffffff',
    backgroundOpacity: 0.7,
    width: 60,
    height: 20
  };

  editingDisplayIndex.value = null;
  isEditModalVisible.value = true;
};

const editDisplay = (index: number) => {
  if (!selectedSymbol.value || !valueDisplays.value[index]) return;

  // Clone the display for editing
  editingDisplay.value = JSON.parse(JSON.stringify(valueDisplays.value[index]));
  editingDisplayIndex.value = index;
  isEditModalVisible.value = true;
};

const removeDisplay = (index: number) => {
  if (!selectedSymbol.value || !valueDisplays.value[index]) return;

  // Get current displays
  const displays = [...valueDisplays.value];

  // Remove the display at the specified index
  displays.splice(index, 1);

  // Update the symbol properties
  updateSymbolValueDisplays(displays);
};

const saveDisplay = () => {
  if (!selectedSymbol.value || !isValidDisplay.value) return;

  // Get current displays or initialize empty array
  const displays = [...valueDisplays.value];

  if (editingDisplayIndex.value !== null) {
    // Update existing display
    displays[editingDisplayIndex.value] = { ...editingDisplay.value };
  } else {
    // Add new display
    displays.push({ ...editingDisplay.value });
  }

  // Update the symbol properties
  updateSymbolValueDisplays(displays);

  // Close the modal
  isEditModalVisible.value = false;
};

const cancelEdit = () => {
  isEditModalVisible.value = false;
};

const updateSymbolValueDisplays = (displays: ValueDisplay[]) => {
  if (!selectedSymbol.value) return;

  // Update the symbol properties
  diagramStore.updateSymbolProperties(
    selectedSymbol.value.id,
    {
      ...selectedSymbol.value.properties,
      valueDisplays: displays
    }
  );
};
</script>

<style scoped>
.value-display-editor {
  padding: 8px;
}

.section-title {
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--primary-color);
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 2px;
}

.value-displays-list {
  margin-bottom: 8px;
}

.display-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 11px;
}

.display-actions {
  display: flex;
  gap: 2px;
}

.add-display {
  margin-top: 4px;
}

.no-selection {
  color: #999;
  font-style: italic;
  text-align: center;
  margin-top: 8px;
  font-size: 11px;
}

/* Compact modal form styles */
:deep(.ant-modal-body .ant-form-item) {
  margin-bottom: 8px;
}

:deep(.ant-modal-body .ant-form-item-label > label) {
  font-size: 11px;
  height: 16px;
  line-height: 16px;
}

:deep(.ant-modal-body .ant-input),
:deep(.ant-modal-body .ant-input-number),
:deep(.ant-modal-body .ant-select) {
  font-size: 11px;
}

:deep(.ant-list-item) {
  padding: 4px 0;
}

:deep(.ant-list-item-meta-title) {
  font-size: 11px;
}
</style>
