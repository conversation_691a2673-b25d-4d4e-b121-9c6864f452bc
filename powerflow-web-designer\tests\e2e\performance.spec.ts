import { test, expect } from '@playwright/test';
import { 
  navigateToEditor, 
  createNewDiagram, 
  addSymbolToDiagram 
} from './helpers';

test.describe('Performance Tests', () => {
  test('should load the editor page quickly', async ({ page }) => {
    // Start timing
    const startTime = Date.now();
    
    // Navigate to the editor
    await navigateToEditor(page);
    
    // End timing
    const loadTime = Date.now() - startTime;
    
    // Check that the page loaded within a reasonable time
    expect(loadTime).toBeLessThan(5000); // 5 seconds
    
    // Check that the editor is fully loaded
    await expect(page.locator('.editor-header')).toBeVisible();
    await expect(page.locator('.editor-toolbar')).toBeVisible();
  });

  test('should create a new diagram quickly', async ({ page }) => {
    await navigateToEditor(page);
    
    // Start timing
    const startTime = Date.now();
    
    // Click the New button in the toolbar
    await page.getByRole('button', { name: 'New' }).click();
    
    // Fill in the diagram name
    await page.getByLabel('Diagram Name').fill('Performance Test Diagram');
    
    // Click the Create button
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Wait for the diagram to be created
    await expect(page.locator('.diagram-canvas')).toBeVisible();
    
    // End timing
    const createTime = Date.now() - startTime;
    
    // Check that the diagram was created within a reasonable time
    expect(createTime).toBeLessThan(3000); // 3 seconds
  });

  test('should handle a diagram with many symbols', async ({ page }) => {
    await createNewDiagram(page, 'Large Diagram Test');
    
    // Add multiple symbols
    const symbolCount = 20;
    const startTime = Date.now();
    
    for (let i = 0; i < symbolCount; i++) {
      await addSymbolToDiagram(page, 'Circuit Breaker');
    }
    
    // End timing
    const addTime = Date.now() - startTime;
    
    // Check that all symbols were added
    await expect(page.locator('.symbol-instance')).toHaveCount(symbolCount);
    
    // Check that the symbols were added within a reasonable time
    const averageTimePerSymbol = addTime / symbolCount;
    expect(averageTimePerSymbol).toBeLessThan(1000); // 1 second per symbol
    
    // Check that the application is still responsive
    await page.getByRole('button', { name: 'Symbols' }).click();
    await expect(page.locator('.symbol-palette')).toBeVisible();
  });

  test('should maintain good performance when panning and zooming', async ({ page }) => {
    await createNewDiagram(page, 'Pan Zoom Performance Test');
    
    // Add some symbols
    for (let i = 0; i < 10; i++) {
      await addSymbolToDiagram(page, 'Circuit Breaker');
    }
    
    // Measure zoom performance
    const zoomStartTime = Date.now();
    
    // Zoom in several times
    for (let i = 0; i < 5; i++) {
      await page.getByRole('button', { name: 'Zoom In' }).click();
      await page.waitForTimeout(100);
    }
    
    // Zoom out several times
    for (let i = 0; i < 5; i++) {
      await page.getByRole('button', { name: 'Zoom Out' }).click();
      await page.waitForTimeout(100);
    }
    
    const zoomTime = Date.now() - zoomStartTime;
    
    // Check that zooming was performed within a reasonable time
    expect(zoomTime).toBeLessThan(3000); // 3 seconds
    
    // Measure pan performance
    const panStartTime = Date.now();
    
    // Get the canvas element
    const canvas = page.locator('.diagram-canvas');
    const canvasBounds = await canvas.boundingBox();
    
    if (canvasBounds) {
      // Pan in different directions
      await page.mouse.move(canvasBounds.x + canvasBounds.width / 2, canvasBounds.y + canvasBounds.height / 2);
      await page.mouse.down();
      await page.mouse.move(canvasBounds.x + canvasBounds.width / 2 + 100, canvasBounds.y + canvasBounds.height / 2);
      await page.mouse.up();
      
      await page.mouse.move(canvasBounds.x + canvasBounds.width / 2, canvasBounds.y + canvasBounds.height / 2);
      await page.mouse.down();
      await page.mouse.move(canvasBounds.x + canvasBounds.width / 2, canvasBounds.y + canvasBounds.height / 2 + 100);
      await page.mouse.up();
    }
    
    const panTime = Date.now() - panStartTime;
    
    // Check that panning was performed within a reasonable time
    expect(panTime).toBeLessThan(2000); // 2 seconds
  });
});
