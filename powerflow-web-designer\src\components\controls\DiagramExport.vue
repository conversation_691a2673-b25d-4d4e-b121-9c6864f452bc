<template>
  <div class="diagram-export">
    <a-dropdown :trigger="['click']">
      <a-button>
        Export <down-outlined />
      </a-button>
      <template #overlay>
        <a-menu>
          <a-menu-item key="png" @click="exportAsPNG">
            <file-image-outlined /> Export as PNG
          </a-menu-item>
          <a-menu-item key="json" @click="exportAsJSON">
            <file-text-outlined /> Export as JSON
          </a-menu-item>
          <a-menu-item key="svg" @click="exportAsSVG">
            <file-outlined /> Export as SVG
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    
    <a-modal
      v-model:visible="isModalVisible"
      title="Export Options"
      @ok="handleExport"
    >
      <a-form :model="exportOptions" layout="vertical">
        <a-form-item label="File Name">
          <a-input v-model:value="exportOptions.fileName" />
        </a-form-item>
        
        <template v-if="exportOptions.format === 'png'">
          <a-form-item label="Scale">
            <a-slider
              v-model:value="exportOptions.scale"
              :min="0.1"
              :max="3"
              :step="0.1"
              :marks="{
                0.5: '0.5x',
                1: '1x',
                2: '2x',
              }"
            />
          </a-form-item>
          
          <a-form-item label="Quality">
            <a-radio-group v-model:value="exportOptions.quality">
              <a-radio-button value="low">Low</a-radio-button>
              <a-radio-button value="medium">Medium</a-radio-button>
              <a-radio-button value="high">High</a-radio-button>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="Background Color">
            <a-input
              v-model:value="exportOptions.backgroundColor"
              type="color"
            />
          </a-form-item>
        </template>
        
        <template v-if="exportOptions.format === 'svg'">
          <a-form-item label="Include Grid">
            <a-switch v-model:checked="exportOptions.includeGrid" />
          </a-form-item>
        </template>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { DownOutlined, FileImageOutlined, FileTextOutlined, FileOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useDiagramStore } from '@/stores/diagram';

// Props
const props = defineProps<{
  stageRef: any;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const isModalVisible = ref(false);
const exportOptions = ref({
  format: 'png',
  fileName: 'diagram',
  scale: 1,
  quality: 'medium',
  backgroundColor: '#ffffff',
  includeGrid: false,
});

// Methods
const exportAsPNG = () => {
  exportOptions.value.format = 'png';
  exportOptions.value.fileName = `${diagramStore.currentDiagramName || 'diagram'}.png`;
  isModalVisible.value = true;
};

const exportAsJSON = () => {
  if (!diagramStore.currentDiagram) {
    message.error('No diagram to export');
    return;
  }
  
  // Create a JSON string from the diagram
  const diagramJson = JSON.stringify(diagramStore.currentDiagram, null, 2);
  
  // Create a blob and download link
  const blob = new Blob([diagramJson], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  // Create a download link and click it
  const a = document.createElement('a');
  a.href = url;
  a.download = `${diagramStore.currentDiagramName || 'diagram'}.json`;
  document.body.appendChild(a);
  a.click();
  
  // Clean up
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  
  message.success('Diagram exported as JSON');
};

const exportAsSVG = () => {
  exportOptions.value.format = 'svg';
  exportOptions.value.fileName = `${diagramStore.currentDiagramName || 'diagram'}.svg`;
  isModalVisible.value = true;
};

const handleExport = () => {
  if (!props.stageRef || !diagramStore.currentDiagram) {
    message.error('No diagram to export');
    isModalVisible.value = false;
    return;
  }
  
  const stage = props.stageRef.getNode();
  
  if (exportOptions.value.format === 'png') {
    // Export as PNG
    const dataURL = stage.toDataURL({
      pixelRatio: exportOptions.value.scale,
      mimeType: 'image/png',
      quality: exportOptions.value.quality === 'high' ? 1 : 
               exportOptions.value.quality === 'medium' ? 0.8 : 0.5,
      backgroundColor: exportOptions.value.backgroundColor,
    });
    
    // Create a download link and click it
    const a = document.createElement('a');
    a.href = dataURL;
    a.download = exportOptions.value.fileName;
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    document.body.removeChild(a);
    
    message.success('Diagram exported as PNG');
  } else if (exportOptions.value.format === 'svg') {
    // Export as SVG
    const svg = stage.toSVG({
      includeGrid: exportOptions.value.includeGrid,
    });
    
    // Create a blob and download link
    const blob = new Blob([svg], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    // Create a download link and click it
    const a = document.createElement('a');
    a.href = url;
    a.download = exportOptions.value.fileName;
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success('Diagram exported as SVG');
  }
  
  isModalVisible.value = false;
};
</script>

<style scoped>
.diagram-export {
  display: inline-block;
}
</style>
