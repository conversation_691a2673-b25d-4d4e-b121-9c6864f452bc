<template>
  <a-modal
    :open="visible"
    title="Grid Settings"
    :width="500"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:open="$emit('update:visible', $event)"
  >
    <div class="grid-settings-dialog">
      <!-- Grid Visibility -->
      <div class="setting-group">
        <div class="setting-label">Grid Visibility</div>
        <div class="setting-control">
          <a-switch
            v-model:checked="localSettings.visible"
            @change="updatePreview"
          />
          <span class="setting-hint">{{ localSettings.visible ? 'Show' : 'Hide' }} grid lines</span>
        </div>
      </div>

      <!-- Snap to Grid -->
      <div class="setting-group">
        <div class="setting-label">Snap to Grid</div>
        <div class="setting-control">
          <a-switch
            v-model:checked="localSettings.snapToGrid"
            @change="updatePreview"
          />
          <span class="setting-hint">{{ localSettings.snapToGrid ? 'Enable' : 'Disable' }} snap to grid</span>
        </div>
      </div>

      <!-- Grid Size -->
      <div class="setting-group">
        <div class="setting-label">Grid Size</div>
        <div class="setting-control">
          <a-slider
            v-model:value="localSettings.size"
            :min="5"
            :max="100"
            :step="5"
            @change="updatePreview"
          />
          <a-input-number
            v-model:value="localSettings.size"
            :min="5"
            :max="100"
            :step="5"
            style="margin-left: 8px; width: 80px;"
            @change="updatePreview"
          />
          <span class="setting-unit">px</span>
        </div>
      </div>

      <!-- Major Grid Size -->
      <div class="setting-group">
        <div class="setting-label">Major Grid Size</div>
        <div class="setting-control">
          <a-slider
            v-model:value="localSettings.majorGridSize"
            :min="localSettings.size * 2"
            :max="500"
            :step="localSettings.size"
            @change="updatePreview"
          />
          <a-input-number
            v-model:value="localSettings.majorGridSize"
            :min="localSettings.size * 2"
            :max="500"
            :step="localSettings.size"
            style="margin-left: 8px; width: 80px;"
            @change="updatePreview"
          />
          <span class="setting-unit">px</span>
        </div>
      </div>

      <!-- Grid Pattern -->
      <div class="setting-group">
        <div class="setting-label">Grid Pattern</div>
        <div class="setting-control">
          <a-radio-group
            v-model:value="localSettings.pattern"
            @change="updatePreview"
          >
            <a-radio value="lines">Lines</a-radio>
            <a-radio value="dots">Dots</a-radio>
          </a-radio-group>
        </div>
      </div>

      <!-- Grid Colors -->
      <div class="setting-group">
        <div class="setting-label">Grid Colors</div>
        <div class="setting-control color-controls">
          <div class="color-item">
            <label>Minor Grid:</label>
            <input
              type="color"
              v-model="localSettings.minorColor"
              @change="updatePreview"
              class="color-picker"
            />
            <span class="color-value">{{ localSettings.minorColor }}</span>
          </div>
          <div class="color-item">
            <label>Major Grid:</label>
            <input
              type="color"
              v-model="localSettings.majorColor"
              @change="updatePreview"
              class="color-picker"
            />
            <span class="color-value">{{ localSettings.majorColor }}</span>
          </div>
        </div>
      </div>

      <!-- Preview -->
      <div class="setting-group">
        <div class="setting-label">Preview</div>
        <div class="grid-preview" ref="previewContainer">
          <canvas ref="previewCanvas" width="200" height="150"></canvas>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button @click="handleCancel">Cancel</a-button>
      <a-button @click="resetToDefaults" style="margin-left: 8px;">Reset to Defaults</a-button>
      <a-button type="primary" @click="handleOk" style="margin-left: 8px;">Apply</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onMounted } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { GridPatternType } from '@/types/diagram';

// Props
const props = defineProps<{
  visible: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
}>();

// Store
const diagramStore = useDiagramStore();

// Refs
const previewCanvas = ref<HTMLCanvasElement | null>(null);
const previewContainer = ref<HTMLElement | null>(null);

// Local settings state
const localSettings = reactive({
  visible: true,
  snapToGrid: false,
  size: 20,
  majorGridSize: 100,
  pattern: 'lines' as GridPatternType,
  minorColor: '#e0e0e0',
  majorColor: '#c0c0c0'
});

// Default settings
const defaultSettings = {
  visible: false,
  snapToGrid: false,
  size: 20,
  majorGridSize: 100,
  pattern: 'lines' as GridPatternType,
  minorColor: '#e0e0e0',
  majorColor: '#c0c0c0'
};

// Initialize settings from current diagram
const initializeSettings = () => {
  if (diagramStore.currentDiagram?.grid) {
    const grid = diagramStore.currentDiagram.grid;
    Object.assign(localSettings, {
      visible: grid.visible,
      snapToGrid: grid.snapToGrid,
      size: grid.size,
      majorGridSize: grid.majorGridSize,
      pattern: grid.pattern,
      minorColor: grid.minorColor,
      majorColor: grid.majorColor
    });
  } else {
    Object.assign(localSettings, defaultSettings);
  }
};

// Watch for dialog visibility changes
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    initializeSettings();
    nextTick(() => {
      updatePreview();
    });
  }
});

// Update preview canvas
const updatePreview = () => {
  nextTick(() => {
    if (!previewCanvas.value) return;

    const canvas = previewCanvas.value;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    if (!localSettings.visible) return;

    const gridSize = localSettings.size;
    const majorGridSize = localSettings.majorGridSize;

    if (localSettings.pattern === 'lines') {
      // Draw minor grid lines
      ctx.strokeStyle = localSettings.minorColor;
      ctx.lineWidth = 1;
      ctx.beginPath();

      // Vertical lines
      for (let x = 0; x <= canvas.width; x += gridSize) {
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
      }

      // Horizontal lines
      for (let y = 0; y <= canvas.height; y += gridSize) {
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
      }

      ctx.stroke();

      // Draw major grid lines
      ctx.strokeStyle = localSettings.majorColor;
      ctx.lineWidth = 2;
      ctx.beginPath();

      // Vertical major lines
      for (let x = 0; x <= canvas.width; x += majorGridSize) {
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
      }

      // Horizontal major lines
      for (let y = 0; y <= canvas.height; y += majorGridSize) {
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
      }

      ctx.stroke();
    } else {
      // Draw dots
      ctx.fillStyle = localSettings.minorColor;

      for (let x = 0; x <= canvas.width; x += gridSize) {
        for (let y = 0; y <= canvas.height; y += gridSize) {
          ctx.beginPath();
          ctx.arc(x, y, 1, 0, 2 * Math.PI);
          ctx.fill();
        }
      }

      // Draw major dots
      ctx.fillStyle = localSettings.majorColor;

      for (let x = 0; x <= canvas.width; x += majorGridSize) {
        for (let y = 0; y <= canvas.height; y += majorGridSize) {
          ctx.beginPath();
          ctx.arc(x, y, 2, 0, 2 * Math.PI);
          ctx.fill();
        }
      }
    }
  });
};

// Handle OK button
const handleOk = () => {
  if (diagramStore.currentDiagram) {
    diagramStore.updateGridSettings({
      visible: localSettings.visible,
      snapToGrid: localSettings.snapToGrid,
      size: localSettings.size,
      majorGridSize: localSettings.majorGridSize,
      pattern: localSettings.pattern,
      minorColor: localSettings.minorColor,
      majorColor: localSettings.majorColor
    });
  }
  emit('update:visible', false);
};

// Handle Cancel button
const handleCancel = () => {
  emit('update:visible', false);
};

// Reset to defaults
const resetToDefaults = () => {
  Object.assign(localSettings, defaultSettings);
  updatePreview();
};

// Initialize on mount
onMounted(() => {
  initializeSettings();
});
</script>

<style scoped>
.grid-settings-dialog {
  padding: 16px 0;
}

.setting-group {
  margin-bottom: 24px;
}

.setting-label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.setting-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-hint {
  color: #666;
  font-size: 12px;
}

.setting-unit {
  color: #666;
  font-size: 12px;
  min-width: 20px;
}

.color-controls {
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-item label {
  min-width: 80px;
  font-size: 12px;
  color: #666;
}

.color-picker {
  width: 40px;
  height: 30px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
}

.color-value {
  font-family: monospace;
  font-size: 11px;
  color: #666;
  min-width: 60px;
}

.grid-preview {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  background: #f9f9f9;
  display: inline-block;
}

.grid-preview canvas {
  border: 1px solid #e0e0e0;
  background: white;
  border-radius: 2px;
}
</style>
