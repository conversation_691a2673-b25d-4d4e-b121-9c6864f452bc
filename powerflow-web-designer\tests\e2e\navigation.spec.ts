import { test, expect } from '@playwright/test';
import { navigateToHome, navigateToEditor, navigateToViewer } from './helpers';

test.describe('Basic Navigation', () => {
  test('should load the home page correctly', async ({ page }) => {
    await page.goto('/');
    
    // Check that the home page is loaded
    await expect(page.locator('h1')).toContainText('PowerFlow Web Designer');
    
    // Check that navigation buttons are visible
    await expect(page.getByRole('button', { name: 'Open Editor' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Open Viewer' })).toBeVisible();
  });

  test('should navigate to the editor page', async ({ page }) => {
    await navigateToHome(page);
    
    // Click the Editor button
    await page.getByRole('button', { name: 'Open Editor' }).click();
    
    // Check that the editor page is loaded
    await expect(page.locator('.editor-header')).toBeVisible();
    await expect(page.locator('.editor-toolbar')).toBeVisible();
  });

  test('should navigate to the viewer page', async ({ page }) => {
    await navigateToHome(page);
    
    // Click the Viewer button
    await page.getByRole('button', { name: 'Open Viewer' }).click();
    
    // Check that the viewer page is loaded
    await expect(page.locator('.viewer-header')).toBeVisible();
  });

  test('should navigate back to home from editor', async ({ page }) => {
    await navigateToEditor(page);
    
    // Click the Home button
    await page.getByRole('button', { name: 'Home' }).click();
    
    // Check that the home page is loaded
    await expect(page.locator('h1')).toContainText('PowerFlow Web Designer');
  });

  test('should navigate back to home from viewer', async ({ page }) => {
    await navigateToViewer(page);
    
    // Click the Home button
    await page.getByRole('button', { name: 'Home' }).click();
    
    // Check that the home page is loaded
    await expect(page.locator('h1')).toContainText('PowerFlow Web Designer');
  });
});
