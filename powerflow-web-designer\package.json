{"name": "powerflow-web-designer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:hierarchical": "node tests/e2e/run-hierarchical-tests.js"}, "dependencies": {"ant-design-vue": "^3.2.20", "chart.js": "^4.4.9", "konva": "^9.3.20", "less": "^4.3.0", "pinia": "^3.0.2", "uuid": "^9.0.1", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-konva": "^3.2.1", "vue-router": "^4.5.1"}, "devDependencies": {"@axe-core/playwright": "^4.10.1", "@playwright/test": "^1.52.0", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "^10.1.0", "happy-dom": "^17.4.7", "prettier": "^3.5.3", "typescript": "~5.8.3", "vite": "^6.3.5", "vitest": "^3.1.4", "vue-tsc": "^2.2.8"}}