<template>
  <div class="viewer">
    <div class="viewer-header">
      <div class="logo">
        <h2>{{ $locale.t('views.viewer.title') }}</h2>
      </div>
      <div class="toolbar">
        <a-space>
          <diagram-search
            :stage-ref="stageRef"
            @focus-element="focusElement"
          />
          <a-button type="primary" ghost @click="$router.push('/editor')">{{ $locale.t('toolbar.edit') }}</a-button>
          <a-button ghost @click="$router.push('/')">{{ $locale.t('toolbar.home') }}</a-button>
        </a-space>
      </div>
      <div class="diagram-info">
        <span class="diagram-name">{{ diagramName }}</span>
      </div>
    </div>

    <div class="viewer-content">
      <diagram-renderer ref="diagramRenderer" :read-only="true" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import DiagramRenderer from '@/components/canvas/DiagramRenderer.vue';
import DiagramSearch from '@/components/controls/DiagramSearch.vue';
import { Position } from '@/types/symbol';

// Store
const diagramStore = useDiagramStore();

// Refs
const diagramRenderer = ref(null);
const stageRef = computed(() => diagramRenderer.value?.canvas?.stage);

// Computed
const diagramName = computed(() => {
  return diagramStore.currentDiagramName;
});

// Methods
const handleViewportChange = (position: Position, scale: number) => {
  if (!diagramStore.currentDiagram) return;

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    position,
    scale,
  };
};

const focusElement = (id: string, type: 'symbol' | 'connection', position: Position) => {
  if (!diagramStore.currentDiagram || !stageRef.value) return;

  // Get the stage
  const stage = stageRef.value.getNode();
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Calculate the new viewport position
  const viewport = diagramStore.currentDiagram.viewport;
  const newPosition = {
    x: position.x - stageWidth / (2 * viewport.scale),
    y: position.y - stageHeight / (2 * viewport.scale),
  };

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    ...viewport,
    position: newPosition,
  };
};

// Lifecycle hooks
onMounted(() => {
  // Initialize with a new diagram if none exists
  if (!diagramStore.currentDiagram) {
    diagramStore.createDiagram('New Diagram');
  }
});
</script>

<style scoped>
.viewer {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.viewer-header {
  height: 36px;
  background-color: var(--header-background-color);
  border-bottom: 1px solid var(--header-border-color);
  display: flex;
  align-items: center;
  padding: 0 8px;
  color: white;
}

.logo {
  width: 200px;
}

.logo h2 {
  margin: 0;
  font-size: 14px;
  color: white;
  font-weight: 500;
}

.toolbar {
  flex: 1;
  display: flex;
  justify-content: center;
  background-color: var(--header-background-color);
}

.diagram-info {
  width: 200px;
  text-align: right;
}

.diagram-name {
  font-weight: 500;
  color: white;
}

.viewer-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* 为了在蓝色背景上更好地显示按钮 */
:deep(.toolbar .ant-btn) {
  color: white;
  border-color: rgba(255, 255, 255, 0.7);
  background-color: rgba(255, 255, 255, 0.15);
  height: 28px;
  padding: 0 8px;
}

:deep(.toolbar .ant-btn:hover) {
  color: white;
  border-color: white;
  background-color: rgba(255, 255, 255, 0.25);
}

:deep(.toolbar .ant-btn[disabled]) {
  color: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.05);
}

/* 确保图标正常显示 */
:deep(.toolbar .anticon) {
  font-size: 14px;
  vertical-align: middle;
  line-height: 1;
}
</style>
