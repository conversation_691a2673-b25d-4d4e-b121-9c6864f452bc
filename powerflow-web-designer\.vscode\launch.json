{"version": "0.2.0", "configurations": [{"name": "🚀 启动开发服务器 (Dev Server)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "env": {"NODE_ENV": "development"}, "presentation": {"group": "development", "order": 1}}, {"name": "🔧 构建项目 (Build)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "build"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "build", "order": 1}}, {"name": "👀 预览构建结果 (Preview)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "preview"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "preview", "order": 1}}, {"name": "🧪 运行单元测试 (Unit Tests)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "test"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "test", "order": 1}}, {"name": "🔍 监视模式测试 (Test Watch)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "test:watch"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "test", "order": 2}}, {"name": "🎭 E2E测试 (Playwright)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "test:e2e"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "test", "order": 3}}, {"name": "🎭 E2E测试UI (Playwright UI)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "test:e2e:ui"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "test", "order": 4}}, {"name": "🐛 E2E调试模式 (Playwright Debug)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "test:e2e:debug"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "test", "order": 5}}, {"name": "📊 测试覆盖率 (Coverage)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "test:coverage"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "test", "order": 6}}, {"name": "🔧 代码检查 (Lint)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "lint"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "tools", "order": 1}}, {"name": "💅 代码格式化 (Format)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "format"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "tools", "order": 2}}], "compounds": [{"name": "🚀 完整开发环境 (Dev + Tests)", "configurations": ["🚀 启动开发服务器 (Dev Server)", "🔍 监视模式测试 (Test Watch)"], "presentation": {"group": "compound", "order": 1}}]}