{
  // 编辑器设置
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.rulers": [80, 120],
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 120,
  
  // 文件类型关联
  "files.associations": {
    "*.vue": "vue",
    "*.ts": "typescript",
    "*.js": "javascript",
    "*.json": "jsonc"
  },
  
  // 文件排除设置
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/.vscode/settings.json": false
  },
  
  // 搜索排除设置
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/coverage": true,
    "**/test-results": true,
    "**/playwright-report": true,
    "**/*.log": true
  },
  
  // Vue特定设置
  "vetur.validation.template": false,
  "vetur.validation.script": false,
  "vetur.validation.style": false,
  
  // TypeScript设置
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  
  // ESLint设置
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  "eslint.format.enable": true,
  "eslint.codeAction.showDocumentation": {
    "enable": true
  },
  
  // Prettier设置
  "prettier.configPath": ".prettierrc",
  "prettier.requireConfig": false,
  "prettier.useEditorConfig": false,
  
  // 特定文件类型的格式化器
  "[vue]": {
    "editor.defaultFormatter": "Vue.volar"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[less]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.wordWrap": "on"
  },
  
  // 终端设置
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.profiles.windows": {
    "PowerShell": {
      "source": "PowerShell",
      "icon": "terminal-powershell"
    },
    "Command Prompt": {
      "path": [
        "${env:windir}\\Sysnative\\cmd.exe",
        "${env:windir}\\System32\\cmd.exe"
      ],
      "args": [],
      "icon": "terminal-cmd"
    }
  },
  
  // 调试设置
  "debug.console.fontSize": 14,
  "debug.console.lineHeight": 22,
  "debug.openDebug": "openOnDebugBreak",
  
  // 工作区设置
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.js, ${capture}.d.ts, ${capture}.js.map",
    "*.vue": "${capture}.vue.d.ts",
    "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml",
    "vite.config.*": "vitest.config.*, playwright.config.*",
    "tsconfig.json": "tsconfig.*.json",
    ".eslintrc.*": ".eslintignore, .prettierrc.*, .prettierignore"
  },
  
  // Git设置
  "git.autofetch": true,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,
  "git.postCommitCommand": "none",
  
  // 扩展设置
  "extensions.ignoreRecommendations": false,
  
  // 性能设置
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/coverage/**": true,
    "**/test-results/**": true,
    "**/playwright-report/**": true
  },
  
  // 自动保存设置
  "files.autoSave": "onFocusChange",
  "files.autoSaveDelay": 1000,
  
  // 面包屑导航
  "breadcrumbs.enabled": true,
  "breadcrumbs.showFiles": true,
  "breadcrumbs.showSymbols": true,
  
  // 代码提示设置
  "editor.suggest.insertMode": "replace",
  "editor.acceptSuggestionOnCommitCharacter": true,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.suggestSelection": "first",
  
  // 颜色主题（可选）
  "workbench.colorTheme": "Default Dark+",
  "workbench.iconTheme": "vs-seti",
  
  // 字体设置
  "editor.fontFamily": "'Cascadia Code', 'Fira Code', 'JetBrains Mono', Consolas, 'Courier New', monospace",
  "editor.fontLigatures": true,
  "editor.fontSize": 14,
  "editor.lineHeight": 22,
  
  // 缩进指南
  "editor.guides.indentation": true,
  "editor.guides.bracketPairs": true,
  
  // 括号配对
  "editor.bracketPairColorization.enabled": true,
  "editor.matchBrackets": "always",
  
  // 空白字符显示
  "editor.renderWhitespace": "boundary",
  "editor.renderControlCharacters": true,
  
  // 行号设置
  "editor.lineNumbers": "on",
  "editor.glyphMargin": true,
  
  // 滚动设置
  "editor.smoothScrolling": true,
  "editor.cursorSmoothCaretAnimation": "on",
  
  // 快速修复设置
  "editor.lightbulb.enabled": "on",
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  }
}
