<template>
  <v-group
    :config="{
      id: connection.id,
    }"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Waypoint context menu -->
    <waypoint-context-menu
      v-if="showWaypointContextMenu"
      :connection-id="connection.id"
      :waypoint-index="contextMenuWaypointIndex"
      :position="contextMenuPosition"
      @close="closeWaypointContextMenu"
    />
    <!-- Main connection line -->
    <v-line
      v-if="connectionType === ConnectionLineType.STRAIGHT"
      :config="{
        points: flattenedPoints,
        stroke: getStrokeColor(),
        strokeWidth: getLineWidth(),
        lineCap: getLineCap(),
        lineJoin: getLineJoin(),
        dash: getLineDash(),
        opacity: getLineOpacity(),
        shadowColor: getShadowColor(),
        shadowBlur: getShadowBlur(),
        shadowOffsetX: getShadowOffsetX(),
        shadowOffsetY: getShadowOffsetY(),
      }"
    />

    <v-path
      v-else-if="connectionType === ConnectionLineType.BEZIER && bezierPath"
      :config="{
        data: bezierPath || 'M0,0',
        stroke: getStrokeColor(),
        strokeWidth: getLineWidth(),
        lineCap: getLineCap(),
        lineJoin: getLineJoin(),
        dash: getLineDash(),
        opacity: getLineOpacity(),
        shadowColor: getShadowColor(),
        shadowBlur: getShadowBlur(),
        shadowOffsetX: getShadowOffsetX(),
        shadowOffsetY: getShadowOffsetY(),
      }"
    />

    <!-- Polyline and SMART connections (both use v-line with waypoints) -->
    <v-line
      v-else
      :config="{
        points: flattenedPoints,
        stroke: getStrokeColor(),
        strokeWidth: getLineWidth(),
        lineCap: getLineCap(),
        lineJoin: getLineJoin(),
        dash: getLineDash(),
        opacity: getLineOpacity(),
        shadowColor: getShadowColor(),
        shadowBlur: getShadowBlur(),
        shadowOffsetX: getShadowOffsetX(),
        shadowOffsetY: getShadowOffsetY(),
      }"
    />

    <!-- Jump symbols for line intersections -->
    <template v-if="shouldShowJumps">
      <v-group v-for="(jump, index) in jumpPoints" :key="`jump-${index}`">
        <!-- Arc jump -->
        <v-arc
          v-if="getJumpStyle() === 'arc'"
          :config="{
            x: jump.position.x,
            y: jump.position.y,
            radius: getJumpSize(),
            angle: jump.angle,
            rotation: jump.rotation,
            stroke: getStrokeColor(),
            strokeWidth: getLineWidth(),
            lineCap: getLineCap(),
            dash: getLineDash(),
            opacity: getLineOpacity(),
          }"
        />

        <!-- Gap jump (nothing to render, the gap is created by the line segments) -->

        <!-- Square jump -->
        <v-rect
          v-if="getJumpStyle() === 'square'"
          :config="{
            x: jump.position.x - getJumpSize() / 2,
            y: jump.position.y - getJumpSize() / 2,
            width: getJumpSize(),
            height: getJumpSize(),
            rotation: jump.rotation,
            stroke: getStrokeColor(),
            strokeWidth: getLineWidth(),
            fill: 'white',
            opacity: getLineOpacity(),
          }"
        />
      </v-group>
    </template>

    <!-- Power flow animation -->
    <power-flow-animation-component
      :points="points"
      :flattened-points="flattenedPoints"
      :bezier-path="bezierPath"
      :connection-type="connectionType"
      :animation="getAnimation()"
      :line-width="getLineWidth()"
      :line-cap="getLineCap()"
      :line-join="getLineJoin()"
      :stroke-color="getStrokeColor()"
    />

    <!-- Waypoint editor for selected connections -->
    <connection-waypoint-editor
      v-if="isSelected && !isLocked && !readOnly"
      :connection="connection"
      :source-position="sourcePosition"
      :target-position="targetPosition"
      :is-selected="isSelected"
      :is-locked="isLocked"
      :read-only="readOnly"
      @waypoint-move="handleWaypointMove"
      @waypoint-add="handleWaypointAdd"
      @waypoint-remove="handleWaypointRemove"
      @control-point-move="handleControlPointMove"
      @context-menu="handleWaypointContextMenu"
    />

    <!-- Connection labels -->
    <template v-if="connection.labels && connection.labels.length > 0">
      <connection-label
        v-for="label in connection.labels"
        :key="label.id"
        :label="label"
        :points="points"
        :read-only="readOnly"
        :selected="selectedLabelId === label.id"
        @update:label="updateLabel"
        @select="selectLabel"
        @edit="editLabel"
      />
    </template>

    <!-- Legacy label support -->
    <template v-else-if="connection.label">
      <connection-label
        :label="{
          id: `${connection.id}-label`,
          text: connection.label,
          position: LabelPosition.MIDDLE,
          alignment: LabelAlignment.ABOVE,
          style: defaultLabelStyle,
        }"
        :points="points"
        :read-only="readOnly"
        :selected="selectedLabelId === `${connection.id}-label`"
        @update:label="updateLegacyLabel"
        @select="selectLabel"
        @edit="editLabel"
      />
    </template>

    <!-- Connection annotations -->
    <template v-if="connection.annotations && connection.annotations.length > 0">
      <connection-annotation
        v-for="annotation in connection.annotations"
        :key="annotation.id"
        :annotation="annotation"
        :points="points"
        :read-only="readOnly"
        :selected="selectedAnnotationId === annotation.id"
        @update:annotation="updateAnnotation"
        @select="selectAnnotation"
        @edit="editAnnotation"
      />
    </template>
  </v-group>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, watch, nextTick } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import {
  Connection,
  ConnectionLineType,
  LineCapStyle,
  LineJoinStyle,
  JumpStyle,
  PowerFlowAnimation,
  PowerFlowAnimationType,
  PowerFlowDirection,
  defaultRoutingOptions,
  ConnectionLabel as ConnectionLabelType,
  ConnectionAnnotation as ConnectionAnnotationType,
  LabelPosition,
  LabelAlignment,
  AnnotationType,
  defaultLabelStyle,
  defaultAnnotationStyle,
  createConnectionLabel,
  createConnectionAnnotation
} from '@/types/connection';
import { Position } from '@/types/symbol';
import { getConnectionPointPosition } from '@/types/symbolInstance';
import {
  getBusbarConnectionPoints,
  getBusbarConnectionPointPosition,
  isBusbarSymbol
} from '@/utils/busbarUtils';

import { findPath, RoutingStrategy } from '@/utils/pathFinding';
import {
  detectLineIntersections,
  addWaypoint,
  removeWaypoint,
  calculateBezierControlPoints,
  findPathIntersections,
  createJumpOverCrossings
} from '@/utils/connectionUtils';
import {
  bundleConnections,
  calculateBundleOffsets,
  applyBundleOffset
} from '@/utils/connectionBundling';
import ConnectionWaypointEditor from './ConnectionWaypointEditor.vue';
import WaypointContextMenu from '../menus/WaypointContextMenu.vue';
// Import the PowerFlowAnimation component (different from the type with the same name)
import PowerFlowAnimationComponent from './PowerFlowAnimation.vue';
import ConnectionLabel from './ConnectionLabel.vue';
import ConnectionAnnotation from './ConnectionAnnotation.vue';

// Props
const props = withDefaults(defineProps<{
  connection: Connection;
  sourcePosition?: Position;
  targetPosition?: Position;
  isSelected: boolean;
  isLocked: boolean;
  readOnly?: boolean;
  showSuccessFeedback?: boolean;
}>(), {
  sourcePosition: () => ({ x: 0, y: 0 }),
  targetPosition: () => ({ x: 0, y: 0 }),
  showSuccessFeedback: false
});

// Emits
const emit = defineEmits<{
  (e: 'select', id: string): void;
  (e: 'deselect', id: string): void;
  (e: 'waypointMove', connectionId: string, waypointIndex: number, position: Position): void;
  (e: 'updateConnection', connection: Connection): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const isHovered = ref(false);

// State for smart connections
const smartPathWaypoints = ref<Position[]>([]);

// State for labels and annotations
const selectedLabelId = ref<string | null>(null);
const selectedAnnotationId = ref<string | null>(null);
const isEditingLabel = ref(false);
const isEditingAnnotation = ref(false);

// State for jump points
const jumpPoints = ref<Array<{
  position: Position;
  angle: number;
  rotation: number;
}>>([]);

// State for waypoint context menu
const showWaypointContextMenu = ref(false);
const contextMenuWaypointIndex = ref(0);
const contextMenuPosition = ref<Position>({ x: 0, y: 0 });

// Whether to show jump symbols
const shouldShowJumps = computed(() => {
  return props.connection.style.jumpStyle !== undefined &&
         props.connection.style.jumpStyle !== JumpStyle.NONE;
});

// Computed
const connectionType = computed(() => {
  // For SMART connections, treat them as polylines for rendering purposes
  // since they use waypoints to create multi-segment paths
  if (props.connection.lineType === ConnectionLineType.SMART) {
    return ConnectionLineType.POLYLINE;
  }
  return props.connection.lineType;
});

const waypoints = computed(() => {
  if (props.connection.lineType === ConnectionLineType.STRAIGHT) {
    return [];
  } else if (props.connection.lineType === ConnectionLineType.POLYLINE) {
    return props.connection.waypoints || [];
  } else if (props.connection.lineType === ConnectionLineType.SMART) {
    return smartPathWaypoints.value;
  } else {
    return props.connection.controlPoints || [];
  }
});

// Calculate adjusted connection points to avoid visual overlap with symbol borders
const getAdjustedConnectionPoint = (
  position: Position,
  symbolInstanceId: string,
  connectionPointId: string,
  isSource: boolean
): Position => {
  if (!diagramStore.currentDiagram) return position;

  try {
    const symbol = diagramStore.currentDiagram.symbolInstances[symbolInstanceId];
    if (!symbol) return position;

    const definition = diagramStore.getSymbolDefinition(symbol.definitionId);
    if (!definition) return position;

    // Special handling for busbar symbols - no adjustment needed for perpendicular connections
    if (isBusbarSymbol(definition)) {
      // For busbar symbols, return the exact connection point position for tight perpendicular connections
      return position;
    }

    // Get the symbol's stroke width for offset calculation
    const symbolStrokeWidth = definition.properties.lineWidth || 1;
    const connectionStrokeWidth = getLineWidth();

    // Calculate offset to prevent visual overlap
    // Use half of the symbol's stroke width plus a small buffer
    const offset = (symbolStrokeWidth / 2) + 1;

    // Find the connection point definition to determine direction
    const connectionPoints = isBusbarSymbol(definition)
      ? getBusbarConnectionPoints(symbol, definition)
      : definition.connectionPoints;

    const connectionPoint = connectionPoints.find(cp => cp.id === connectionPointId);
    if (!connectionPoint) return position;

    // Calculate the direction vector from symbol center to connection point
    const symbolCenter = {
      x: symbol.position.x + (definition.dimensions.width / 2) * symbol.scale,
      y: symbol.position.y + (definition.dimensions.height / 2) * symbol.scale
    };

    const dx = position.x - symbolCenter.x;
    const dy = position.y - symbolCenter.y;
    const length = Math.sqrt(dx * dx + dy * dy);

    if (length === 0) return position;

    // Normalize the direction vector
    const normalizedDx = dx / length;
    const normalizedDy = dy / length;

    // Apply offset in the direction away from the symbol center
    return {
      x: position.x + normalizedDx * offset,
      y: position.y + normalizedDy * offset
    };
  } catch (error) {
    console.warn('Error calculating adjusted connection point:', error);
    return position;
  }
};

const points = computed(() => {
  const rawSource = props.sourcePosition;
  const rawTarget = props.targetPosition;

  // Get adjusted connection points to avoid visual overlap
  const source = getAdjustedConnectionPoint(
    rawSource,
    props.connection.source.symbolInstanceId,
    props.connection.source.connectionPointId,
    true
  );

  const target = getAdjustedConnectionPoint(
    rawTarget,
    props.connection.target.symbolInstanceId,
    props.connection.target.connectionPointId,
    false
  );

  if (props.connection.lineType === ConnectionLineType.STRAIGHT) {
    return [source, target];
  } else if (props.connection.lineType === ConnectionLineType.POLYLINE) {
    return [source, ...waypoints.value, target];
  } else if (props.connection.lineType === ConnectionLineType.SMART) {
    // For smart connections, use the calculated path
    return [source, ...waypoints.value, target];
  } else {
    // For bezier curves, we still need the points for calculations
    return [source, ...waypoints.value, target];
  }
});

const flattenedPoints = computed(() => {
  // Convert array of {x, y} to flat array [x1, y1, x2, y2, ...]
  return points.value.flatMap(p => [p.x, p.y]);
});

const bezierPath = computed(() => {
  // 默认路径，确保始终有一个有效的 SVG 路径
  const defaultPath = 'M0,0 L1,1';

  try {
    if (props.connection.lineType !== ConnectionLineType.BEZIER) {
      return defaultPath;
    }

    // 确保源点和目标点存在且有效
    if (!props.sourcePosition || !props.targetPosition ||
        typeof props.sourcePosition.x !== 'number' ||
        typeof props.sourcePosition.y !== 'number' ||
        typeof props.targetPosition.x !== 'number' ||
        typeof props.targetPosition.y !== 'number') {
      console.warn('Source or target position is invalid for bezier path');
      return defaultPath;
    }

    const source = props.sourcePosition;
    const target = props.targetPosition;

    // 确保坐标是有限数值
    if (!isFinite(source.x) || !isFinite(source.y) ||
        !isFinite(target.x) || !isFinite(target.y)) {
      console.warn('Source or target position contains non-finite values');
      return defaultPath;
    }

    const controlPoints = props.connection.controlPoints || [];

    // 构建路径字符串
    let pathData = `M${source.x},${source.y} `;

    if (controlPoints.length === 0) {
      // 默认控制点
      const dx = target.x - source.x;
      const dy = target.y - source.y;
      const cp1 = { x: source.x + dx / 3, y: source.y };
      const cp2 = { x: target.x - dx / 3, y: target.y };

      if (isFinite(cp1.x) && isFinite(cp1.y) && isFinite(cp2.x) && isFinite(cp2.y)) {
        pathData += `C${cp1.x},${cp1.y} ${cp2.x},${cp2.y} ${target.x},${target.y}`;
      } else {
        // 如果控制点无效，退回到直线
        pathData += `L${target.x},${target.y}`;
      }
    } else if (controlPoints.length === 1) {
      // 二次贝塞尔曲线
      const cp = controlPoints[0];
      if (cp && typeof cp.x === 'number' && typeof cp.y === 'number' &&
          isFinite(cp.x) && isFinite(cp.y)) {
        pathData += `Q${cp.x},${cp.y} ${target.x},${target.y}`;
      } else {
        // 如果控制点无效，退回到直线
        pathData += `L${target.x},${target.y}`;
      }
    } else {
      // 三次贝塞尔曲线
      const cp1 = controlPoints[0];
      const cp2 = controlPoints[1];

      if (cp1 && cp2 &&
          typeof cp1.x === 'number' && typeof cp1.y === 'number' &&
          typeof cp2.x === 'number' && typeof cp2.y === 'number' &&
          isFinite(cp1.x) && isFinite(cp1.y) &&
          isFinite(cp2.x) && isFinite(cp2.y)) {
        pathData += `C${cp1.x},${cp1.y} ${cp2.x},${cp2.y} ${target.x},${target.y}`;
      } else {
        // 如果控制点无效，退回到直线
        pathData += `L${target.x},${target.y}`;
      }
    }

    return pathData;
  } catch (error) {
    console.error('Error generating bezier path:', error);
    return defaultPath;
  }
});

const labelPosition = computed(() => {
  // Calculate the middle point of the connection for label placement
  const allPoints = points.value;
  const middleIndex = Math.floor(allPoints.length / 2);

  if (allPoints.length % 2 === 0) {
    // Even number of points, interpolate between the two middle points
    const p1 = allPoints[middleIndex - 1];
    const p2 = allPoints[middleIndex];
    return {
      x: (p1.x + p2.x) / 2,
      y: (p1.y + p2.y) / 2,
    };
  } else {
    // Odd number of points, use the middle point
    return allPoints[middleIndex];
  }
});

// Methods
const getStrokeColor = () => {
  // 如果显示成功反馈，使用绿色
  if (props.showSuccessFeedback) {
    return '#52c41a'; // 绿色成功反馈
  }

  // 如果连接被选中，使用高亮颜色
  if (props.isSelected) {
    return '#1890ff'; // 蓝色高亮
  }

  // 如果连接被锁定，使用灰色
  if (props.isLocked) {
    return '#d9d9d9'; // 灰色
  }

  // 使用连接样式中定义的颜色，如果没有定义，使用默认颜色
  return props.connection.style.strokeColor || '#000000';
};

const getLineWidth = () => {
  // 如果显示成功反馈，使用较粗的线宽
  if (props.showSuccessFeedback) {
    return 3; // 成功反馈时的线宽
  }

  // Get the connection's configured line width
  let width = props.connection.style.lineWidth;

  // If no line width is specified, try to match the connected symbols' stroke width
  if (!width || !isFinite(width)) {
    width = getMatchingSymbolStrokeWidth();
  }

  // Ensure we have a valid number, default to 1
  return typeof width === 'number' && isFinite(width) ? width : 1;
};

// Get stroke width from connected symbols for visual consistency
const getMatchingSymbolStrokeWidth = (): number => {
  if (!diagramStore.currentDiagram) return 1;

  try {
    // Get source symbol
    const sourceSymbol = diagramStore.currentDiagram.symbolInstances[props.connection.source.symbolInstanceId];
    if (sourceSymbol) {
      const sourceDefinition = diagramStore.getSymbolDefinition(sourceSymbol.definitionId);
      if (sourceDefinition?.properties?.lineWidth) {
        return sourceDefinition.properties.lineWidth;
      }
    }

    // Get target symbol as fallback
    const targetSymbol = diagramStore.currentDiagram.symbolInstances[props.connection.target.symbolInstanceId];
    if (targetSymbol) {
      const targetDefinition = diagramStore.getSymbolDefinition(targetSymbol.definitionId);
      if (targetDefinition?.properties?.lineWidth) {
        return targetDefinition.properties.lineWidth;
      }
    }
  } catch (error) {
    console.warn('Error getting symbol stroke width:', error);
  }

  return 1; // Default fallback
};

// Cache the calculated line width to maintain consistency during updates
const cachedLineWidth = ref<number | null>(null);

// Flag to prevent infinite recursion in line width calculation
const isProcessingLineWidth = ref(false);

// Function to ensure line width consistency during updates
const ensureLineWidthConsistency = () => {
  // Prevent infinite recursion by checking if we're already processing
  if (isProcessingLineWidth.value) {
    return null;
  }

  try {
    isProcessingLineWidth.value = true;

    // Get the current matched width from symbols
    const matchedWidth = getMatchingSymbolStrokeWidth();

    // Check if we need to update the connection's line width
    const currentLineWidth = props.connection.style.lineWidth;
    const shouldUpdate = !currentLineWidth ||
                        !isFinite(currentLineWidth) ||
                        Math.abs(currentLineWidth - matchedWidth) > 0.1; // Allow small tolerance

    if (shouldUpdate && matchedWidth !== 1) { // Only update if we found a meaningful width
      console.log(`Updating connection line width from ${currentLineWidth} to ${matchedWidth}`);

      // Update the connection style to persist the matched width
      const updatedConnection = {
        ...props.connection,
        style: {
          ...props.connection.style,
          lineWidth: matchedWidth
        }
      };

      // Use nextTick to avoid immediate re-rendering issues
      nextTick(() => {
        diagramStore.updateConnectionWithValidation(updatedConnection);
      });

      return matchedWidth;
    }

    return currentLineWidth || matchedWidth;
  } catch (error) {
    console.warn('Error in ensureLineWidthConsistency:', error);
    return null;
  } finally {
    isProcessingLineWidth.value = false;
  }
};

const getLineDash = () => {
  return props.connection.style.lineDash;
};

const getLineOpacity = () => {
  return props.connection.style.lineOpacity !== undefined ? props.connection.style.lineOpacity : 1;
};

const getLineCap = () => {
  // Use round caps for better visual integration at connection points
  // This helps create seamless transitions between connection lines and symbols
  return props.connection.style.lineCap || LineCapStyle.ROUND;
};

const getLineJoin = () => {
  // Use round joins for smoother corners in orthogonal routing
  // This provides better visual quality for right-angled connections
  return props.connection.style.lineJoin || LineJoinStyle.ROUND;
};

const getJumpStyle = () => {
  return props.connection.style.jumpStyle || JumpStyle.NONE;
};

const getJumpSize = () => {
  return props.connection.style.jumpSize || 10;
};

const getShadowColor = () => {
  // 如果显示成功反馈，使用绿色阴影
  if (props.showSuccessFeedback) {
    return 'rgba(82, 196, 26, 0.6)'; // 绿色阴影
  }
  return props.connection.style.shadowColor;
};

const getShadowBlur = () => {
  // 如果显示成功反馈，使用较大的阴影模糊
  if (props.showSuccessFeedback) {
    return 10;
  }
  return props.connection.style.shadowBlur;
};

const getShadowOffsetX = () => {
  return props.connection.style.shadowOffset?.x || 0;
};

const getShadowOffsetY = () => {
  return props.connection.style.shadowOffset?.y || 0;
};

const getAnimation = (): PowerFlowAnimation => {
  if (!props.connection.style.animation) {
    // Default animation if none is specified
    return {
      type: PowerFlowAnimationType.NONE,
      direction: PowerFlowDirection.FORWARD,
      speed: 5,
      enabled: false
    };
  }
  return props.connection.style.animation;
};

const handleClick = (e: any) => {
  e.cancelBubble = true; // Prevent event bubbling

  // Toggle selection
  if (props.isSelected) {
    emit('deselect', props.connection.id);
  } else {
    emit('select', props.connection.id);
  }
};

const handleMouseEnter = () => {
  isHovered.value = true;
};

const handleMouseLeave = () => {
  isHovered.value = false;
};

const handleWaypointMove = (connectionId: string, waypointIndex: number, position: Position) => {
  emit('waypointMove', connectionId, waypointIndex, position);
};

const handleWaypointAdd = (connectionId: string, position: Position, index: number) => {
  if (!diagramStore.currentDiagram) return;

  // Get the connection
  const connection = diagramStore.currentDiagram.connections[connectionId];
  if (!connection) return;

  // Add the waypoint
  const updatedConnection = addWaypoint(connection, position, index);

  // Update the connection
  diagramStore.updateConnectionWithValidation(updatedConnection);
};

const handleWaypointRemove = (connectionId: string, waypointIndex: number) => {
  if (!diagramStore.currentDiagram) return;

  // Get the connection
  const connection = diagramStore.currentDiagram.connections[connectionId];
  if (!connection) return;

  // Remove the waypoint
  const updatedConnection = removeWaypoint(connection, waypointIndex);

  // Update the connection
  diagramStore.updateConnectionWithValidation(updatedConnection);
};

const handleControlPointMove = (connectionId: string, controlPointIndex: number, position: Position) => {
  if (!diagramStore.currentDiagram) return;

  // Get the connection
  const connection = diagramStore.currentDiagram.connections[connectionId];
  if (!connection || !connection.controlPoints) return;

  // Update the control point
  const updatedControlPoints = [...connection.controlPoints];
  updatedControlPoints[controlPointIndex] = position;

  // Update the connection
  diagramStore.updateConnectionWithValidation({
    ...connection,
    controlPoints: updatedControlPoints
  });
};

const handleWaypointContextMenu = (event: MouseEvent, waypointIndex: number) => {
  // Show the context menu at the event position
  contextMenuPosition.value = {
    x: event.clientX,
    y: event.clientY
  };

  // Set the waypoint index
  contextMenuWaypointIndex.value = waypointIndex;

  // Show the context menu
  showWaypointContextMenu.value = true;

  // Prevent default context menu
  event.preventDefault();
};

const closeWaypointContextMenu = () => {
  showWaypointContextMenu.value = false;
};

// Calculate distance between two points
const calculateDistance = (point1: Position, point2: Position): number => {
  return Math.sqrt(Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2));
};

// Mandatory orthogonal routing for all connections
// All connections must use orthogonal (vertical and horizontal) line segments only
const shouldUseOrthogonalRouting = (
  source: Position,
  target: Position,
  options: ConnectionRoutingOptions,
  obstacles: any[] = []
): boolean => {
  // MANDATORY: All connections must use orthogonal routing
  // Diagonal lines are prohibited regardless of symbol proximity or distance
  return true;
};

// Calculate distance from a point to a line segment
const distanceFromPointToLine = (point: Position, lineStart: Position, lineEnd: Position): number => {
  const A = point.x - lineStart.x;
  const B = point.y - lineStart.y;
  const C = lineEnd.x - lineStart.x;
  const D = lineEnd.y - lineStart.y;

  const dot = A * C + B * D;
  const lenSq = C * C + D * D;

  if (lenSq === 0) {
    // Line start and end are the same point
    return calculateDistance(point, lineStart);
  }

  let param = dot / lenSq;

  let xx, yy;

  if (param < 0) {
    xx = lineStart.x;
    yy = lineStart.y;
  } else if (param > 1) {
    xx = lineEnd.x;
    yy = lineEnd.y;
  } else {
    xx = lineStart.x + param * C;
    yy = lineStart.y + param * D;
  }

  const dx = point.x - xx;
  const dy = point.y - yy;
  return Math.sqrt(dx * dx + dy * dy);
};

// STRICT ORTHOGONAL ROUTING - NO DIAGONAL LINES, NO SYMBOL OCCLUSION
const calculateStrictOrthogonalPath = (source: Position, target: Position, obstacles: SymbolInstance[], options: ConnectionRoutingOptions): Position[] => {
  console.log(`Calculating strict orthogonal path from (${source.x}, ${source.y}) to (${target.x}, ${target.y})`);

  // Try direct orthogonal path (0 bends)
  const directPath = attemptDirectOrthogonalPath(source, target, obstacles);
  if (directPath) {
    console.log('Direct orthogonal path found');
    return directPath;
  }

  // Try L-shaped path with obstacle avoidance (1 bend)
  const lShapedPath = attemptLShapedPathWithAvoidance(source, target, obstacles);
  if (lShapedPath) {
    console.log('L-shaped orthogonal path found');
    return lShapedPath;
  }

  // Use advanced obstacle avoidance (multiple bends)
  const advancedPath = calculateAdvancedObstacleAvoidancePath(source, target, obstacles);
  console.log('Advanced obstacle avoidance path calculated');
  return advancedPath;
};

// Attempt direct orthogonal connection (0 bends)
const attemptDirectOrthogonalPath = (source: Position, target: Position, obstacles: SymbolInstance[]): Position[] | null => {
  const dx = target.x - source.x;
  const dy = target.y - source.y;

  // Check if points are orthogonally aligned
  if (Math.abs(dx) < 5) {
    // Vertically aligned - check for obstacles in vertical path
    const path = [source, target];
    if (!pathIntersectsObstacles(path, obstacles)) {
      return path;
    }
  } else if (Math.abs(dy) < 5) {
    // Horizontally aligned - check for obstacles in horizontal path
    const path = [source, target];
    if (!pathIntersectsObstacles(path, obstacles)) {
      return path;
    }
  }

  return null; // Direct path not possible
};

// Attempt L-shaped path with obstacle avoidance (1 bend)
const attemptLShapedPathWithAvoidance = (source: Position, target: Position, obstacles: SymbolInstance[]): Position[] | null => {
  const dx = target.x - source.x;
  const dy = target.y - source.y;

  // Try horizontal-first L-shape
  const horizontalFirstPath = [
    source,
    { x: target.x, y: source.y },
    target
  ];

  if (!pathIntersectsObstacles(horizontalFirstPath, obstacles)) {
    return horizontalFirstPath;
  }

  // Try vertical-first L-shape
  const verticalFirstPath = [
    source,
    { x: source.x, y: target.y },
    target
  ];

  if (!pathIntersectsObstacles(verticalFirstPath, obstacles)) {
    return verticalFirstPath;
  }

  return null; // L-shaped path not possible
};

// Calculate advanced obstacle avoidance path with multiple waypoints
const calculateAdvancedObstacleAvoidancePath = (source: Position, target: Position, obstacles: SymbolInstance[]): Position[] => {
  console.log('Calculating advanced obstacle avoidance path');

  // Get obstacle boundaries with safety margins
  const obstacleRects = obstacles.map(obstacle => {
    const definition = getSymbolDefinition(obstacle.definitionId);
    if (!definition) return null;

    const margin = 20; // Safety margin around obstacles
    return {
      x: obstacle.position.x - margin,
      y: obstacle.position.y - margin,
      width: definition.dimensions.width * obstacle.scale + 2 * margin,
      height: definition.dimensions.height * obstacle.scale + 2 * margin
    };
  }).filter(rect => rect !== null);

  // Find path around obstacles using orthogonal segments only
  return findOrthogonalPathAroundObstacles(source, target, obstacleRects);
};

// Find orthogonal path around obstacles
const findOrthogonalPathAroundObstacles = (source: Position, target: Position, obstacleRects: any[]): Position[] => {
  // Create a grid of potential waypoints around obstacles
  const waypoints = generateWaypointGrid(source, target, obstacleRects);

  // Use A* algorithm with orthogonal constraints
  const path = findOrthogonalAStar(source, target, waypoints, obstacleRects);

  return path;
};

// Generate waypoint grid around obstacles
const generateWaypointGrid = (source: Position, target: Position, obstacleRects: any[]): Position[] => {
  const waypoints: Position[] = [];

  // Add waypoints at obstacle corners and edges
  obstacleRects.forEach(rect => {
    // Add points around each obstacle
    waypoints.push(
      { x: rect.x, y: rect.y }, // Top-left
      { x: rect.x + rect.width, y: rect.y }, // Top-right
      { x: rect.x, y: rect.y + rect.height }, // Bottom-left
      { x: rect.x + rect.width, y: rect.y + rect.height }, // Bottom-right
      { x: rect.x, y: source.y }, // Left edge at source Y
      { x: rect.x + rect.width, y: source.y }, // Right edge at source Y
      { x: rect.x, y: target.y }, // Left edge at target Y
      { x: rect.x + rect.width, y: target.y }, // Right edge at target Y
      { x: source.x, y: rect.y }, // Top edge at source X
      { x: source.x, y: rect.y + rect.height }, // Bottom edge at source X
      { x: target.x, y: rect.y }, // Top edge at target X
      { x: target.x, y: rect.y + rect.height } // Bottom edge at target X
    );
  });

  // Add source and target aligned waypoints
  waypoints.push(
    { x: source.x, y: target.y },
    { x: target.x, y: source.y }
  );

  // Remove duplicates and invalid points
  return waypoints.filter((point, index, array) =>
    array.findIndex(p => Math.abs(p.x - point.x) < 1 && Math.abs(p.y - point.y) < 1) === index
  );
};

// A* pathfinding with strict orthogonal constraints
const findOrthogonalAStar = (source: Position, target: Position, waypoints: Position[], obstacleRects: any[]): Position[] => {
  // Simplified A* implementation for orthogonal paths
  // This is a basic implementation - can be enhanced further

  const allPoints = [source, ...waypoints, target];
  const graph = buildOrthogonalGraph(allPoints, obstacleRects);

  // Use Dijkstra's algorithm for simplicity
  const path = dijkstraOrthogonal(graph, source, target);

  return path || createEmergencyOrthogonalPath(source, target);
};

// Build graph with only orthogonal connections
const buildOrthogonalGraph = (points: Position[], obstacleRects: any[]): Map<string, { point: Position, neighbors: { point: Position, distance: number }[] }> => {
  const graph = new Map();

  points.forEach(point => {
    const key = `${point.x},${point.y}`;
    const neighbors: { point: Position, distance: number }[] = [];

    // Find orthogonal neighbors
    points.forEach(otherPoint => {
      if (point === otherPoint) return;

      // Check if connection is orthogonal
      const dx = Math.abs(otherPoint.x - point.x);
      const dy = Math.abs(otherPoint.y - point.y);

      if (dx < 1 || dy < 1) { // Orthogonal connection
        const path = [point, otherPoint];
        if (!pathIntersectsObstacleRects(path, obstacleRects)) {
          const distance = dx + dy; // Manhattan distance
          neighbors.push({ point: otherPoint, distance });
        }
      }
    });

    graph.set(key, { point, neighbors });
  });

  return graph;
};

// Dijkstra's algorithm for orthogonal pathfinding
const dijkstraOrthogonal = (graph: Map<string, any>, source: Position, target: Position): Position[] | null => {
  const distances = new Map<string, number>();
  const previous = new Map<string, Position | null>();
  const unvisited = new Set<string>();

  // Initialize
  graph.forEach((_, key) => {
    distances.set(key, Infinity);
    previous.set(key, null);
    unvisited.add(key);
  });

  const sourceKey = `${source.x},${source.y}`;
  const targetKey = `${target.x},${target.y}`;
  distances.set(sourceKey, 0);

  while (unvisited.size > 0) {
    // Find unvisited node with minimum distance
    let currentKey: string | null = null;
    let minDistance = Infinity;

    unvisited.forEach(key => {
      const distance = distances.get(key) || Infinity;
      if (distance < minDistance) {
        minDistance = distance;
        currentKey = key;
      }
    });

    if (!currentKey || minDistance === Infinity) break;

    unvisited.delete(currentKey);

    if (currentKey === targetKey) {
      // Reconstruct path
      const path: Position[] = [];
      let current: Position | null = target;

      while (current) {
        path.unshift(current);
        const key = `${current.x},${current.y}`;
        current = previous.get(key) || null;
      }

      return path;
    }

    // Update neighbors
    const currentNode = graph.get(currentKey);
    if (currentNode) {
      currentNode.neighbors.forEach(({ point: neighbor, distance: edgeDistance }: { point: Position, distance: number }) => {
        const neighborKey = `${neighbor.x},${neighbor.y}`;
        if (unvisited.has(neighborKey)) {
          const newDistance = minDistance + edgeDistance;
          if (newDistance < (distances.get(neighborKey) || Infinity)) {
            distances.set(neighborKey, newDistance);
            previous.set(neighborKey, currentNode.point);
          }
        }
      });
    }
  }

  return null; // No path found
};

// VALIDATION AND UTILITY FUNCTIONS

// Check if path intersects with obstacles
const pathIntersectsObstacles = (path: Position[], obstacles: SymbolInstance[]): boolean => {
  for (let i = 0; i < path.length - 1; i++) {
    const segment = { start: path[i], end: path[i + 1] };

    for (const obstacle of obstacles) {
      const definition = getSymbolDefinition(obstacle.definitionId);
      if (!definition) continue;

      const obstacleRect = {
        x: obstacle.position.x,
        y: obstacle.position.y,
        width: definition.dimensions.width * obstacle.scale,
        height: definition.dimensions.height * obstacle.scale
      };

      if (lineIntersectsRect(segment.start, segment.end, obstacleRect)) {
        return true;
      }
    }
  }
  return false;
};

// Check if path intersects with obstacle rectangles
const pathIntersectsObstacleRects = (path: Position[], obstacleRects: any[]): boolean => {
  for (let i = 0; i < path.length - 1; i++) {
    const segment = { start: path[i], end: path[i + 1] };

    for (const rect of obstacleRects) {
      if (lineIntersectsRect(segment.start, segment.end, rect)) {
        return true;
      }
    }
  }
  return false;
};

// Check if line segment intersects with rectangle
const lineIntersectsRect = (start: Position, end: Position, rect: { x: number, y: number, width: number, height: number }): boolean => {
  // Check if line segment intersects with any edge of the rectangle
  const rectEdges = [
    { start: { x: rect.x, y: rect.y }, end: { x: rect.x + rect.width, y: rect.y } }, // Top
    { start: { x: rect.x + rect.width, y: rect.y }, end: { x: rect.x + rect.width, y: rect.y + rect.height } }, // Right
    { start: { x: rect.x + rect.width, y: rect.y + rect.height }, end: { x: rect.x, y: rect.y + rect.height } }, // Bottom
    { start: { x: rect.x, y: rect.y + rect.height }, end: { x: rect.x, y: rect.y } } // Left
  ];

  for (const edge of rectEdges) {
    if (lineSegmentsIntersect(start, end, edge.start, edge.end)) {
      return true;
    }
  }

  // Also check if line passes through the rectangle
  return pointInRect(start, rect) || pointInRect(end, rect);
};

// Check if point is inside rectangle
const pointInRect = (point: Position, rect: { x: number, y: number, width: number, height: number }): boolean => {
  return point.x >= rect.x && point.x <= rect.x + rect.width &&
         point.y >= rect.y && point.y <= rect.y + rect.height;
};

// Check if two line segments intersect
const lineSegmentsIntersect = (p1: Position, p2: Position, p3: Position, p4: Position): boolean => {
  const denominator = (p4.y - p3.y) * (p2.x - p1.x) - (p4.x - p3.x) * (p2.y - p1.y);

  if (Math.abs(denominator) < 1e-10) {
    return false; // Lines are parallel
  }

  const ua = ((p4.x - p3.x) * (p1.y - p3.y) - (p4.y - p3.y) * (p1.x - p3.x)) / denominator;
  const ub = ((p2.x - p1.x) * (p1.y - p3.y) - (p2.y - p1.y) * (p1.x - p3.x)) / denominator;

  return ua >= 0 && ua <= 1 && ub >= 0 && ub <= 1;
};

// Validate orthogonal path
const validateOrthogonalPath = (path: Position[], obstacles: SymbolInstance[]): { isValid: boolean, issues: string[] } => {
  const issues: string[] = [];

  // Check if path has at least 2 points
  if (!path || path.length < 2) {
    issues.push('Path has insufficient points');
    return { isValid: false, issues };
  }

  // Check if all segments are orthogonal
  for (let i = 0; i < path.length - 1; i++) {
    const start = path[i];
    const end = path[i + 1];
    const dx = Math.abs(end.x - start.x);
    const dy = Math.abs(end.y - start.y);

    if (dx > 1 && dy > 1) {
      issues.push(`Segment ${i} is not orthogonal: dx=${dx}, dy=${dy}`);
    }
  }

  // Check for obstacle intersections
  if (pathIntersectsObstacles(path, obstacles)) {
    issues.push('Path intersects with obstacles');
  }

  return { isValid: issues.length === 0, issues };
};

// Enhanced obstacle avoidance path calculation
const calculateEnhancedObstacleAvoidancePath = (source: Position, target: Position, obstacles: SymbolInstance[], options: ConnectionRoutingOptions): Position[] => {
  console.log('Calculating enhanced obstacle avoidance path');

  // Try multiple strategies with increasing complexity
  const strategies = [
    () => attemptExpandedLShapedPath(source, target, obstacles),
    () => attemptZShapedPath(source, target, obstacles),
    () => attemptMultiSegmentPath(source, target, obstacles)
  ];

  for (const strategy of strategies) {
    const path = strategy();
    if (path && !pathIntersectsObstacles(path, obstacles)) {
      return path;
    }
  }

  // Fallback to advanced pathfinding
  return calculateAdvancedObstacleAvoidancePath(source, target, obstacles);
};

// Attempt expanded L-shaped path with larger offsets
const attemptExpandedLShapedPath = (source: Position, target: Position, obstacles: SymbolInstance[]): Position[] | null => {
  const offsets = [50, 100, 150]; // Try different offsets

  for (const offset of offsets) {
    // Try horizontal-first with offset
    const horizontalPath = [
      source,
      { x: target.x + (target.x > source.x ? offset : -offset), y: source.y },
      { x: target.x + (target.x > source.x ? offset : -offset), y: target.y },
      target
    ];

    if (!pathIntersectsObstacles(horizontalPath, obstacles)) {
      return horizontalPath;
    }

    // Try vertical-first with offset
    const verticalPath = [
      source,
      { x: source.x, y: target.y + (target.y > source.y ? offset : -offset) },
      { x: target.x, y: target.y + (target.y > source.y ? offset : -offset) },
      target
    ];

    if (!pathIntersectsObstacles(verticalPath, obstacles)) {
      return verticalPath;
    }
  }

  return null;
};

// Attempt Z-shaped path
const attemptZShapedPath = (source: Position, target: Position, obstacles: SymbolInstance[]): Position[] | null => {
  const midX = (source.x + target.x) / 2;
  const midY = (source.y + target.y) / 2;

  const zPath = [
    source,
    { x: midX, y: source.y },
    { x: midX, y: target.y },
    target
  ];

  if (!pathIntersectsObstacles(zPath, obstacles)) {
    return zPath;
  }

  return null;
};

// Attempt multi-segment path
const attemptMultiSegmentPath = (source: Position, target: Position, obstacles: SymbolInstance[]): Position[] | null => {
  // This is a simplified version - can be enhanced with more sophisticated logic
  const quarterX = source.x + (target.x - source.x) * 0.25;
  const threeQuarterX = source.x + (target.x - source.x) * 0.75;

  const multiPath = [
    source,
    { x: quarterX, y: source.y },
    { x: quarterX, y: target.y },
    { x: threeQuarterX, y: target.y },
    target
  ];

  if (!pathIntersectsObstacles(multiPath, obstacles)) {
    return multiPath;
  }

  return null;
};

// Minimize bends while avoiding obstacles
const minimizeBendsWhileAvoidingObstacles = (path: Position[], obstacles: SymbolInstance[]): Position[] => {
  if (!path || path.length <= 2) return path;

  // Try to remove unnecessary waypoints
  let optimizedPath = [...path];
  let changed = true;

  while (changed) {
    changed = false;

    for (let i = 1; i < optimizedPath.length - 1; i++) {
      // Try to remove waypoint i
      const testPath = [
        ...optimizedPath.slice(0, i),
        ...optimizedPath.slice(i + 1)
      ];

      // Check if the simplified path is still valid
      const validation = validateOrthogonalPath(testPath, obstacles);
      if (validation.isValid) {
        optimizedPath = testPath;
        changed = true;
        break;
      }
    }
  }

  return optimizedPath;
};

// Emergency orthogonal path (guaranteed to work)
const createEmergencyOrthogonalPath = (source: Position, target: Position): Position[] => {
  console.log('Creating emergency orthogonal path');

  // Simple L-shaped path as emergency fallback
  const dx = target.x - source.x;
  const dy = target.y - source.y;

  if (Math.abs(dx) > Math.abs(dy)) {
    // Horizontal first
    return [
      source,
      { x: target.x, y: source.y },
      target
    ];
  } else {
    // Vertical first
    return [
      source,
      { x: source.x, y: target.y },
      target
    ];
  }
};

// Create optimized orthogonal path with bend minimization
// All connections use orthogonal routing with minimal bends for professional electrical schematic style
const createSimpleOrthogonalPath = (source: Position, target: Position, options: ConnectionRoutingOptions): Position[] => {
  const dx = target.x - source.x;
  const dy = target.y - source.y;
  const distance = calculateDistance(source, target);

  // Get minimum distance threshold for direct connection
  const minDirectDistance = options.minOrthogonalDistance || 30;

  // If the distance is very small, use direct connection (still orthogonal if aligned)
  if (distance < minDirectDistance) {
    // Check if points are already aligned orthogonally
    if (Math.abs(dx) < 5) {
      // Vertically aligned - direct vertical connection
      return [source, target];
    } else if (Math.abs(dy) < 5) {
      // Horizontally aligned - direct horizontal connection
      return [source, target];
    }
    // For very close non-aligned points, use simple L-shape
    return createMinimalBendPath(source, target, options);
  }

  // For all other distances, use bend minimization algorithm
  return createMinimalBendPath(source, target, options);
};

// Create minimal bend orthogonal path with intelligent routing
// Minimizes bends while maintaining orthogonal constraints and handling obstructions
const createMinimalBendPath = (source: Position, target: Position, options: ConnectionRoutingOptions): Position[] => {
  const dx = target.x - source.x;
  const dy = target.y - source.y;

  // Check if points are already orthogonally aligned (can use direct connection)
  if (Math.abs(dx) < 5) {
    // Vertically aligned - direct vertical connection (0 bends)
    return [source, target];
  }

  if (Math.abs(dy) < 5) {
    // Horizontally aligned - direct horizontal connection (0 bends)
    return [source, target];
  }

  // For non-aligned points, create L-shaped path (1 bend) - minimal bend solution
  // Choose direction based on larger distance for better visual flow
  if (Math.abs(dx) > Math.abs(dy)) {
    // Horizontal first, then vertical (L-shape)
    return [
      source,
      { x: target.x, y: source.y },
      target
    ];
  } else {
    // Vertical first, then horizontal (L-shape)
    return [
      source,
      { x: source.x, y: target.y },
      target
    ];
  }
};

// Create optimized orthogonal path for closely positioned elements with obstruction handling
// Uses intelligent routing to navigate around obstacles while minimizing bends
const createCloseProximityOrthogonalPath = (source: Position, target: Position, options: ConnectionRoutingOptions): Position[] => {
  const dx = target.x - source.x;
  const dy = target.y - source.y;

  // Minimum offset to ensure clean visual separation and avoid overlaps
  const minOffset = 25; // Increased for better visual separation
  const offsetMultiplier = 1.2; // Reduced for more compact routing

  // Calculate offsets based on direction and distance
  const horizontalOffset = Math.max(minOffset, Math.abs(dx) * 0.25) * offsetMultiplier;
  const verticalOffset = Math.max(minOffset, Math.abs(dy) * 0.25) * offsetMultiplier;

  // Determine primary direction based on larger distance
  const isPrimaryHorizontal = Math.abs(dx) > Math.abs(dy);

  if (isPrimaryHorizontal) {
    // Create horizontal-first path with proper offsets
    const intermediateX = source.x + (dx > 0 ? horizontalOffset : -horizontalOffset);

    // Use Z-shaped path for close proximity to avoid overlaps
    return [
      source,
      { x: intermediateX, y: source.y },
      { x: intermediateX, y: target.y },
      target
    ];
  } else {
    // Create vertical-first path with proper offsets
    const intermediateY = source.y + (dy > 0 ? verticalOffset : -verticalOffset);

    // Use Z-shaped path for close proximity to avoid overlaps
    return [
      source,
      { x: source.x, y: intermediateY },
      { x: target.x, y: intermediateY },
      target
    ];
  }
};

// Ensure path uses only orthogonal segments (convert diagonal segments to orthogonal)
const ensureOrthogonalPath = (path: Position[]): Position[] => {
  if (!Array.isArray(path) || path.length < 2) {
    return path;
  }

  const orthogonalPath: Position[] = [path[0]]; // Start with the first point

  for (let i = 1; i < path.length; i++) {
    const current = path[i];
    const previous = orthogonalPath[orthogonalPath.length - 1];

    const dx = current.x - previous.x;
    const dy = current.y - previous.y;

    // Check if the segment is already orthogonal
    if (Math.abs(dx) < 5) {
      // Vertically aligned - keep as is
      orthogonalPath.push(current);
    } else if (Math.abs(dy) < 5) {
      // Horizontally aligned - keep as is
      orthogonalPath.push(current);
    } else {
      // Diagonal segment - convert to orthogonal L-shape
      // Choose direction based on larger distance
      if (Math.abs(dx) > Math.abs(dy)) {
        // Horizontal first, then vertical
        orthogonalPath.push({ x: current.x, y: previous.y });
        orthogonalPath.push(current);
      } else {
        // Vertical first, then horizontal
        orthogonalPath.push({ x: previous.x, y: current.y });
        orthogonalPath.push(current);
      }
    }
  }

  return orthogonalPath;
};

// Optimize path by removing unnecessary waypoints
const optimizePath = (path: Position[], options: ConnectionRoutingOptions): Position[] => {
  if (path.length <= 3) {
    return path; // Can't optimize paths with 3 or fewer points
  }

  const optimized: Position[] = [path[0]]; // Always keep the start point

  for (let i = 1; i < path.length - 1; i++) {
    const prev = optimized[optimized.length - 1];
    const current = path[i];
    const next = path[i + 1];

    // Check if current point is necessary
    const isNecessary = isWaypointNecessary(prev, current, next, options);

    if (isNecessary) {
      optimized.push(current);
    }
  }

  optimized.push(path[path.length - 1]); // Always keep the end point

  return optimized;
};

// Check if a waypoint is necessary for the path
const isWaypointNecessary = (prev: Position, current: Position, next: Position, options: ConnectionRoutingOptions): boolean => {
  // Check if the three points are collinear (on the same line)
  const dx1 = current.x - prev.x;
  const dy1 = current.y - prev.y;
  const dx2 = next.x - current.x;
  const dy2 = next.y - current.y;

  // If the direction doesn't change significantly, the waypoint might be unnecessary
  const tolerance = 5; // pixels

  // Check for horizontal alignment
  if (Math.abs(dy1) < tolerance && Math.abs(dy2) < tolerance) {
    return false; // All points are roughly horizontal
  }

  // Check for vertical alignment
  if (Math.abs(dx1) < tolerance && Math.abs(dx2) < tolerance) {
    return false; // All points are roughly vertical
  }

  // Check if the waypoint creates a very small detour
  const directDistance = Math.sqrt((next.x - prev.x) ** 2 + (next.y - prev.y) ** 2);
  const viaCurrentDistance = Math.sqrt(dx1 ** 2 + dy1 ** 2) + Math.sqrt(dx2 ** 2 + dy2 ** 2);

  if (viaCurrentDistance - directDistance < 10) {
    return false; // The detour is too small to be meaningful
  }

  return true; // The waypoint is necessary
};

// UNIFIED SMART PATH CALCULATION - Single authoritative routing system
// This replaces all legacy routing mechanisms with mandatory orthogonal routing
const calculateUnifiedSmartPath = () => {
  // PERFORMANCE MONITORING: Track calculation frequency
  const startTime = performance.now();

  if (props.connection.lineType !== ConnectionLineType.SMART) {
    console.log(`Skipping path calculation for connection ${props.connection.id}: not SMART type (${props.connection.lineType})`);
    return;
  }

  console.log(`Starting unified smart path calculation for connection ${props.connection.id}`);

  // Get raw source and target positions
  const rawSource = props.sourcePosition;
  const rawTarget = props.targetPosition;

  // Get adjusted positions for visual consistency
  const source = getAdjustedConnectionPoint(
    rawSource,
    props.connection.source.symbolInstanceId,
    props.connection.source.connectionPointId,
    true
  );

  const target = getAdjustedConnectionPoint(
    rawTarget,
    props.connection.target.symbolInstanceId,
    props.connection.target.connectionPointId,
    false
  );

  // Validate source and target positions
  if (!source || !target ||
      typeof source.x !== 'number' || typeof source.y !== 'number' ||
      typeof target.x !== 'number' || typeof target.y !== 'number' ||
      !isFinite(source.x) || !isFinite(source.y) ||
      !isFinite(target.x) || !isFinite(target.y)) {
    console.warn('Invalid source or target position for unified smart path calculation', { source, target });
    return;
  }

  // Get all symbol instances as obstacles
  const obstacles = diagramStore.currentDiagram
    ? Object.values(diagramStore.currentDiagram.symbolInstances)
      // Filter out the source and target symbols
      .filter(symbol =>
        symbol.id !== props.connection.source.symbolInstanceId &&
        symbol.id !== props.connection.target.symbolInstanceId
      )
    : [];

  // MANDATORY: Use orthogonal routing options - override any legacy settings
  const routingOptions = {
    ...defaultRoutingOptions,
    ...props.connection.routingOptions,
    // FORCE orthogonal routing regardless of stored options
    routingStrategy: 'orthogonal',
    preferStraightLines: false, // Prioritize orthogonal over straight
    snapToGrid: false // Grid-independent routing
  };

  // STRICT ORTHOGONAL ROUTING WITH OBSTACLE AVOIDANCE
  let path;
  try {
    console.log(`Calculating strict orthogonal path for connection ${props.connection.id} with ${obstacles.length} obstacles`);

    path = calculateStrictOrthogonalPath(source, target, obstacles, routingOptions);

    const pathValidation = validateOrthogonalPath(path, obstacles);
    if (!pathValidation.isValid) {
      console.warn(`Path validation failed for connection ${props.connection.id}:`, pathValidation.issues);
      path = calculateEnhancedObstacleAvoidancePath(source, target, obstacles, routingOptions);
    }

    path = minimizeBendsWhileAvoidingObstacles(path, obstacles);

    console.log(`Strict orthogonal routing completed for connection ${props.connection.id}:`, {
      pathSegments: path.length - 1,
      bends: Math.max(0, path.length - 2),
      obstaclesAvoided: obstacles.length,
      pathValidated: true
    });

    if (!Array.isArray(path) || path.length < 2) {
      console.error('Critical error: Invalid path returned, using emergency fallback');
      path = createEmergencyOrthogonalPath(source, target);
    }
  } catch (error) {
    console.error('Error in strict orthogonal routing:', error);
    path = createEmergencyOrthogonalPath(source, target);
  }

  // Apply jump-over crossings if enabled
  if (routingOptions.jumpOverCrossings && diagramStore.currentDiagram) {
    try {
      // Get all other connections
      const otherConnections: Record<string, { id: string, waypoints: Position[] }> = {};

      for (const id in diagramStore.currentDiagram.connections) {
        if (id !== props.connection.id) {
          const conn = diagramStore.currentDiagram.connections[id];

          // Create waypoints array including source and target positions
          const sourceSymbol = diagramStore.currentDiagram.symbolInstances[conn.source.symbolInstanceId];
          const targetSymbol = diagramStore.currentDiagram.symbolInstances[conn.target.symbolInstanceId];

          if (!sourceSymbol || !targetSymbol) {
            continue; // Skip this connection if symbols are missing
          }

          const sourceDefinition = getSymbolDefinition(sourceSymbol.definitionId);
          const targetDefinition = getSymbolDefinition(targetSymbol.definitionId);

          if (!sourceDefinition || !targetDefinition) {
            continue; // Skip this connection if definitions are missing
          }

          const sourcePos = getConnectionPointPosition(
            sourceSymbol,
            sourceDefinition,
            conn.source.connectionPointId
          );

          const targetPos = getConnectionPointPosition(
            targetSymbol,
            targetDefinition,
            conn.target.connectionPointId
          );

          if (sourcePos && targetPos) {
            const waypoints = [sourcePos, ...(conn.waypoints || []), targetPos];
            otherConnections[id] = {
              id,
              waypoints
            };
          }
        }
      }

      // Find intersections
      const fullPath = [source, ...path.slice(1, -1), target];
      const intersections = findPathIntersections(fullPath, otherConnections);

      // Apply jump-over crossings
      if (intersections.length > 0) {
        const pathWithJumps = createJumpOverCrossings(
          fullPath,
          intersections,
          routingOptions.cornerRadius || 10
        );

        // Update path (excluding source and target)
        path = pathWithJumps.slice(1, -1);
      }
    } catch (error) {
      console.error('Error applying jump-over crossings:', error);
      // Continue with the original path
    }
  }

  // Optimize path to remove unnecessary waypoints
  if (routingOptions.optimizePath) {
    path = optimizePath(path, routingOptions);
  }

  // Update waypoints (excluding start and end points)
  // Ensure we have valid waypoints
  const newWaypoints = path.slice(1, -1);
  if (Array.isArray(newWaypoints)) {
    smartPathWaypoints.value = newWaypoints;
    console.log(`Smart path calculated for connection ${props.connection.id}:`, {
      source,
      target,
      waypoints: newWaypoints,
      totalPoints: newWaypoints.length + 2,
      optimized: routingOptions.optimizePath
    });
  }

  // PERFORMANCE MONITORING: Log calculation time
  const endTime = performance.now();
  const calculationTime = endTime - startTime;
  console.log(`Unified smart path calculation completed for connection ${props.connection.id} in ${calculationTime.toFixed(2)}ms`);
};

// Watch for changes in source/target positions - UNIFIED ROUTING AND LINE WIDTH CONSISTENCY
// PERFORMANCE OPTIMIZATION: Only recalculate when positions actually change
watch(
  [() => props.sourcePosition, () => props.targetPosition],
  (newValues, oldValues) => {
    const [newSource, newTarget] = newValues;
    const [oldSource, oldTarget] = oldValues || [null, null];

    // PERFORMANCE: Skip if positions are invalid
    if (!newSource || !newTarget ||
        typeof newSource.x !== 'number' || typeof newSource.y !== 'number' ||
        typeof newTarget.x !== 'number' || typeof newTarget.y !== 'number') {
      return;
    }

    // Only recalculate if positions actually changed
    if (oldSource && oldTarget && newSource && newTarget) {
      const sourceChanged = Math.abs(newSource.x - oldSource.x) > 0.1 || Math.abs(newSource.y - oldSource.y) > 0.1;
      const targetChanged = Math.abs(newTarget.x - oldTarget.x) > 0.1 || Math.abs(newTarget.y - oldTarget.y) > 0.1;

      if (sourceChanged || targetChanged) {
        console.log(`Recalculating unified smart path for connection ${props.connection.id} due to position change`);
        // Use nextTick to ensure proper sequencing
        nextTick(() => {
          ensureLineWidthConsistency();
          // 统一路由计算：确保只有智能连线才重新计算路径
          if (props.connection.lineType === ConnectionLineType.SMART) {
            calculateUnifiedSmartPath();
          }
        });
      }
    } else {
      // Initial calculation - ensure this runs for new connections
      console.log(`Initial unified smart path calculation for connection ${props.connection.id}`);
      nextTick(() => {
        ensureLineWidthConsistency();
        // 统一路由计算：确保只有智能连线才计算路径
        if (props.connection.lineType === ConnectionLineType.SMART) {
          calculateUnifiedSmartPath();
        }
      });
    }
  },
  { deep: true, immediate: true } // IMPORTANT: immediate: true ensures initial calculation
);

// Note: Position change handling is now unified in the main watch statement above

// Watch for changes in connection properties to reset cached line width
watch(
  () => props.connection,
  (newConnection, oldConnection) => {
    // Reset cached line width when connection changes
    if (oldConnection && newConnection.id !== oldConnection.id) {
      cachedLineWidth.value = null;
      isProcessingLineWidth.value = false;
    }

    // Reset cached line width if style changes
    if (oldConnection &&
        JSON.stringify(newConnection.style) !== JSON.stringify(oldConnection.style)) {
      cachedLineWidth.value = null;
      isProcessingLineWidth.value = false;
    }
  },
  { deep: true }
);

// Initialize line width consistency on mount
onMounted(() => {
  // Use nextTick to ensure component is fully mounted before checking line width
  nextTick(() => {
    ensureLineWidthConsistency();
  });
});

// Watch for changes in routing options - UNIFIED ROUTING
watch(
  () => props.connection.routingOptions,
  () => {
    if (props.connection.lineType === ConnectionLineType.SMART) {
      calculateUnifiedSmartPath();
    }
  },
  { deep: true }
);

// Calculate jump points for line intersections
const calculateJumpPoints = () => {
  if (!shouldShowJumps.value || !diagramStore.currentDiagram) {
    jumpPoints.value = [];
    return;
  }

  // Get all connections except this one
  const otherConnections = Object.values(diagramStore.currentDiagram.connections)
    .filter(conn => conn.id !== props.connection.id);

  // Get line segments for this connection
  const segments = [];
  for (let i = 0; i < points.value.length - 1; i++) {
    segments.push({
      start: points.value[i],
      end: points.value[i + 1]
    });
  }

  // Detect intersections with other connections
  const intersections = [];
  for (const connection of otherConnections) {
    // Skip if the connection has the same source or target
    if (
      connection.source.symbolInstanceId === props.connection.source.symbolInstanceId ||
      connection.source.symbolInstanceId === props.connection.target.symbolInstanceId ||
      connection.target.symbolInstanceId === props.connection.source.symbolInstanceId ||
      connection.target.symbolInstanceId === props.connection.target.symbolInstanceId
    ) {
      continue;
    }

    // Get source and target positions for the other connection
    const sourceSymbol = diagramStore.currentDiagram.symbolInstances[connection.source.symbolInstanceId];
    const targetSymbol = diagramStore.currentDiagram.symbolInstances[connection.target.symbolInstanceId];

    if (!sourceSymbol || !targetSymbol) continue;

    const sourceDefinition = getSymbolDefinition(sourceSymbol.definitionId);
    const targetDefinition = getSymbolDefinition(targetSymbol.definitionId);

    if (!sourceDefinition || !targetDefinition) continue;

    const sourcePosition = getConnectionPointPosition(
      sourceSymbol,
      sourceDefinition,
      connection.source.connectionPointId
    );

    const targetPosition = getConnectionPointPosition(
      targetSymbol,
      targetDefinition,
      connection.target.connectionPointId
    );

    if (!sourcePosition || !targetPosition) continue;

    // Check for intersections
    for (const segment of segments) {
      const intersection = detectLineIntersections(
        segment.start,
        segment.end,
        sourcePosition,
        targetPosition
      );

      if (intersection) {
        // Calculate angle and rotation for the jump
        const dx = segment.end.x - segment.start.x;
        const dy = segment.end.y - segment.start.y;
        const angle = Math.atan2(dy, dx) * 180 / Math.PI;

        intersections.push({
          position: intersection,
          angle: 180, // Full circle for arc
          rotation: angle
        });
      }
    }
  }

  jumpPoints.value = intersections;
};

// Label and annotation methods
const updateLabel = (label: ConnectionLabelType) => {
  if (!props.connection.labels) return;

  // Find the label index
  const labelIndex = props.connection.labels.findIndex(l => l.id === label.id);
  if (labelIndex === -1) return;

  // Create a new connection with the updated label
  const updatedConnection = { ...props.connection };
  const updatedLabels = [...props.connection.labels];
  updatedLabels[labelIndex] = label;
  updatedConnection.labels = updatedLabels;

  // Emit the updated connection
  emit('updateConnection', updatedConnection);
};

const updateLegacyLabel = (label: ConnectionLabelType) => {
  // Create a new connection with the updated label
  const updatedConnection = {
    ...props.connection,
    label: label.text,
    labels: [label]
  };

  // Emit the updated connection
  emit('updateConnection', updatedConnection);
};

const selectLabel = (labelId: string) => {
  selectedLabelId.value = labelId;
  selectedAnnotationId.value = null;
};

const editLabel = (labelId: string) => {
  selectedLabelId.value = labelId;
  isEditingLabel.value = true;
};

const updateAnnotation = (annotation: ConnectionAnnotationType) => {
  if (!props.connection.annotations) return;

  // Find the annotation index
  const annotationIndex = props.connection.annotations.findIndex(a => a.id === annotation.id);
  if (annotationIndex === -1) return;

  // Create a new connection with the updated annotation
  const updatedConnection = { ...props.connection };
  const updatedAnnotations = [...props.connection.annotations];
  updatedAnnotations[annotationIndex] = annotation;
  updatedConnection.annotations = updatedAnnotations;

  // Emit the updated connection
  emit('updateConnection', updatedConnection);
};

const selectAnnotation = (annotationId: string) => {
  selectedAnnotationId.value = annotationId;
  selectedLabelId.value = null;
};

const editAnnotation = (annotationId: string) => {
  selectedAnnotationId.value = annotationId;
  isEditingAnnotation.value = true;
};

// Calculate path on mount - UNIFIED ROUTING
onMounted(() => {
  // IMMEDIATE ORTHOGONAL ROUTING: Ensure new connections use orthogonal routing immediately
  console.log(`ConnectionLine mounted for connection ${props.connection.id}, lineType: ${props.connection.lineType}`);

  // Use nextTick to ensure component is fully mounted before calculating paths
  nextTick(() => {
    ensureLineWidthConsistency();
    // 统一路由计算：无论是初始化还是后续更新，都使用相同的函数
    if (props.connection.lineType === ConnectionLineType.SMART) {
      console.log(`Forcing immediate orthogonal routing calculation for new connection ${props.connection.id}`);
      calculateUnifiedSmartPath();
    }
    calculateJumpPoints();
  });
});

// Watch for changes that require recalculating jump points
watch(
  [() => props.sourcePosition, () => props.targetPosition, () => waypoints.value],
  () => {
    calculateJumpPoints();
  }
);

</script>
