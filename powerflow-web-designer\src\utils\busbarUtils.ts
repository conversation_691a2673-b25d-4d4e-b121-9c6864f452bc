/**
 * Busbar utility functions for PowerFlow Web Designer
 * Handles dynamic connection point generation and management
 */

import type {
  SymbolInstance,
  SymbolDefinition,
  ConnectionPoint,
  Position,
  Size,
  BusbarProperties
} from '../types/symbol';
import {
  ConnectionPointType,
  BusbarOrientation,
  generateBusbarConnectionPoints
} from '../types/symbol';

/**
 * Update busbar connection points based on current properties
 */
export function updateBusbarConnectionPoints(
  symbolInstance: SymbolInstance,
  symbolDefinition: SymbolDefinition,
  busbarProperties: BusbarProperties
): ConnectionPoint[] {
  // Calculate dimensions based on orientation
  const dimensions: Size = busbarProperties.orientation === BusbarOrientation.HORIZONTAL
    ? { width: busbarProperties.length, height: busbarProperties.width + 40 }
    : { width: busbarProperties.width + 40, height: busbarProperties.length };

  // Generate new connection points
  return generateBusbarConnectionPoints(
    busbarProperties.orientation,
    dimensions,
    busbarProperties.connectionPointCount,
    busbarProperties.autoDistributePoints
  );
}

/**
 * Get the effective busbar properties for a symbol instance
 */
export function getBusbarProperties(
  symbolInstance: SymbolInstance,
  symbolDefinition: SymbolDefinition
): BusbarProperties | null {
  // Check if this is a busbar symbol
  if (!isBusbarSymbol(symbolDefinition)) {
    return null;
  }

  // Get properties from instance or definition
  const instanceBusbarProps = symbolInstance.properties?.busbar;
  const definitionBusbarProps = symbolDefinition.properties?.busbar;

  if (instanceBusbarProps) {
    return instanceBusbarProps;
  } else if (definitionBusbarProps) {
    return definitionBusbarProps;
  }

  // Return default properties for legacy busbar symbols
  return {
    orientation: BusbarOrientation.HORIZONTAL,
    length: 200,
    width: 20,
    connectionPointCount: 3,
    connectionPointSpacing: 60,
    autoDistributePoints: true,
  };
}

/**
 * Check if a symbol definition represents a busbar
 */
export function isBusbarSymbol(symbolDefinition: SymbolDefinition): boolean {
  return symbolDefinition.category === 'busbar' || 
         symbolDefinition.id.includes('busbar');
}

/**
 * Get the effective connection points for a busbar symbol instance
 */
export function getBusbarConnectionPoints(
  symbolInstance: SymbolInstance,
  symbolDefinition: SymbolDefinition
): ConnectionPoint[] {
  const busbarProps = getBusbarProperties(symbolInstance, symbolDefinition);

  if (!busbarProps) {
    // Not a busbar, return original connection points
    return symbolDefinition.connectionPoints;
  }

  // For enhanced busbar symbols, generate dynamic connection points
  if (symbolDefinition.id.includes('busbar-') && symbolDefinition.connectionPoints.length === 0) {
    return updateBusbarConnectionPoints(symbolInstance, symbolDefinition, busbarProps);
  }

  // For legacy busbar or symbols with predefined connection points
  return symbolDefinition.connectionPoints.length > 0
    ? symbolDefinition.connectionPoints
    : updateBusbarConnectionPoints(symbolInstance, symbolDefinition, busbarProps);
}

/**
 * Calculate the absolute position of a connection point on a busbar instance
 * Ensures proper perpendicular alignment for connection lines
 */
export function getBusbarConnectionPointPosition(
  symbolInstance: SymbolInstance,
  symbolDefinition: SymbolDefinition,
  connectionPointId: string
): Position | null {
  const connectionPoints = getBusbarConnectionPoints(symbolInstance, symbolDefinition);
  const connectionPoint = connectionPoints.find(cp => cp.id === connectionPointId);

  if (!connectionPoint) {
    console.warn(`Connection point ${connectionPointId} not found for busbar ${symbolInstance.id}`);
    return null;
  }

  // Get busbar properties for precise positioning
  const busbarProps = getBusbarProperties(symbolInstance, symbolDefinition);
  if (!busbarProps) {
    console.warn(`Failed to get busbar properties for ${symbolInstance.id}`);
    return null;
  }

  // Calculate absolute position with proper scaling and rotation
  let absoluteX = symbolInstance.position.x + (connectionPoint.position.x * symbolInstance.scale);
  let absoluteY = symbolInstance.position.y + (connectionPoint.position.y * symbolInstance.scale);

  // Apply rotation if needed (for future rotation support)
  if (symbolInstance.rotation && symbolInstance.rotation !== 0) {
    const radians = (symbolInstance.rotation * Math.PI) / 180;
    const cos = Math.cos(radians);
    const sin = Math.sin(radians);

    // Rotate around symbol center
    const centerX = symbolInstance.position.x + (busbarProps.length / 2) * symbolInstance.scale;
    const centerY = symbolInstance.position.y + (busbarProps.width / 2) * symbolInstance.scale;

    const relativeX = absoluteX - centerX;
    const relativeY = absoluteY - centerY;

    absoluteX = centerX + (relativeX * cos - relativeY * sin);
    absoluteY = centerY + (relativeX * sin + relativeY * cos);
  }

  // Ensure connection points are positioned for perpendicular connections
  // For horizontal busbars, connection points should be exactly on the top/bottom edges
  // For vertical busbars, connection points should be exactly on the left/right edges
  if (busbarProps.orientation === BusbarOrientation.HORIZONTAL) {
    // Snap Y coordinate to busbar edges for perfect perpendicular alignment
    const busbarTop = symbolInstance.position.y + (20 * symbolInstance.scale); // Top edge
    const busbarBottom = symbolInstance.position.y + ((20 + busbarProps.width) * symbolInstance.scale); // Bottom edge

    if (connectionPoint.position.y <= 15) {
      absoluteY = busbarTop; // Top connection points
    } else {
      absoluteY = busbarBottom; // Bottom connection points
    }
  } else if (busbarProps.orientation === BusbarOrientation.VERTICAL) {
    // Snap X coordinate to busbar edges for perfect perpendicular alignment
    const busbarLeft = symbolInstance.position.x + (20 * symbolInstance.scale); // Left edge
    const busbarRight = symbolInstance.position.x + ((20 + busbarProps.width) * symbolInstance.scale); // Right edge

    if (connectionPoint.position.x <= 15) {
      absoluteX = busbarLeft; // Left connection points
    } else {
      absoluteX = busbarRight; // Right connection points
    }
  }

  const result = {
    x: Math.round(absoluteX * 100) / 100, // Round to 2 decimal places for precision
    y: Math.round(absoluteY * 100) / 100,
  };

  return result;
}

/**
 * Generate SVG for busbar based on properties - Clean appearance without visible connection points
 */
export function generateBusbarSVG(busbarProperties: BusbarProperties): string {
  const { orientation, length, width } = busbarProperties;

  if (orientation === BusbarOrientation.HORIZONTAL) {
    const viewBoxWidth = length;
    const viewBoxHeight = width + 40;
    const busbarY = 20;

    return `
      <svg viewBox="0 0 ${viewBoxWidth} ${viewBoxHeight}" xmlns="http://www.w3.org/2000/svg">
        <rect x="0" y="${busbarY}" width="${length}" height="${width}" fill="currentColor"/>
      </svg>
    `;
  } else {
    const viewBoxWidth = width + 40;
    const viewBoxHeight = length;
    const busbarX = 20;

    return `
      <svg viewBox="0 0 ${viewBoxWidth} ${viewBoxHeight}" xmlns="http://www.w3.org/2000/svg">
        <rect x="${busbarX}" y="0" width="${width}" height="${length}" fill="currentColor"/>
      </svg>
    `;
  }
}

/**
 * Generate SVG for busbar with connection point indicators (for preview/editing purposes)
 */
export function generateBusbarPreviewSVG(busbarProperties: BusbarProperties): string {
  const { orientation, length, width, connectionPointCount } = busbarProperties;

  if (orientation === BusbarOrientation.HORIZONTAL) {
    const viewBoxWidth = length;
    const viewBoxHeight = width + 40;
    const busbarY = 20;

    // Generate connection point indicators for preview
    const spacing = length / (connectionPointCount + 1);
    let connectionPointsHTML = '';

    for (let i = 0; i < connectionPointCount; i++) {
      const x = spacing * (i + 1);
      connectionPointsHTML += `
        <circle cx="${x}" cy="10" r="3" fill="none" stroke="currentColor" stroke-width="1" opacity="0.6"/>
        <circle cx="${x}" cy="${busbarY + width + 10}" r="3" fill="none" stroke="currentColor" stroke-width="1" opacity="0.6"/>
      `;
    }

    return `
      <svg viewBox="0 0 ${viewBoxWidth} ${viewBoxHeight}" xmlns="http://www.w3.org/2000/svg">
        <rect x="0" y="${busbarY}" width="${length}" height="${width}" fill="currentColor"/>
        ${connectionPointsHTML}
      </svg>
    `;
  } else {
    const viewBoxWidth = width + 40;
    const viewBoxHeight = length;
    const busbarX = 20;

    // Generate connection point indicators for preview
    const spacing = length / (connectionPointCount + 1);
    let connectionPointsHTML = '';

    for (let i = 0; i < connectionPointCount; i++) {
      const y = spacing * (i + 1);
      connectionPointsHTML += `
        <circle cx="10" cy="${y}" r="3" fill="none" stroke="currentColor" stroke-width="1" opacity="0.6"/>
        <circle cx="${busbarX + width + 10}" cy="${y}" r="3" fill="none" stroke="currentColor" stroke-width="1" opacity="0.6"/>
      `;
    }

    return `
      <svg viewBox="0 0 ${viewBoxWidth} ${viewBoxHeight}" xmlns="http://www.w3.org/2000/svg">
        <rect x="${busbarX}" y="0" width="${width}" height="${length}" fill="currentColor"/>
        ${connectionPointsHTML}
      </svg>
    `;
  }
}

/**
 * Validate busbar properties
 */
export function validateBusbarProperties(properties: Partial<BusbarProperties>): string[] {
  const errors: string[] = [];
  
  if (properties.length !== undefined && (properties.length < 50 || properties.length > 500)) {
    errors.push('母线长度必须在50-500之间');
  }
  
  if (properties.width !== undefined && (properties.width < 10 || properties.width > 50)) {
    errors.push('母线宽度必须在10-50之间');
  }
  
  if (properties.connectionPointCount !== undefined && 
      (properties.connectionPointCount < 1 || properties.connectionPointCount > 10)) {
    errors.push('连接点数量必须在1-10之间');
  }
  
  if (properties.connectionPointSpacing !== undefined && 
      (properties.connectionPointSpacing < 20 || properties.connectionPointSpacing > 100)) {
    errors.push('连接点间距必须在20-100之间');
  }
  
  return errors;
}
