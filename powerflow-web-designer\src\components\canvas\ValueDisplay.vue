<template>
  <v-group>
    <!-- Background for better readability -->
    <v-rect
      v-if="showBackground"
      :config="{
        x: -padding,
        y: -padding,
        width: width + padding * 2,
        height: height + padding * 2,
        fill: backgroundColor,
        cornerRadius: 3,
        opacity: backgroundOpacity,
      }"
    />

    <!-- Value text -->
    <v-text
      :config="{
        text: formattedValue,
        fontSize,
        fontFamily,
        fill: textColor,
        align: 'center',
        width,
        height,
      }"
    />

    <!-- Unit text (if provided) -->
    <v-text
      v-if="unit"
      :config="{
        text: unit,
        fontSize: unitFontSize,
        fontFamily,
        fill: textColor,
        align: 'left',
        x: width + 5,
        y: (height - unitFontSize) / 2,
      }"
    />
  </v-group>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

// Props
const props = defineProps<{
  value: number | string | null;
  format?: string;
  precision?: number;
  unit?: string;
  fontSize?: number;
  unitFontSize?: number;
  fontFamily?: string;
  textColor?: string;
  showBackground?: boolean;
  backgroundColor?: string;
  backgroundOpacity?: number;
  width?: number;
  height?: number;
  padding?: number;
}>();

// Default values
const fontSize = computed(() => props.fontSize || 14);
const unitFontSize = computed(() => props.unitFontSize || fontSize.value * 0.8);
const fontFamily = computed(() => props.fontFamily || 'Arial');
const textColor = computed(() => props.textColor || '#333333');
const showBackground = computed(() => props.showBackground !== undefined ? props.showBackground : true);
const backgroundColor = computed(() => props.backgroundColor || '#ffffff');
const backgroundOpacity = computed(() => props.backgroundOpacity !== undefined ? props.backgroundOpacity : 0.7);
const width = computed(() => props.width || 60);
const height = computed(() => props.height || 20);
const padding = computed(() => props.padding || 4);

// Format the value based on the provided format and precision
const formattedValue = computed(() => {
  if (props.value === null || props.value === undefined) {
    return 'N/A';
  }

  // Handle string values
  if (typeof props.value === 'string') {
    return props.value;
  }

  // Handle number values
  const value = Number(props.value);
  if (isNaN(value)) {
    return 'N/A';
  }

  // Apply formatting
  if (props.format) {
    switch (props.format.toLowerCase()) {
      case 'integer':
        return Math.round(value).toString();
      case 'decimal':
        return value.toFixed(props.precision || 2);
      case 'percentage':
        return `${(value * 100).toFixed(props.precision || 1)}%`;
      case 'scientific':
        return value.toExponential(props.precision || 2);
      case 'binary':
        return value ? '1' : '0';
      case 'hex':
        return `0x${Math.round(value).toString(16).toUpperCase()}`;
      case 'currency':
        return `$${value.toFixed(props.precision || 2)}`;
      default:
        return value.toFixed(props.precision || 2);
    }
  }

  // Default formatting
  if (Number.isInteger(value)) {
    return value.toString();
  } else {
    return value.toFixed(props.precision || 2);
  }
});
</script>
