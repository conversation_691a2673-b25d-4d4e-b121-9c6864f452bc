import { test, expect } from '@playwright/test';

/**
 * Level 1: Basic Error Handling Tests
 * These tests verify that the application handles basic errors gracefully.
 */

test.describe('Basic Error Handling', () => {
  test('should handle navigation to non-existent routes', async ({ page }) => {
    // Navigate to a non-existent route
    await page.goto('/this-route-does-not-exist-' + Date.now());
    
    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');
    
    // Take a screenshot
    await page.screenshot({ path: './test-results/non-existent-route.png' });
    
    // Log the current URL
    console.log(`Current URL after navigating to non-existent route: ${page.url()}`);
    
    // Check that the page still loads something (doesn't crash)
    await expect(page.locator('body')).toBeVisible();
    
    // Basic assertion - just check that the page is still loaded
    await expect(page).toBeTruthy();
  });
  
  test('should handle page refresh', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');
    
    // Store the initial URL
    const initialUrl = page.url();
    console.log(`Initial URL: ${initialUrl}`);
    
    // Refresh the page
    await page.reload();
    
    // Wait for the page to load again
    await page.waitForLoadState('domcontentloaded');
    
    // Log the URL after refresh
    console.log(`URL after refresh: ${page.url()}`);
    
    // Check that we're still on the same page
    expect(page.url()).toBe(initialUrl);
    
    // Take a screenshot
    await page.screenshot({ path: './test-results/after-refresh.png' });
    
    // Check that the page still loads properly
    await expect(page.locator('#app')).toBeVisible();
  });
  
  test('should handle browser back/forward navigation', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Store the initial URL
    const homeUrl = page.url();
    console.log(`Home URL: ${homeUrl}`);
    
    // Navigate to another page
    await page.goto('/about');
    
    // Store the second URL
    const aboutUrl = page.url();
    console.log(`About URL: ${aboutUrl}`);
    
    // Go back to the home page
    await page.goBack();
    
    // Check that we're back at the home page
    console.log(`URL after going back: ${page.url()}`);
    
    // Go forward to the about page
    await page.goForward();
    
    // Check that we're back at the about page
    console.log(`URL after going forward: ${page.url()}`);
    
    // Basic assertion - just check that the page is still loaded
    await expect(page).toBeTruthy();
  });
});
