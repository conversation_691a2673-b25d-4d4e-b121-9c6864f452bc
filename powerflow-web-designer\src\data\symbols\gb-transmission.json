[{"id": "overhead-line", "category": "transmission", "name": "架空线路", "svg": "<svg viewBox=\"0 0 120 100\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"0\" y1=\"50\" x2=\"30\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"90\" y1=\"50\" x2=\"120\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"30\" y1=\"50\" x2=\"90\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"2\" stroke-dasharray=\"5,5\"/><line x1=\"45\" y1=\"20\" x2=\"45\" y2=\"80\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"75\" y1=\"20\" x2=\"75\" y2=\"80\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"40\" y1=\"20\" x2=\"50\" y2=\"20\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"70\" y1=\"20\" x2=\"80\" y2=\"20\" stroke=\"currentColor\" stroke-width=\"2\"/><circle cx=\"45\" cy=\"35\" r=\"3\" fill=\"currentColor\"/><circle cx=\"75\" cy=\"35\" r=\"3\" fill=\"currentColor\"/></svg>", "dimensions": {"width": 120, "height": 100}, "connectionPoints": [{"id": "start", "position": {"x": 0, "y": 50}, "type": "input"}, {"id": "end", "position": {"x": 120, "y": 50}, "type": "output"}], "bindingSlots": [{"id": "current", "name": "电流", "dataType": "number", "defaultValue": 0, "description": "线路电流 (A)"}, {"id": "voltage", "name": "电压", "dataType": "number", "defaultValue": 0, "description": "线路电压 (kV)"}, {"id": "length", "name": "长度", "dataType": "number", "defaultValue": 0, "description": "线路长度 (km)"}, {"id": "temperature", "name": "导线温度", "dataType": "number", "defaultValue": 25, "description": "导线温度 (°C)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "underground-cable", "category": "transmission", "name": "地下电缆", "svg": "<svg viewBox=\"0 0 120 100\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"0\" y1=\"50\" x2=\"30\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"90\" y1=\"50\" x2=\"120\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><rect x=\"30\" y=\"45\" width=\"60\" height=\"10\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"30\" y1=\"40\" x2=\"90\" y2=\"40\" stroke=\"currentColor\" stroke-width=\"1\"/><line x1=\"30\" y1=\"60\" x2=\"90\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"1\"/><text x=\"60\" y=\"35\" font-size=\"8\" text-anchor=\"middle\" fill=\"currentColor\">电缆</text></svg>", "dimensions": {"width": 120, "height": 100}, "connectionPoints": [{"id": "start", "position": {"x": 0, "y": 50}, "type": "input"}, {"id": "end", "position": {"x": 120, "y": 50}, "type": "output"}], "bindingSlots": [{"id": "current", "name": "电流", "dataType": "number", "defaultValue": 0, "description": "电缆电流 (A)"}, {"id": "voltage", "name": "电压", "dataType": "number", "defaultValue": 0, "description": "电缆电压 (kV)"}, {"id": "length", "name": "长度", "dataType": "number", "defaultValue": 0, "description": "电缆长度 (km)"}, {"id": "sheath_current", "name": "护套电流", "dataType": "number", "defaultValue": 0, "description": "护套电流 (A)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "transmission-tower", "category": "transmission", "name": "输电塔", "svg": "<svg viewBox=\"0 0 100 120\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"50\" y1=\"10\" x2=\"50\" y2=\"110\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"30\" y1=\"30\" x2=\"70\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"35\" y1=\"50\" x2=\"65\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"40\" y1=\"70\" x2=\"60\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"25\" y1=\"110\" x2=\"75\" y2=\"110\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"30\" y1=\"30\" x2=\"40\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"70\" y1=\"30\" x2=\"60\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"2\"/><circle cx=\"30\" cy=\"25\" r=\"3\" fill=\"currentColor\"/><circle cx=\"50\" cy=\"25\" r=\"3\" fill=\"currentColor\"/><circle cx=\"70\" cy=\"25\" r=\"3\" fill=\"currentColor\"/><line x1=\"0\" y1=\"25\" x2=\"30\" y2=\"25\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"70\" y1=\"25\" x2=\"100\" y2=\"25\" stroke=\"currentColor\" stroke-width=\"2\"/></svg>", "dimensions": {"width": 100, "height": 120}, "connectionPoints": [{"id": "line_left", "position": {"x": 0, "y": 25}, "type": "bidirectional", "label": "左侧线路"}, {"id": "line_right", "position": {"x": 100, "y": 25}, "type": "bidirectional", "label": "右侧线路"}, {"id": "ground", "position": {"x": 50, "y": 120}, "type": "output", "label": "接地"}], "bindingSlots": [{"id": "wind_speed", "name": "风速", "dataType": "number", "defaultValue": 0, "description": "风速 (m/s)"}, {"id": "ice_thickness", "name": "覆冰厚度", "dataType": "number", "defaultValue": 0, "description": "覆冰厚度 (mm)"}, {"id": "tilt_angle", "name": "倾斜角", "dataType": "number", "defaultValue": 0, "description": "杆塔倾斜角 (度)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}]