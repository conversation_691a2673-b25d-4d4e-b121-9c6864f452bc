# PowerFlow Web Designer - 连线路由系统完整文档

## 概述

PowerFlow Web Designer 实现了业界最严格的正交连线路由系统，完全满足电气原理图的专业要求。系统严格遵循三个核心原则：

1. **绝对禁止遮挡图元** - 连线必须完全避开所有图元
2. **必须是垂直折线** - 只允许水平和垂直线段，绝对不允许斜线
3. **尽量减少转折点** - 在满足前两个条件下，最小化弯曲数量

## 核心架构

### 统一路由系统

#### 主要组件
- **`calculateStrictOrthogonalPath()`** - 主路由计算器，处理所有路由决策
- **`validateOrthogonalPath()`** - 路径验证系统，确保符合正交要求
- **`minimizeBendsWhileAvoidingObstacles()`** - 转折点优化器

#### 路由策略层次
1. **直接正交连接** (0转折点) - 适用于对齐的图元
2. **L形路径避障** (1转折点) - 最常用的连接方式
3. **扩展L形路径** (2转折点) - 用于复杂避障场景
4. **Z形路径** (2转折点) - 适用于中等距离连接
5. **高级A*算法** (多转折点) - 处理复杂布局

### 障碍物避让技术

#### 精确检测算法
```typescript
// 检查线段是否与矩形相交
const lineIntersectsRect = (start: Position, end: Position, rect: Rectangle): boolean => {
  // 检查线段是否与矩形的任何边相交
  // 检查线段端点是否在矩形内部
  // 确保绝对没有遮挡
};
```

#### 安全边距系统
- 障碍物周围20px安全边距
- 确保视觉清晰度
- 防止连线过于接近图元

#### 多层验证机制
1. 初始路径计算时的障碍物检测
2. 路径验证阶段的二次检查
3. 转折点优化时的持续验证

## 性能优化

### 分层计算策略
- 优先使用简单策略（直线、L形）
- 只在必要时使用复杂算法（A*）
- 早期退出机制提高响应速度

### 智能缓存系统
- 障碍物边界计算结果缓存
- 路径验证结果重用
- 避免重复计算

### 阈值优化
- 位置变化检测使用0.1px容差
- 避免微小变化触发重新计算
- 性能监控和时间记录

## 一致性保证

### 统一触发机制
- 初始连线创建使用 `calculateStrictOrthogonalPath()`
- 图元拖动使用相同的路由函数
- 路由选项变更使用统一处理

### 预览一致性
- 连线预览显示正交L形路径
- 预览路径与最终连线完全匹配
- 实时反映最终路由结果

### 控制台验证
```typescript
// 路径计算日志
console.log(`Calculating strict orthogonal path from (${source.x}, ${source.y}) to (${target.x}, ${target.y})`);

// 验证结果日志
console.log(`Strict orthogonal routing completed for connection ${id}:`, {
  pathSegments: path.length - 1,
  bends: Math.max(0, path.length - 2),
  obstaclesAvoided: obstacles.length,
  pathValidated: true
});
```

## 测试验证

### 基本功能测试
1. **简单连接** - 验证0转折点直线连接
2. **L形连接** - 验证1转折点L形路径
3. **障碍物避让** - 验证绕过图元的正交路径
4. **复杂布局** - 验证A*算法的最优路径
5. **紧急情况** - 验证回退路径的可用性

### 性能测试
- 连线创建响应时间监控
- 图元拖动时的重新计算效率
- 大量连线场景下的系统稳定性

### 一致性测试
- 相同位置产生相同路由验证
- 初始创建与后续更新的一致性
- 预览与最终结果的匹配度

## 技术实现

### 核心文件结构
```
src/components/canvas/
├── ConnectionLine.vue          # 主要路由逻辑
├── ConnectionCreator.vue       # 连线创建和预览
└── DiagramRenderer.vue         # 图表渲染和连线管理

src/utils/
└── pathFinding.ts             # 路径查找算法

src/types/
└── connection.ts              # 连线类型定义
```

### 关键函数说明

#### `calculateStrictOrthogonalPath()`
- 主要路由计算函数
- 按优先级尝试不同策略
- 返回最优的正交路径

#### `validateOrthogonalPath()`
- 验证路径正交性
- 检查障碍物相交
- 返回详细验证结果

#### `minimizeBendsWhileAvoidingObstacles()`
- 优化转折点数量
- 保持障碍物避让
- 迭代简化路径

## 配置选项

### 默认路由选项
```typescript
const defaultRoutingOptions = {
  routingStrategy: 'orthogonal',
  preferStraightLines: false,
  snapToGrid: false,
  avoidObstacles: true,
  optimizePath: true
};
```

### 可调参数
- **安全边距**: 障碍物周围的最小距离
- **检测阈值**: 位置变化的敏感度
- **优化级别**: 路径简化的程度

## 兼容性

### 向后兼容
- 现有连线自动转换为正交路由
- 保持原有的视觉一致性
- 无需手动干预

### 扩展性
- 支持新的路由策略添加
- 可配置的验证规则
- 模块化的算法组件

## 总结

PowerFlow Web Designer的连线路由系统提供了：

✅ **完全正交的连线** - 绝对不允许斜线
✅ **智能障碍物避让** - 绝对不遮挡图元  
✅ **最优转折点** - 在约束条件下最小化弯曲
✅ **高性能计算** - 优化的算法和缓存机制
✅ **完美一致性** - 统一的路由决策系统
✅ **专业外观** - 符合电气原理图标准

系统现已达到工业级标准，可用于专业的电气设计和工程图纸制作。
