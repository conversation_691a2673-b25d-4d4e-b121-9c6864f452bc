[{"id": "distance-relay", "category": "relay", "name": "距离继电器", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"20\" y=\"20\" width=\"60\" height=\"60\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"40\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">距离</text><text x=\"50\" y=\"55\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">继电器</text><circle cx=\"35\" cy=\"65\" r=\"5\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><circle cx=\"65\" cy=\"65\" r=\"5\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"0\" y1=\"30\" x2=\"20\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"30\" x2=\"100\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"70\" x2=\"20\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"70\" x2=\"100\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"80\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "voltage_in", "position": {"x": 0, "y": 30}, "type": "input", "label": "电压输入"}, {"id": "voltage_out", "position": {"x": 100, "y": 30}, "type": "output", "label": "电压输出"}, {"id": "current_in", "position": {"x": 0, "y": 70}, "type": "input", "label": "电流输入"}, {"id": "current_out", "position": {"x": 100, "y": 70}, "type": "output", "label": "电流输出"}, {"id": "trip_out", "position": {"x": 50, "y": 100}, "type": "output", "label": "跳闸输出"}], "bindingSlots": [{"id": "impedance", "name": "阻抗", "dataType": "number", "defaultValue": 0, "description": "测量阻抗 (Ω)"}, {"id": "zone1_setting", "name": "Ⅰ段定值", "dataType": "number", "defaultValue": 0, "description": "距离Ⅰ段定值 (Ω)"}, {"id": "zone2_setting", "name": "Ⅱ段定值", "dataType": "number", "defaultValue": 0, "description": "距离Ⅱ段定值 (Ω)"}, {"id": "status", "name": "状态", "dataType": "enum", "defaultValue": "normal", "enumValues": ["normal", "zone1", "zone2", "zone3", "trip"], "description": "继电器状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "frequency-relay", "category": "relay", "name": "频率继电器", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"20\" y=\"20\" width=\"60\" height=\"60\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"40\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">频率</text><text x=\"50\" y=\"55\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">继电器</text><path d=\"M 30 65 Q 40 60 50 65 Q 60 70 70 65\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"0\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"80\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "voltage_in", "position": {"x": 50, "y": 0}, "type": "input", "label": "电压输入"}, {"id": "trip_out", "position": {"x": 50, "y": 100}, "type": "output", "label": "跳闸输出"}], "bindingSlots": [{"id": "frequency", "name": "频率", "dataType": "number", "defaultValue": 50, "description": "测量频率 (Hz)"}, {"id": "low_freq_setting", "name": "低频定值", "dataType": "number", "defaultValue": 49.5, "description": "低频保护定值 (Hz)"}, {"id": "high_freq_setting", "name": "高频定值", "dataType": "number", "defaultValue": 50.5, "description": "高频保护定值 (Hz)"}, {"id": "status", "name": "状态", "dataType": "enum", "defaultValue": "normal", "enumValues": ["normal", "low_freq", "high_freq", "trip"], "description": "继电器状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "voltage-relay", "category": "relay", "name": "电压继电器", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"20\" y=\"20\" width=\"60\" height=\"60\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"40\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">电压</text><text x=\"50\" y=\"55\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">继电器</text><circle cx=\"35\" cy=\"65\" r=\"5\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><circle cx=\"65\" cy=\"65\" r=\"5\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"0\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"80\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"50\" x2=\"20\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "voltage_in", "position": {"x": 50, "y": 0}, "type": "input", "label": "电压输入"}, {"id": "neutral", "position": {"x": 50, "y": 100}, "type": "input", "label": "中性点"}, {"id": "contact_no", "position": {"x": 0, "y": 50}, "type": "output", "label": "常开触点"}, {"id": "contact_nc", "position": {"x": 100, "y": 50}, "type": "output", "label": "常闭触点"}], "bindingSlots": [{"id": "voltage", "name": "电压", "dataType": "number", "defaultValue": 0, "description": "测量电压 (V)"}, {"id": "pickup_voltage", "name": "动作电压", "dataType": "number", "defaultValue": 100, "description": "继电器动作电压 (V)"}, {"id": "dropout_voltage", "name": "返回电压", "dataType": "number", "defaultValue": 95, "description": "继电器返回电压 (V)"}, {"id": "status", "name": "状态", "dataType": "enum", "defaultValue": "normal", "enumValues": ["normal", "pickup", "trip"], "description": "继电器状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}]