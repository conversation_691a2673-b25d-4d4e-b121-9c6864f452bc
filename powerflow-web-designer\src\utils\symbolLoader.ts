/**
 * Symbol loader utility for loading symbols from JSON files
 * Supports both hardcoded symbols and JSON-based symbols for GB standard compliance
 */

import type { SymbolDefinition } from '../types/symbol';
import { symbolLibrary as hardcodedSymbols } from './symbolLibrary';

// Import JSON symbol files directly
import gbDatacenter from '../data/symbols/gb-datacenter.json';
import gbTransformers from '../data/symbols/gb-transformers.json';
import gbSwitchgear from '../data/symbols/gb-switchgear.json';
import gbMotors from '../data/symbols/gb-motors.json';
import gbMeasurement from '../data/symbols/gb-measurement.json';
import gbProtection from '../data/symbols/gb-protection.json';
import gbTransmission from '../data/symbols/gb-transmission.json';
import gbCapacitors from '../data/symbols/gb-capacitors.json';
import gbReactors from '../data/symbols/gb-reactors.json';
import gbRelays from '../data/symbols/gb-relays.json';

// Cache for loaded JSON symbols
let jsonSymbolsCache: SymbolDefinition[] | null = null;

/**
 * Load symbols from JSON files in the data directory
 */
async function loadJsonSymbols(): Promise<SymbolDefinition[]> {
  if (jsonSymbolsCache !== null) {
    return jsonSymbolsCache;
  }

  const symbols: SymbolDefinition[] = [];

  try {
    // Combine all JSON symbol data
    const symbolDataSets = [
      gbDatacenter,
      gbTransformers,
      gbSwitchgear,
      gbMotors,
      gbMeasurement,
      gbProtection,
      gbTransmission,
      gbCapacitors,
      gbReactors,
      gbRelays
    ];

    // Process each symbol data set
    for (const symbolData of symbolDataSets) {
      if (Array.isArray(symbolData)) {
        // Validate each symbol before adding
        for (const symbol of symbolData) {
          if (validateSymbolDefinition(symbol)) {
            symbols.push(symbol as SymbolDefinition);
          } else {
            console.warn('Invalid symbol definition:', symbol);
          }
        }
      }
    }

    jsonSymbolsCache = symbols;
    console.log(`Loaded ${symbols.length} symbols from JSON files`);
  } catch (error) {
    console.error('Error loading JSON symbols:', error);
    jsonSymbolsCache = [];
  }

  return jsonSymbolsCache;
}

/**
 * Get all available symbols (hardcoded + JSON)
 */
export async function getAllSymbols(): Promise<SymbolDefinition[]> {
  const jsonSymbols = await loadJsonSymbols();
  return [...hardcodedSymbols, ...jsonSymbols];
}

/**
 * Get a symbol definition by ID
 */
export async function getSymbolDefinition(id: string): Promise<SymbolDefinition | undefined> {
  const allSymbols = await getAllSymbols();
  return allSymbols.find(symbol => symbol.id === id);
}

/**
 * Get symbol definitions by category
 */
export async function getSymbolDefinitionsByCategory(category: string): Promise<SymbolDefinition[]> {
  const allSymbols = await getAllSymbols();
  return allSymbols.filter(symbol => symbol.category === category);
}

/**
 * Clear the symbol cache (useful for development/testing)
 */
export function clearSymbolCache(): void {
  jsonSymbolsCache = null;
}

/**
 * Validate a symbol definition
 */
export function validateSymbolDefinition(symbol: any): symbol is SymbolDefinition {
  return (
    typeof symbol === 'object' &&
    typeof symbol.id === 'string' &&
    typeof symbol.category === 'string' &&
    typeof symbol.name === 'string' &&
    typeof symbol.svg === 'string' &&
    typeof symbol.dimensions === 'object' &&
    typeof symbol.dimensions.width === 'number' &&
    typeof symbol.dimensions.height === 'number' &&
    Array.isArray(symbol.connectionPoints) &&
    Array.isArray(symbol.bindingSlots) &&
    typeof symbol.properties === 'object'
  );
}
