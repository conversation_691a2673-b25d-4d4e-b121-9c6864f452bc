<template>
  <div class="diagram-manager">
    <a-modal
      :visible="visible"
      :title="'画面管理'"
      width="800px"
      :footer="null"
      @cancel="handleCancel"
      @update:visible="(val) => emit('update:visible', val)"
    >
      <template #default>
        <div class="diagram-manager-content">
          <div class="diagram-list-container">
            <div class="diagram-list-header">
              <a-input-search
                v-model:value="searchText"
                placeholder="搜索画面"
                style="width: 200px"
                @change="handleSearch"
              />
              <div class="diagram-list-actions">
                <a-button type="primary" @click="handleNewDiagram">
                  <template #icon><plus-outlined /></template>
                  新建
                </a-button>
                <a-button @click="handleImport">
                  <template #icon><import-outlined /></template>
                  导入
                </a-button>
                <a-button @click="handleExportCurrent">
                  <template #icon><export-outlined /></template>
                  导出当前画面
                </a-button>
                <a-button @click="handleLoadDemo">
                  <template #icon><experiment-outlined /></template>
                  加载演示
                </a-button>
              </div>
            </div>

            <a-table
              :dataSource="filteredDiagrams"
              :columns="columns"
              :pagination="{ pageSize: 10 }"
              :rowKey="record => record.id"
              :rowClassName="(record) => record.id === currentDiagramId ? 'current-diagram-row' : ''"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'name'">
                  <div class="diagram-name">{{ record.name }}</div>
                </template>
                <template v-if="column.key === 'modified'">
                  {{ formatDate(record.modified) }}
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" @click="handleEdit(record)">
                      <template #icon><edit-outlined /></template>
                    </a-button>
                    <a-button type="link" @click="handleExport(record)">
                      <template #icon><export-outlined /></template>
                    </a-button>
                    <a-popconfirm
                      title="确定要删除这个画面吗？"
                      @confirm="() => handleDelete(record)"
                      okText="确定"
                      cancelText="取消"
                    >
                      <template #default>
                        <a-button type="link" danger>
                          <template #icon><delete-outlined /></template>
                        </a-button>
                      </template>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </template>
      <template #closeIcon>
        <close-outlined />
      </template>
    </a-modal>

    <!-- 新建画面对话框 -->
    <a-modal
      :visible="newDiagramModalVisible"
      title="新建画面"
      @ok="createNewDiagram"
      @cancel="closeNewDiagramModal"
      @update:visible="(val) => newDiagramModalVisible = val"
      okText="创建"
      cancelText="取消"
    >
      <template #default>
        <a-form :model="newDiagramForm" layout="vertical">
          <a-form-item label="画面名称" name="name" :rules="[{ required: true, message: '请输入画面名称' }]">
            <a-input v-model:value="newDiagramForm.name" />
          </a-form-item>
          <a-form-item label="描述" name="description">
            <a-textarea v-model:value="newDiagramForm.description" :rows="4" />
          </a-form-item>
        </a-form>
      </template>
      <template #closeIcon>
        <close-outlined />
      </template>
    </a-modal>

    <!-- 导入画面对话框 -->
    <a-modal
      :visible="importModalVisible"
      title="导入画面"
      @ok="importDiagram"
      @cancel="closeImportModal"
      @update:visible="(val) => importModalVisible = val"
      okText="导入"
      cancelText="取消"
    >
      <template #default>
        <div class="import-container">
          <a-upload
            :fileList="fileList"
            :beforeUpload="beforeUpload"
            :maxCount="1"
            @remove="handleRemove"
          >
            <a-button>
              <template #icon><upload-outlined /></template>
              选择文件
            </a-button>
          </a-upload>
          <div class="import-note">
            <p>注意：只支持导入 JSON 格式的画面文件</p>
          </div>
        </div>
      </template>
      <template #closeIcon>
        <close-outlined />
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  EditOutlined,
  DeleteOutlined,
  ExperimentOutlined,
  UploadOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';
import type { Diagram } from '@/types/diagram';
import { createDiagram } from '@/types/diagram';
import { v4 as uuidv4 } from 'uuid';
import demoData from '@/data/demo-diagram.json';

// Props
const props = defineProps<{
  visible: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'edit', diagramId: string): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const searchText = ref('');
const newDiagramModalVisible = ref(false);
const importModalVisible = ref(false);
const newDiagramForm = ref({
  name: '',
  description: '',
});
const fileList = ref<any[]>([]);
const importedData = ref<any>(null);

// 表格列定义
const columns = [
  {
    title: '画面名称',
    dataIndex: 'name',
    key: 'name',
    sorter: (a: any, b: any) => a.name.localeCompare(b.name),
  },
  {
    title: '最后编辑时间',
    dataIndex: 'modified',
    key: 'modified',
    sorter: (a: any, b: any) => new Date(a.modified).getTime() - new Date(b.modified).getTime(),
    defaultSortOrder: 'descend',
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
];

// 从 localStorage 加载画面列表
const loadDiagramsFromLocalStorage = () => {
  if (!diagramStore.loadDiagramsFromLocalStorage()) {
    message.error('加载画面列表失败');
  }
};

// 保存画面列表到 localStorage
const saveDiagramsToLocalStorage = () => {
  if (!diagramStore.saveDiagramsToLocalStorage()) {
    message.error('保存画面列表失败');
  }
};

// 计算属性：当前画面 ID
const currentDiagramId = computed(() => {
  return diagramStore.currentDiagram?.id || '';
});

// 计算属性：过滤后的画面列表
const filteredDiagrams = computed(() => {
  const diagrams = Object.values(diagramStore.diagrams);
  if (!searchText.value) {
    return diagrams;
  }

  const searchLower = searchText.value.toLowerCase();
  return diagrams.filter(diagram =>
    diagram.name.toLowerCase().includes(searchLower) ||
    (diagram.description && diagram.description.toLowerCase().includes(searchLower))
  );
});

// 格式化日期
const formatDate = (date: Date | string) => {
  if (!date) return '';
  const d = typeof date === 'string' ? new Date(date) : date;
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;
};

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
};

// 处理新建画面
const handleNewDiagram = () => {
  newDiagramForm.value = {
    name: '',
    description: '',
  };
  newDiagramModalVisible.value = true;
};

// 创建新画面
const createNewDiagram = () => {
  if (!newDiagramForm.value.name) {
    message.error('请输入画面名称');
    return;
  }

  const id = uuidv4();
  const diagram = createDiagram(
    id,
    newDiagramForm.value.name,
    newDiagramForm.value.description
  );

  diagramStore.diagrams[id] = diagram;
  diagramStore.saveDiagramsToLocalStorage();

  message.success('画面创建成功');
  newDiagramModalVisible.value = false;

  // 先关闭对话框，再加载画面，避免 Transition 组件的警告
  emit('update:visible', false);

  // 使用 nextTick 确保对话框已关闭
  nextTick(() => {
    // 加载新创建的画面
    diagramStore.loadDiagram(id);
    emit('edit', id);
  });
};

// 处理编辑画面
const handleEdit = (record: Diagram) => {
  // 先关闭对话框，再加载画面，避免 Transition 组件的警告
  emit('update:visible', false);

  // 使用 nextTick 确保对话框已关闭
  nextTick(() => {
    diagramStore.loadDiagram(record.id);
    emit('edit', record.id);
  });
};

// 处理导出画面
const handleExport = (record: Diagram) => {
  try {
    // 创建 JSON 字符串
    const diagramJson = JSON.stringify(record, null, 2);

    // 创建 Blob 和下载链接
    const blob = new Blob([diagramJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    // 创建下载链接并点击
    const a = document.createElement('a');
    a.href = url;
    a.download = `${record.name}.json`;
    document.body.appendChild(a);
    a.click();

    // 清理
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    message.success('画面导出成功');
  } catch (error) {
    console.error('Failed to export diagram:', error);
    message.error('画面导出失败');
  }
};

// 导出当前画面
const handleExportCurrent = () => {
  if (!diagramStore.currentDiagram) {
    message.error('没有当前画面可导出');
    return;
  }

  // 使用当前画面的ID查找完整的画面对象
  const currentDiagramId = diagramStore.currentDiagram.id;
  const currentDiagram = diagramStore.diagrams[currentDiagramId];

  if (currentDiagram) {
    handleExport(currentDiagram);
  } else {
    message.error('无法找到当前画面');
  }
};

// 处理删除画面
const handleDelete = (record: Diagram) => {
  // 如果删除的是当前画面，则创建一个新画面
  if (record.id === currentDiagramId.value) {
    diagramStore.createDiagram('新画面');
  }

  // 从 diagrams 中删除
  delete diagramStore.diagrams[record.id];
  diagramStore.saveDiagramsToLocalStorage();

  message.success('画面删除成功');
};

// 处理导入
const handleImport = () => {
  fileList.value = [];
  importedData.value = null;
  importModalVisible.value = true;
};

// 上传前处理
const beforeUpload = (file: File) => {
  const isJSON = file.type === 'application/json' || file.name.endsWith('.json');
  if (!isJSON) {
    message.error('只能上传 JSON 文件!');
    return false;
  }

  // 读取文件内容
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const content = e.target?.result as string;
      importedData.value = JSON.parse(content);
    } catch (error) {
      console.error('Failed to parse JSON file:', error);
      message.error('解析 JSON 文件失败');
    }
  };
  reader.readAsText(file);

  // 更新文件列表
  fileList.value = [file];

  // 阻止自动上传
  return false;
};

// 移除文件
const handleRemove = () => {
  fileList.value = [];
  importedData.value = null;
};

// 导入画面
const importDiagram = () => {
  if (!importedData.value) {
    message.error('请选择要导入的 JSON 文件');
    return;
  }

  try {
    // 验证导入的数据
    if (!importedData.value.id || !importedData.value.name) {
      message.error('无效的画面数据');
      return;
    }

    // 深拷贝导入的数据
    const importedDataCopy = JSON.parse(JSON.stringify(importedData.value));

    // 转换连接格式（从旧格式到新格式）
    if (importedDataCopy.connections) {
      Object.values(importedDataCopy.connections).forEach((connection: any) => {
        // 检查是否使用旧格式
        if (connection.sourceId && connection.sourcePointId &&
            connection.targetId && connection.targetPointId) {
          // 转换为新格式
          connection.source = {
            symbolInstanceId: connection.sourceId,
            connectionPointId: connection.sourcePointId
          };
          connection.target = {
            symbolInstanceId: connection.targetId,
            connectionPointId: connection.targetPointId
          };

          // 删除旧属性
          delete connection.sourceId;
          delete connection.sourcePointId;
          delete connection.targetId;
          delete connection.targetPointId;

          // 确保 style 属性存在并格式正确
          if (!connection.style || typeof connection.style !== 'object') {
            connection.style = {};
          }

          // 将旧的 stroke 和 strokeWidth 转换为新格式
          if (connection.style.stroke) {
            connection.style.strokeColor = connection.style.stroke;
            delete connection.style.stroke;
          }

          if (connection.style.strokeWidth) {
            connection.style.lineWidth = connection.style.strokeWidth;
            delete connection.style.strokeWidth;
          }
        }
      });
    }

    // 生成新的 ID，避免覆盖现有画面
    const id = uuidv4();
    const diagram = {
      ...importedDataCopy,
      id,
      created: new Date(),
      modified: new Date(),
    };

    // 添加到 diagrams 中
    diagramStore.diagrams[id] = diagram;
    diagramStore.saveDiagramsToLocalStorage();

    message.success('画面导入成功');
    importModalVisible.value = false;

    // 先关闭对话框，再加载画面，避免 Transition 组件的警告
    emit('update:visible', false);

    // 使用 nextTick 确保对话框已关闭
    nextTick(() => {
      // 加载导入的画面
      diagramStore.loadDiagram(id);
      emit('edit', id);
    });
  } catch (error) {
    console.error('Failed to import diagram:', error);
    message.error('画面导入失败');
  }
};

// 处理加载演示
const handleLoadDemo = () => {
  // 检查是否已存在名为 "Demo" 的画面
  const existingDemo = Object.values(diagramStore.diagrams).find(d => d.name === 'Demo');

  if (existingDemo) {
    // 如果存在，询问是否覆盖
    if (confirm('已存在名为 "Demo" 的画面，是否覆盖？')) {
      loadDemoData();
    }
  } else {
    loadDemoData();
  }
};

// 加载演示数据
const loadDemoData = () => {
  try {
    // 生成新的 ID
    const id = uuidv4();

    // 深拷贝演示数据
    const demoDataCopy = JSON.parse(JSON.stringify(demoData));

    // 转换连接格式（从旧格式到新格式）
    if (demoDataCopy.connections) {
      Object.values(demoDataCopy.connections).forEach((connection: any) => {
        // 检查是否使用旧格式
        if (connection.sourceId && connection.sourcePointId &&
            connection.targetId && connection.targetPointId) {
          // 转换为新格式
          connection.source = {
            symbolInstanceId: connection.sourceId,
            connectionPointId: connection.sourcePointId
          };
          connection.target = {
            symbolInstanceId: connection.targetId,
            connectionPointId: connection.targetPointId
          };

          // 删除旧属性
          delete connection.sourceId;
          delete connection.sourcePointId;
          delete connection.targetId;
          delete connection.targetPointId;

          // 确保 style 属性存在并格式正确
          if (!connection.style || typeof connection.style !== 'object') {
            connection.style = {};
          }

          // 将旧的 stroke 和 strokeWidth 转换为新格式
          if (connection.style.stroke) {
            connection.style.strokeColor = connection.style.stroke;
            delete connection.style.stroke;
          }

          if (connection.style.strokeWidth) {
            connection.style.lineWidth = connection.style.strokeWidth;
            delete connection.style.strokeWidth;
          }
        }
      });
    }

    // 创建演示画面
    const demo = {
      ...demoDataCopy,
      id,
      name: 'Demo',
      created: new Date(),
      modified: new Date(),
    };

    // 添加到 diagrams 中
    diagramStore.diagrams[id] = demo;
    diagramStore.saveDiagramsToLocalStorage();

    message.success('演示画面加载成功');

    // 先关闭对话框，再加载画面，避免 Transition 组件的警告
    emit('update:visible', false);

    // 使用 nextTick 确保对话框已关闭
    nextTick(() => {
      // 加载演示画面
      diagramStore.loadDiagram(id);
      emit('edit', id);
    });
  } catch (error) {
    console.error('Failed to load demo:', error);
    message.error('加载演示画面失败');
  }
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 关闭新建画面对话框
const closeNewDiagramModal = () => {
  newDiagramModalVisible.value = false;
};

// 关闭导入对话框
const closeImportModal = () => {
  importModalVisible.value = false;
};

// 生命周期钩子
onMounted(() => {
  loadDiagramsFromLocalStorage();
});
</script>

<style scoped>
.diagram-manager-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.diagram-list-container {
  flex: 1;
  overflow: auto;
}

.diagram-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.diagram-list-actions {
  display: flex;
  gap: 8px;
}

.diagram-name {
  font-weight: 500;
}

.current-diagram-row {
  background-color: #e6f7ff;
}

.import-container {
  padding: 16px;
}

.import-note {
  margin-top: 16px;
  color: #999;
}
</style>
