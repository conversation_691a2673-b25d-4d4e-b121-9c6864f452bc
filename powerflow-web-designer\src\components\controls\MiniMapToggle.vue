<template>
  <div class="mini-map-toggle">
    <a-tooltip :title="$locale.t(isVisible ? 'miniMap.hide' : 'miniMap.show')">
      <a-button 
        type="text" 
        shape="circle" 
        @click="toggleMiniMap"
      >
        <template #icon>
          <eye-outlined v-if="isVisible" />
          <eye-invisible-outlined v-else />
        </template>
      </a-button>
    </a-tooltip>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue';

// State
const isVisible = ref(false);

// Emits
const emit = defineEmits<{
  (e: 'toggle', visible: boolean): void;
}>();

// Methods
const toggleMiniMap = () => {
  isVisible.value = !isVisible.value;
  emit('toggle', isVisible.value);
};
</script>

<style scoped>
.mini-map-toggle {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 99;
}
</style>
