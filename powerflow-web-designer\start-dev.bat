@echo off
chcp 65001 >nul
title PowerFlow Web Designer - 开发环境

echo.
echo ========================================
echo   PowerFlow Web Designer 开发环境
echo ========================================
echo.

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 检查npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到npm
    pause
    exit /b 1
)

:: 显示版本信息
echo ✅ 环境检查通过
for /f "tokens=*" %%i in ('node --version') do echo Node.js版本: %%i
for /f "tokens=*" %%i in ('npm --version') do echo npm版本: %%i
echo.

:: 检查依赖
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
    echo.
)

:: 启动开发服务器
echo 🚀 启动开发服务器...
echo 📝 提示: 
echo    - 服务器启动后会自动打开浏览器
echo    - 使用 Ctrl+C 停止服务器
echo    - 修改代码后页面会自动刷新
echo.

npm run dev

echo.
echo 👋 开发服务器已停止
pause
