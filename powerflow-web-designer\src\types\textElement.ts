/**
 * Text element types for PowerFlow Web Designer
 */

import type { Position, Size } from './symbol';

// Text alignment options
export enum TextAlignment {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
  JUSTIFY = 'justify'
}

// Text style interface
export interface TextStyle {
  fontFamily: string;
  fontSize: number;
  fontWeight: string;
  fontStyle: string;
  textColor: string;
  backgroundColor?: string;
  backgroundOpacity?: number;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  padding?: number;
  lineHeight?: number;
  letterSpacing?: number;
}

// Text element interface
export interface TextElement {
  id: string;
  content: string;
  position: Position;
  size?: Size; // Optional size for text box constraints
  rotation: number; // Rotation in degrees
  alignment: TextAlignment;
  style: TextStyle;
  multiline: boolean;
  editable: boolean;
  selected?: boolean; // UI state
  layerId?: string; // Layer assignment
  locked?: boolean; // Whether the text element is locked
  visible?: boolean; // Whether the text element is visible
}

// Default text style
export const defaultTextStyle: TextStyle = {
  fontFamily: 'Arial, sans-serif',
  fontSize: 14,
  fontWeight: 'normal',
  fontStyle: 'normal',
  textColor: '#000000',
  backgroundColor: 'transparent',
  backgroundOpacity: 1,
  borderColor: 'transparent',
  borderWidth: 0,
  borderRadius: 0,
  padding: 4,
  lineHeight: 1.2,
  letterSpacing: 0,
};

// Factory function to create a new text element
export function createTextElement(
  id: string,
  content: string,
  position: Position,
  alignment: TextAlignment = TextAlignment.LEFT,
  style: Partial<TextStyle> = {},
  rotation: number = 0,
  multiline: boolean = false,
  size?: Size
): TextElement {
  return {
    id,
    content,
    position,
    size,
    rotation,
    alignment,
    style: { ...defaultTextStyle, ...style },
    multiline,
    editable: true,
    visible: true,
    locked: false,
  };
}

// Text element operations
export function updateTextElement(
  textElement: TextElement,
  updates: Partial<TextElement>
): TextElement {
  return {
    ...textElement,
    ...updates,
    style: updates.style ? { ...textElement.style, ...updates.style } : textElement.style,
  };
}

// Text element bounds calculation
export function getTextElementBounds(textElement: TextElement): {
  x: number;
  y: number;
  width: number;
  height: number;
} {
  // Estimate text dimensions based on font size and content
  const { fontSize, padding = 0 } = textElement.style;
  const lines = textElement.multiline ? textElement.content.split('\n') : [textElement.content];
  const maxLineLength = Math.max(...lines.map(line => line.length));
  
  // Rough estimation - in a real implementation, you'd measure actual text
  const charWidth = fontSize * 0.6; // Approximate character width
  const lineHeight = fontSize * (textElement.style.lineHeight || 1.2);
  
  const width = textElement.size?.width || (maxLineLength * charWidth + padding * 2);
  const height = textElement.size?.height || (lines.length * lineHeight + padding * 2);
  
  return {
    x: textElement.position.x,
    y: textElement.position.y,
    width,
    height,
  };
}
