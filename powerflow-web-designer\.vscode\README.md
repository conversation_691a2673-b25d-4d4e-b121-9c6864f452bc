# VSCode 配置说明

本目录包含了为 PowerFlow Web Designer 项目优化的 VSCode 配置文件，提供了完整的开发、调试和测试环境。

## 📁 配置文件说明

### `launch.json` - 运行和调试配置
包含以下预配置的启动选项：

#### 🚀 开发相关
- **启动开发服务器** - 启动 Vite 开发服务器，支持热重载
- **构建项目** - 构建生产环境版本
- **预览构建结果** - 预览构建后的生产版本

#### 🧪 测试相关
- **运行单元测试** - 执行 Vitest 单元测试
- **监视模式测试** - 文件变化时自动重新运行测试
- **E2E测试** - 运行 Playwright 端到端测试
- **E2E测试UI** - 以UI模式运行 Playwright 测试
- **E2E调试模式** - 以调试模式运行 Playwright 测试
- **测试覆盖率** - 生成测试覆盖率报告

#### 🔧 工具相关
- **代码检查** - 运行 ESLint 检查并自动修复
- **代码格式化** - 使用 Prettier 格式化代码

#### 🔄 组合配置
- **完整开发环境** - 同时启动开发服务器和测试监视

### `tasks.json` - 任务配置
提供了所有常用的开发任务，可通过 `Ctrl+Shift+P` → `Tasks: Run Task` 执行。

### `settings.json` - 工作区设置
包含以下优化配置：
- 代码格式化和保存时自动修复
- TypeScript 和 Vue 开发优化
- 文件关联和排除规则
- 终端和调试设置
- 性能优化配置

### `extensions.json` - 推荐扩展
自动推荐安装以下扩展：
- **Vue.volar** - Vue 3 语言支持
- **esbenp.prettier-vscode** - 代码格式化
- **dbaeumer.vscode-eslint** - 代码检查
- **ms-playwright.playwright** - Playwright 测试支持
- 以及其他实用开发工具

## 🚀 快速开始

### 方法一：使用 VSCode 运行配置
1. 打开项目文件夹
2. 按 `F5` 或点击侧边栏的"运行和调试"
3. 选择"🚀 启动开发服务器 (Dev Server)"
4. 点击绿色播放按钮

### 方法二：使用任务
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `Tasks: Run Task`
3. 选择"🚀 启动开发服务器"

### 方法三：使用快捷脚本
- **Windows**: 双击 `start-dev.bat`
- **PowerShell**: 运行 `.\scripts\dev-start.ps1`

## 🔧 常用操作

### 启动开发环境
```bash
# 基本启动
npm run dev

# 或使用 VSCode 任务
Ctrl+Shift+P → Tasks: Run Task → 🚀 启动开发服务器
```

### 运行测试
```bash
# 单元测试
npm run test

# 监视模式
npm run test:watch

# E2E测试
npm run test:e2e
```

### 代码质量检查
```bash
# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 🎯 调试指南

### 前端调试
1. 在 VSCode 中设置断点
2. 启动"🚀 启动开发服务器"配置
3. 在浏览器中打开应用
4. 断点会在 VSCode 中触发

### 测试调试
1. 在测试文件中设置断点
2. 使用"🧪 运行单元测试"配置
3. 或使用"🐛 E2E调试模式"进行 E2E 测试调试

## 📝 自定义配置

### 修改端口
在 `vite.config.ts` 中修改：
```typescript
export default defineConfig({
  server: {
    port: 3000  // 修改为你想要的端口
  }
})
```

### 添加新的启动配置
在 `launch.json` 中添加新的配置项：
```json
{
  "name": "你的配置名称",
  "type": "node",
  "request": "launch",
  "cwd": "${workspaceFolder}",
  "runtimeExecutable": "npm",
  "runtimeArgs": ["run", "your-script"]
}
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   - 检查是否有其他服务占用端口
   - Vite 会自动选择可用端口

2. **依赖安装失败**
   - 删除 `node_modules` 和 `package-lock.json`
   - 重新运行 `npm install`

3. **TypeScript 错误**
   - 确保安装了推荐的扩展
   - 重启 TypeScript 服务：`Ctrl+Shift+P` → `TypeScript: Restart TS Server`

4. **ESLint 不工作**
   - 确保安装了 ESLint 扩展
   - 检查 `.eslintrc` 配置文件

### 重置环境
如果遇到问题，可以使用以下命令重置环境：
```bash
# 清理并重新安装依赖
npm ci

# 或使用 PowerShell 脚本
.\scripts\dev-start.ps1 -Clean
```

## 📚 更多资源

- [Vite 文档](https://vitejs.dev/)
- [Vue 3 文档](https://vuejs.org/)
- [TypeScript 文档](https://www.typescriptlang.org/)
- [Vitest 文档](https://vitest.dev/)
- [Playwright 文档](https://playwright.dev/)

## 🔄 开发工作流

### 日常开发流程
1. **启动开发环境**
   ```bash
   # 方式1: 使用批处理文件
   双击 start-dev.bat

   # 方式2: 使用VSCode
   F5 → 选择"🚀 启动开发服务器"

   # 方式3: 使用PowerShell脚本
   .\scripts\dev-start.ps1
   ```

2. **编写代码**
   - 代码会自动格式化（保存时）
   - ESLint 会实时检查代码质量
   - TypeScript 提供类型检查

3. **运行测试**
   ```bash
   # 监视模式（推荐）
   npm run test:watch

   # 或使用VSCode配置
   F5 → 选择"🔍 监视模式测试"
   ```

4. **提交代码前**
   ```bash
   # 代码检查和格式化
   npm run lint
   npm run format

   # 运行完整测试套件
   npm run test
   npm run test:e2e
   ```

### 多人协作工作流
1. **环境同步**
   - 所有开发者使用相同的VSCode配置
   - 推荐扩展会自动提示安装
   - 代码风格自动统一

2. **代码审查**
   - 使用GitLens扩展查看代码历史
   - 利用ESLint规则保证代码质量
   - 测试覆盖率报告确保测试完整性

## ⚙️ 高级配置

### 自定义调试配置
在 `launch.json` 中添加自定义配置：

```json
{
  "name": "🔧 自定义调试",
  "type": "node",
  "request": "launch",
  "cwd": "${workspaceFolder}",
  "runtimeExecutable": "npm",
  "runtimeArgs": ["run", "dev"],
  "env": {
    "NODE_ENV": "development",
    "DEBUG": "powerflow:*",
    "VITE_API_URL": "http://localhost:3001"
  },
  "console": "integratedTerminal"
}
```

### 环境变量配置
创建 `.env.local` 文件：
```bash
# 开发环境配置
VITE_APP_TITLE=PowerFlow Web Designer (Dev)
VITE_API_URL=http://localhost:3001
VITE_DEBUG_MODE=true
```

### 性能优化设置
在 `settings.json` 中添加：
```json
{
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "typescript.suggest.autoImports": false,
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/.git/**": true
  }
}
```

## 🧪 测试策略

### 单元测试
- 使用 Vitest 进行快速单元测试
- 测试文件位于 `src/tests/` 目录
- 支持 Vue 组件测试

### 集成测试
- 使用 Playwright 进行 E2E 测试
- 测试文件位于 `tests/e2e/` 目录
- 支持多浏览器测试

### 测试覆盖率
```bash
# 生成覆盖率报告
npm run test:coverage

# 查看详细报告
open coverage/index.html
```

## 🚨 常见错误解决

### 1. Vite 启动失败
```bash
# 清理缓存
rm -rf node_modules/.vite
npm run dev
```

### 2. TypeScript 类型错误
```bash
# 重新生成类型定义
npm run build
# 或重启TS服务
Ctrl+Shift+P → TypeScript: Restart TS Server
```

### 3. ESLint 配置冲突
```bash
# 检查配置文件
npx eslint --print-config src/main.ts
```

### 4. 端口冲突
```bash
# 查看端口占用
netstat -ano | findstr :5173
# 或让Vite自动选择端口
npm run dev -- --port 0
```

## 📊 性能监控

### 开发时性能
- 使用 Vue DevTools 监控组件性能
- Vite 提供快速热重载
- TypeScript 增量编译

### 构建性能
```bash
# 分析构建包大小
npm run build -- --analyze

# 预览构建结果
npm run preview
```

## 🔐 安全配置

### 环境变量安全
- 敏感信息使用 `.env.local`（不提交到Git）
- 公开配置使用 `.env`
- 生产环境使用环境变量注入

### 依赖安全
```bash
# 检查安全漏洞
npm audit

# 自动修复
npm audit fix
```

## 🤝 贡献指南

### 提交代码前检查清单
- [ ] 代码通过 ESLint 检查
- [ ] 代码已格式化（Prettier）
- [ ] 单元测试通过
- [ ] E2E 测试通过
- [ ] TypeScript 编译无错误
- [ ] 构建成功

### 代码审查标准
1. **代码质量**
   - 遵循项目编码规范
   - 适当的注释和文档
   - 合理的函数和组件拆分

2. **测试覆盖**
   - 新功能必须有对应测试
   - 测试覆盖率不低于80%
   - 关键路径必须有E2E测试

3. **性能考虑**
   - 避免不必要的重渲染
   - 合理使用缓存
   - 优化包大小

如果你有改进建议或发现问题，请：
1. 创建 Issue 描述问题
2. 提交 Pull Request 包含修复
3. 更新相关文档
