import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import PowerFlowAnimation from '../../../powerflow-web-designer/src/components/canvas/PowerFlowAnimation.vue';
import { 
  ConnectionLineType, 
  PowerFlowAnimationType, 
  PowerFlowDirection,
  LineCapStyle,
  LineJoinStyle
} from '../../../powerflow-web-designer/src/types/connection';
import { Position } from '../../../powerflow-web-designer/src/types/symbol';

// Mock the Konva components
vi.mock('vue-konva', () => ({
  VGroup: {
    name: 'VGroup',
    template: '<div><slot /></div>',
    props: ['config']
  },
  VLine: {
    name: 'VLine',
    template: '<div></div>',
    props: ['config']
  },
  VPath: {
    name: 'VPath',
    template: '<div></div>',
    props: ['config']
  },
  VCircle: {
    name: 'VCircle',
    template: '<div></div>',
    props: ['config']
  },
  VArc: {
    name: 'VArc',
    template: '<div></div>',
    props: ['config']
  }
}));

// Mock requestAnimationFrame and cancelAnimationFrame
global.requestAnimationFrame = vi.fn(callback => {
  return setTimeout(callback, 0);
});

global.cancelAnimationFrame = vi.fn(id => {
  clearTimeout(id);
});

describe('PowerFlowAnimation.vue', () => {
  let points: Position[];
  let flattenedPoints: number[];
  let bezierPath: string;

  beforeEach(() => {
    points = [
      { x: 0, y: 0 },
      { x: 100, y: 100 }
    ];
    
    flattenedPoints = [0, 0, 100, 100];
    
    bezierPath = 'M0,0 L100,100';
    
    vi.useFakeTimers();
  });

  it('renders flow animation when type is flow and animation is enabled', () => {
    const wrapper = mount(PowerFlowAnimation, {
      props: {
        points,
        flattenedPoints,
        bezierPath,
        connectionType: ConnectionLineType.STRAIGHT,
        animation: {
          type: PowerFlowAnimationType.FLOW,
          direction: PowerFlowDirection.FORWARD,
          speed: 5,
          enabled: true
        },
        lineWidth: 2,
        lineCap: LineCapStyle.ROUND,
        lineJoin: LineJoinStyle.ROUND,
        strokeColor: '#000000'
      }
    });

    // Advance timers to trigger animation
    vi.advanceTimersByTime(100);

    // Check that the component renders
    expect(wrapper.exists()).toBe(true);

    // Check that it renders VCircle components for flow animation
    expect(wrapper.findAllComponents({ name: 'VCircle' }).length).toBeGreaterThan(0);
  });

  it('renders pulse animation when type is pulse and animation is enabled', () => {
    const wrapper = mount(PowerFlowAnimation, {
      props: {
        points,
        flattenedPoints,
        bezierPath,
        connectionType: ConnectionLineType.STRAIGHT,
        animation: {
          type: PowerFlowAnimationType.PULSE,
          direction: PowerFlowDirection.FORWARD,
          speed: 5,
          enabled: true
        },
        lineWidth: 2,
        lineCap: LineCapStyle.ROUND,
        lineJoin: LineJoinStyle.ROUND,
        strokeColor: '#000000'
      }
    });

    // Advance timers to trigger animation
    vi.advanceTimersByTime(100);

    // Check that the component renders
    expect(wrapper.exists()).toBe(true);

    // Check that it renders VCircle components for pulse animation
    expect(wrapper.findAllComponents({ name: 'VCircle' }).length).toBeGreaterThan(0);
  });

  it('renders dash animation when type is dash and animation is enabled', () => {
    const wrapper = mount(PowerFlowAnimation, {
      props: {
        points,
        flattenedPoints,
        bezierPath,
        connectionType: ConnectionLineType.STRAIGHT,
        animation: {
          type: PowerFlowAnimationType.DASH,
          direction: PowerFlowDirection.FORWARD,
          speed: 5,
          enabled: true
        },
        lineWidth: 2,
        lineCap: LineCapStyle.ROUND,
        lineJoin: LineJoinStyle.ROUND,
        strokeColor: '#000000'
      }
    });

    // Check that the component renders
    expect(wrapper.exists()).toBe(true);

    // Check that it renders VLine component for dash animation
    expect(wrapper.findComponent({ name: 'VLine' }).exists()).toBe(true);
  });

  it('does not render animation when animation is disabled', () => {
    const wrapper = mount(PowerFlowAnimation, {
      props: {
        points,
        flattenedPoints,
        bezierPath,
        connectionType: ConnectionLineType.STRAIGHT,
        animation: {
          type: PowerFlowAnimationType.FLOW,
          direction: PowerFlowDirection.FORWARD,
          speed: 5,
          enabled: false
        },
        lineWidth: 2,
        lineCap: LineCapStyle.ROUND,
        lineJoin: LineJoinStyle.ROUND,
        strokeColor: '#000000'
      }
    });

    // Check that the component renders
    expect(wrapper.exists()).toBe(true);

    // Check that it does not render any animation components
    expect(wrapper.findAllComponents({ name: 'VCircle' }).length).toBe(0);
    expect(wrapper.findComponent({ name: 'VLine' }).exists()).toBe(false);
  });

  it('cleans up animation frame on unmount', () => {
    const wrapper = mount(PowerFlowAnimation, {
      props: {
        points,
        flattenedPoints,
        bezierPath,
        connectionType: ConnectionLineType.STRAIGHT,
        animation: {
          type: PowerFlowAnimationType.FLOW,
          direction: PowerFlowDirection.FORWARD,
          speed: 5,
          enabled: true
        },
        lineWidth: 2,
        lineCap: LineCapStyle.ROUND,
        lineJoin: LineJoinStyle.ROUND,
        strokeColor: '#000000'
      }
    });

    // Spy on cancelAnimationFrame
    const cancelAnimationFrameSpy = vi.spyOn(global, 'cancelAnimationFrame');

    // Unmount the component
    wrapper.unmount();

    // Check that cancelAnimationFrame was called
    expect(cancelAnimationFrameSpy).toHaveBeenCalled();
  });
});
