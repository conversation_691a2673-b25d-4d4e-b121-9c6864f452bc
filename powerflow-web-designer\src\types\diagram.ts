/**
 * Diagram types for PowerFlow Web Designer
 */

import type { SymbolInstance } from './symbolInstance';
import type { Connection } from './connection';
import type { TextElement } from './textElement';
import type { Position, Size } from './symbol';

// Group interface
export interface DiagramGroup {
  id: string;
  name: string;
  symbolInstanceIds: string[]; // References to symbol instances in this group
  position: Position; // Position of the group
  size: Size; // Size of the group
  rotation: number; // Rotation in degrees
  locked: boolean; // Whether the group is locked
  selected?: boolean; // UI state - whether the group is selected
}

// Layer interface
export interface DiagramLayer {
  id: string;
  name: string;
  visible: boolean;
  locked: boolean;
  symbolInstanceIds: string[]; // References to symbol instances in this layer
  connectionIds: string[]; // References to connections in this layer
  textElementIds?: string[]; // References to text elements in this layer
}

// Viewport interface
export interface DiagramViewport {
  position: Position; // Top-left corner position
  scale: number; // Zoom level
}

// Grid pattern type
export enum GridPatternType {
  LINES = 'lines',
  DOTS = 'dots',
}

// Grid settings interface
export interface DiagramGrid {
  visible: boolean;
  size: number; // Grid cell size
  snapToGrid: boolean;
  majorGridSize: number; // Size of major grid lines (multiple of size)
  minorColor: string; // Color of minor grid lines
  majorColor: string; // Color of major grid lines
  pattern: GridPatternType; // Grid pattern type
}

// Diagram interface
export interface Diagram {
  id: string;
  name: string;
  description?: string;
  size: Size; // Canvas size
  grid: DiagramGrid;
  viewport: DiagramViewport;
  layers: DiagramLayer[];
  symbolInstances: Record<string, SymbolInstance>; // Map of symbol instance ID to instance
  connections: Record<string, Connection>; // Map of connection ID to connection
  textElements: Record<string, TextElement>; // Map of text element ID to text element
  groups: Record<string, DiagramGroup>; // Map of group ID to group
  selectedSymbolIds: string[]; // IDs of selected symbols
  selectedConnectionIds: string[]; // IDs of selected connections
  selectedTextElementIds: string[]; // IDs of selected text elements
  selectedGroupIds: string[]; // IDs of selected groups
  selectedConnectionId?: string; // Currently selected connection for editing
  version: number; // For versioning/undo-redo
  created: Date;
  modified: Date;
}

// Factory function to create a new diagram layer
export function createDiagramLayer(
  id: string,
  name: string,
  visible: boolean = true,
  locked: boolean = false
): DiagramLayer {
  return {
    id,
    name,
    visible,
    locked,
    symbolInstanceIds: [],
    connectionIds: [],
    textElementIds: [],
  };
}

// Factory function to create a new diagram group
export function createDiagramGroup(
  id: string,
  name: string,
  symbolInstanceIds: string[] = [],
  position: Position = { x: 0, y: 0 },
  size: Size = { width: 0, height: 0 },
  rotation: number = 0,
  locked: boolean = false
): DiagramGroup {
  return {
    id,
    name,
    symbolInstanceIds,
    position,
    size,
    rotation,
    locked,
  };
}

// Factory function to create a new diagram
export function createDiagram(
  id: string,
  name: string,
  description: string = '',
  size: Size = { width: 5000, height: 5000 }
): Diagram {
  // Create default layer
  const defaultLayer = createDiagramLayer('layer-1', 'Layer 1');

  return {
    id,
    name,
    description,
    size,
    grid: {
      visible: false, // Grid disabled by default
      size: 20,
      snapToGrid: false, // Snap to grid disabled by default
      majorGridSize: 100, // 5 minor grid cells per major grid cell
      minorColor: '#e0e0e0',
      majorColor: '#c0c0c0',
      pattern: GridPatternType.LINES,
    },
    viewport: {
      position: { x: 0, y: 0 },
      scale: 1,
    },
    layers: [defaultLayer],
    symbolInstances: {},
    connections: {},
    textElements: {},
    groups: {},
    selectedSymbolIds: [],
    selectedConnectionIds: [],
    selectedTextElementIds: [],
    selectedGroupIds: [],
    selectedConnectionId: undefined,
    version: 1,
    created: new Date(),
    modified: new Date(),
  };
}

// Diagram operations

// Add a symbol instance to the diagram
export function addSymbolInstance(
  diagram: Diagram,
  symbolInstance: SymbolInstance,
  layerId?: string
): Diagram {
  // Use the specified layer or the first layer
  const targetLayerId = layerId || diagram.layers[0].id;
  const layerIndex = diagram.layers.findIndex(layer => layer.id === targetLayerId);

  if (layerIndex === -1) {
    throw new Error(`Layer with ID ${targetLayerId} not found`);
  }

  // Add the symbol instance to the diagram
  const updatedSymbolInstances = {
    ...diagram.symbolInstances,
    [symbolInstance.id]: symbolInstance,
  };

  // Add the symbol instance ID to the layer
  const updatedLayers = [...diagram.layers];
  updatedLayers[layerIndex] = {
    ...updatedLayers[layerIndex],
    symbolInstanceIds: [...updatedLayers[layerIndex].symbolInstanceIds, symbolInstance.id],
  };

  return {
    ...diagram,
    symbolInstances: updatedSymbolInstances,
    layers: updatedLayers,
    modified: new Date(),
    version: diagram.version + 1,
  };
}

// Add a connection to the diagram
export function addConnection(
  diagram: Diagram,
  connection: Connection,
  layerId?: string
): Diagram {
  // Use the specified layer or the first layer
  const targetLayerId = layerId || diagram.layers[0].id;
  const layerIndex = diagram.layers.findIndex(layer => layer.id === targetLayerId);

  if (layerIndex === -1) {
    throw new Error(`Layer with ID ${targetLayerId} not found`);
  }

  // Add the connection to the diagram
  const updatedConnections = {
    ...diagram.connections,
    [connection.id]: connection,
  };

  // Add the connection ID to the layer
  const updatedLayers = [...diagram.layers];
  updatedLayers[layerIndex] = {
    ...updatedLayers[layerIndex],
    connectionIds: [...updatedLayers[layerIndex].connectionIds, connection.id],
  };

  return {
    ...diagram,
    connections: updatedConnections,
    layers: updatedLayers,
    modified: new Date(),
    version: diagram.version + 1,
  };
}

// Add a text element to the diagram
export function addTextElement(
  diagram: Diagram,
  textElement: TextElement,
  layerId?: string
): Diagram {
  // Use the specified layer or the first layer
  const targetLayerId = layerId || diagram.layers[0].id;
  const layerIndex = diagram.layers.findIndex(layer => layer.id === targetLayerId);

  if (layerIndex === -1) {
    throw new Error(`Layer with ID ${targetLayerId} not found`);
  }

  // Add the text element to the diagram
  const updatedTextElements = {
    ...diagram.textElements,
    [textElement.id]: textElement,
  };

  // Update the layer to include the text element
  const updatedLayers = [...diagram.layers];
  updatedLayers[layerIndex] = {
    ...updatedLayers[layerIndex],
    textElementIds: [
      ...(updatedLayers[layerIndex].textElementIds || []),
      textElement.id,
    ],
  };

  return {
    ...diagram,
    textElements: updatedTextElements,
    layers: updatedLayers,
    modified: new Date(),
    version: diagram.version + 1,
  };
}

// Remove a text element from the diagram
export function removeTextElement(diagram: Diagram, textElementId: string): Diagram {
  // Find the layer containing this text element
  const layerIndex = diagram.layers.findIndex(layer =>
    layer.textElementIds?.includes(textElementId)
  );

  if (layerIndex === -1) {
    throw new Error(`Text element with ID ${textElementId} not found in any layer`);
  }

  // Remove the text element from the diagram
  const updatedTextElements = { ...diagram.textElements };
  delete updatedTextElements[textElementId];

  // Update the layer to remove the text element
  const updatedLayers = [...diagram.layers];
  updatedLayers[layerIndex] = {
    ...updatedLayers[layerIndex],
    textElementIds: (updatedLayers[layerIndex].textElementIds || []).filter(
      id => id !== textElementId
    ),
  };

  // Remove from selection if selected
  const updatedSelectedTextElementIds = diagram.selectedTextElementIds.filter(
    id => id !== textElementId
  );

  return {
    ...diagram,
    textElements: updatedTextElements,
    layers: updatedLayers,
    selectedTextElementIds: updatedSelectedTextElementIds,
    modified: new Date(),
    version: diagram.version + 1,
  };
}
