<template>
  <div class="simulation-controls">
    <a-button-group>
      <a-tooltip :title="$locale.t('simulation.start')">
        <a-button
          type="primary"
          :disabled="isSimulationRunning || !hasCurrentDiagram"
          @click="startSimulation"
        >
          <template #icon><play-circle-outlined /></template>
        </a-button>
      </a-tooltip>

      <a-tooltip :title="$locale.t('simulation.pause')">
        <a-button
          :disabled="!isSimulationRunning || !hasCurrentDiagram"
          @click="pauseSimulation"
        >
          <template #icon><pause-circle-outlined /></template>
        </a-button>
      </a-tooltip>

      <a-tooltip :title="$locale.t('simulation.stop')">
        <a-button
          :disabled="!isSimulationRunning && !isSimulationPaused || !hasCurrentDiagram"
          @click="stopSimulation"
        >
          <template #icon><stop-outlined /></template>
        </a-button>
      </a-tooltip>

      <a-tooltip :title="$locale.t('simulation.step')">
        <a-button
          :disabled="isSimulationRunning || !hasCurrentDiagram"
          @click="stepSimulation"
        >
          <template #icon><step-forward-outlined /></template>
        </a-button>
      </a-tooltip>
    </a-button-group>

    <a-tooltip :title="$locale.t('simulation.speed')">
      <a-slider
        v-model:value="simulationSpeed"
        :min="1"
        :max="10"
        :step="1"
        :disabled="!hasCurrentDiagram"
        style="width: 100px; margin: 0 16px;"
        @change="updateSimulationSpeed"
      />
    </a-tooltip>

    <a-tooltip :title="$locale.t('simulation.time')">
      <a-statistic
        :value="simulationTime"
        :precision="1"
        suffix="s"
        style="margin: 0 16px;"
        :value-style="{ fontSize: '14px' }"
      />
    </a-tooltip>

    <a-dropdown :disabled="!hasCurrentDiagram">
      <a-button>
        <template #icon><setting-outlined /></template>
        {{ $locale.t('simulation.settings') }}
      </a-button>
      <template #overlay>
        <a-menu>
          <a-menu-item @click="showSimulationSettingsModal">
            <template #icon><setting-outlined /></template>
            {{ $locale.t('simulation.settings') }}
          </a-menu-item>
          <a-menu-item @click="resetSimulation">
            <template #icon><reload-outlined /></template>
            {{ $locale.t('simulation.reset') }}
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item @click="exportSimulationResults">
            <template #icon><export-outlined /></template>
            {{ $locale.t('simulation.exportResults') }}
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>

    <!-- Simulation Settings Modal -->
    <a-modal
      v-model:visible="isSettingsModalVisible"
      :title="$locale.t('simulation.settings')"
      @ok="saveSimulationSettings"
    >
      <a-form :model="simulationSettings" layout="vertical">
        <a-form-item :label="$locale.t('simulation.mode')">
          <a-select v-model:value="simulationSettings.mode">
            <a-select-option value="steady-state">{{ $locale.t('simulation.steadyState') }}</a-select-option>
            <a-select-option value="transient">{{ $locale.t('simulation.transient') }}</a-select-option>
            <a-select-option value="fault">{{ $locale.t('simulation.fault') }}</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item :label="$locale.t('simulation.timeStep')">
          <a-input-number
            v-model:value="simulationSettings.timeStep"
            :min="10"
            :max="1000"
            :step="10"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item :label="$locale.t('simulation.maxTime')">
          <a-input-number
            v-model:value="simulationSettings.maxTime"
            :min="1"
            :max="3600"
            :step="1"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item :label="$locale.t('simulation.autoStop')">
          <a-switch v-model:checked="simulationSettings.autoStop" />
        </a-form-item>

        <a-form-item :label="$locale.t('simulation.showAnimation')">
          <a-switch v-model:checked="simulationSettings.showAnimation" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  StepForwardOutlined,
  SettingOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';
import { useSimulationStore } from '@/stores/simulation';
import { message } from 'ant-design-vue';

// Stores
const diagramStore = useDiagramStore();
const simulationStore = useSimulationStore();

// Computed
const hasCurrentDiagram = computed(() => !!diagramStore.currentDiagram);
const isSimulationRunning = computed(() => simulationStore.isRunning);
const isSimulationPaused = computed(() => simulationStore.isPaused);
const simulationTime = computed(() => simulationStore.currentTime.toFixed(1));

// State
const simulationSpeed = ref<number>(5);
const isSettingsModalVisible = ref<boolean>(false);
const simulationSettings = ref({
  mode: 'steady-state',
  timeStep: 100,
  maxTime: 60,
  autoStop: true,
  showAnimation: true
});

// Methods
const startSimulation = () => {
  if (!hasCurrentDiagram.value) return;

  simulationStore.startSimulation({
    diagramId: diagramStore.currentDiagram!.id,
    speed: simulationSpeed.value,
    settings: { ...simulationSettings.value }
  });

  message.success('Simulation started');
};

const pauseSimulation = () => {
  simulationStore.pauseSimulation();
  message.info('Simulation paused');
};

const stopSimulation = () => {
  simulationStore.stopSimulation();
  message.info('Simulation stopped');
};

const stepSimulation = () => {
  if (!hasCurrentDiagram.value) return;

  simulationStore.stepSimulation({
    diagramId: diagramStore.currentDiagram!.id,
    timeStep: simulationSettings.value.timeStep / 1000 // Convert to seconds
  });

  message.info('Simulation stepped forward');
};

const resetSimulation = () => {
  simulationStore.resetSimulation();
  message.info('Simulation reset');
};

const updateSimulationSpeed = (speed: number) => {
  simulationStore.setSimulationSpeed(speed);
};

const showSimulationSettingsModal = () => {
  // Load current settings from the store
  simulationSettings.value = {
    ...simulationSettings.value,
    ...simulationStore.settings
  };

  isSettingsModalVisible.value = true;
};

const saveSimulationSettings = () => {
  simulationStore.updateSettings(simulationSettings.value);
  isSettingsModalVisible.value = false;
  message.success('Simulation settings updated');
};

const exportSimulationResults = () => {
  if (!simulationStore.hasResults) {
    message.warning('No simulation results to export');
    return;
  }

  // Create a JSON file with the results
  const results = simulationStore.getResults();
  const dataStr = JSON.stringify(results, null, 2);
  const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

  // Create a download link and trigger it
  const exportName = `simulation-results-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
  const linkElement = document.createElement('a');
  linkElement.setAttribute('href', dataUri);
  linkElement.setAttribute('download', exportName);
  linkElement.click();

  message.success('Simulation results exported');
};
</script>

<style scoped>
.simulation-controls {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 100%;
}
</style>
