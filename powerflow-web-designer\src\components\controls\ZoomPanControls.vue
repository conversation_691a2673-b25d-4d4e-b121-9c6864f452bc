<template>
  <div class="zoom-pan-controls">
    <div class="zoom-controls">
      <a-button
        type="text"
        shape="circle"
        :disabled="zoomLevel <= minZoom"
        @click="zoomOut"
        title="Zoom Out (Ctrl+-)"
      >
        <template #icon><minus-outlined /></template>
      </a-button>

      <div class="zoom-level" @click="toggleZoomSlider">
        {{ Math.round(zoomLevel * 100) }}%
      </div>

      <a-button
        type="text"
        shape="circle"
        :disabled="zoomLevel >= maxZoom"
        @click="zoomIn"
        title="Zoom In (Ctrl++)"
      >
        <template #icon><plus-outlined /></template>
      </a-button>

      <div class="zoom-slider" v-if="showZoomSlider">
        <a-slider
          v-model:value="sliderZoomLevel"
          :min="minZoom * 100"
          :max="maxZoom * 100"
          :step="1"
          @change="handleZoomSliderChange"
        />
      </div>
    </div>

    <div class="pan-controls">
      <div class="pan-buttons">
        <a-button
          type="text"
          shape="circle"
          @click="panUp"
          title="Pan Up"
        >
          <template #icon><arrow-up-outlined /></template>
        </a-button>

        <div class="pan-horizontal">
          <a-button
            type="text"
            shape="circle"
            @click="panLeft"
            title="Pan Left"
          >
            <template #icon><arrow-left-outlined /></template>
          </a-button>

          <a-button
            type="text"
            shape="circle"
            @click="panRight"
            title="Pan Right"
          >
            <template #icon><arrow-right-outlined /></template>
          </a-button>
        </div>

        <a-button
          type="text"
          shape="circle"
          @click="panDown"
          title="Pan Down"
        >
          <template #icon><arrow-down-outlined /></template>
        </a-button>
      </div>

      <div class="view-controls">
        <a-button
          type="text"
          shape="circle"
          @click="resetView"
          title="Reset View (Ctrl+0)"
        >
          <template #icon><home-outlined /></template>
        </a-button>

        <a-button
          type="text"
          shape="circle"
          @click="fitContent"
          title="Fit Content (Ctrl+1)"
        >
          <template #icon><fullscreen-outlined /></template>
        </a-button>

        <a-button
          type="text"
          shape="circle"
          @click="zoomToSelection"
          title="Zoom to Selection (Ctrl+2)"
          :disabled="!hasSelection"
        >
          <template #icon><select-outlined /></template>
        </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import {
  MinusOutlined,
  PlusOutlined,
  HomeOutlined,
  FullscreenOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  SelectOutlined
} from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';
import { DiagramViewport } from '@/types/diagram';
import { Position } from '@/types/symbol';

// Props
const props = defineProps<{
  stageRef: any;
  minZoom?: number;
  maxZoom?: number;
  zoomStep?: number;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const showZoomSlider = ref(false);
const sliderZoomLevel = ref(100); // 100%
const panDistance = ref(50); // pixels

// Computed
const minZoom = computed(() => props.minZoom || 0.1);
const maxZoom = computed(() => props.maxZoom || 5);
const zoomStep = computed(() => props.zoomStep || 0.1);

const zoomLevel = computed(() => {
  if (!diagramStore.currentDiagram) return 1;
  return diagramStore.currentDiagram.viewport.scale;
});

// Check if there are any selected elements
const hasSelection = computed(() => {
  if (!diagramStore.currentDiagram) return false;

  return (
    diagramStore.currentDiagram.selectedSymbolIds.length > 0 ||
    diagramStore.currentDiagram.selectedConnectionIds.length > 0 ||
    diagramStore.currentDiagram.selectedGroupIds.length > 0
  );
});

// Methods
const zoomIn = () => {
  if (!diagramStore.currentDiagram) return;

  const newZoom = Math.min(zoomLevel.value + zoomStep.value, maxZoom.value);
  updateZoom(newZoom);

  // Update slider value
  sliderZoomLevel.value = Math.round(newZoom * 100);
};

const zoomOut = () => {
  if (!diagramStore.currentDiagram) return;

  const newZoom = Math.max(zoomLevel.value - zoomStep.value, minZoom.value);
  updateZoom(newZoom);

  // Update slider value
  sliderZoomLevel.value = Math.round(newZoom * 100);
};

const updateZoom = (newZoom: number, point?: { x: number, y: number }) => {
  if (!diagramStore.currentDiagram || !props.stageRef) return;

  const stage = props.stageRef.getNode();
  const oldScale = zoomLevel.value;

  // Get the zoom point (center or specified point)
  const zoomPoint = point || {
    x: stage.width() / 2,
    y: stage.height() / 2,
  };

  // Calculate the new position
  const mousePointTo = {
    x: (zoomPoint.x - stage.x()) / oldScale,
    y: (zoomPoint.y - stage.y()) / oldScale,
  };

  // Update the viewport with animation
  animateViewport({
    position: {
      x: zoomPoint.x / newZoom - mousePointTo.x,
      y: zoomPoint.y / newZoom - mousePointTo.y,
    },
    scale: newZoom,
  });
};

const toggleZoomSlider = () => {
  showZoomSlider.value = !showZoomSlider.value;

  // Initialize slider value
  if (showZoomSlider.value) {
    sliderZoomLevel.value = Math.round(zoomLevel.value * 100);
  }
};

const handleZoomSliderChange = (value: number) => {
  if (!diagramStore.currentDiagram) return;

  // Convert percentage to scale
  const newZoom = value / 100;
  updateZoom(newZoom);
};

const panUp = () => {
  if (!diagramStore.currentDiagram) return;

  const currentPosition = diagramStore.currentDiagram.viewport.position;
  const distance = panDistance.value / zoomLevel.value;

  animateViewport({
    position: {
      x: currentPosition.x,
      y: currentPosition.y - distance,
    },
    scale: zoomLevel.value,
  });
};

const panDown = () => {
  if (!diagramStore.currentDiagram) return;

  const currentPosition = diagramStore.currentDiagram.viewport.position;
  const distance = panDistance.value / zoomLevel.value;

  animateViewport({
    position: {
      x: currentPosition.x,
      y: currentPosition.y + distance,
    },
    scale: zoomLevel.value,
  });
};

const panLeft = () => {
  if (!diagramStore.currentDiagram) return;

  const currentPosition = diagramStore.currentDiagram.viewport.position;
  const distance = panDistance.value / zoomLevel.value;

  animateViewport({
    position: {
      x: currentPosition.x - distance,
      y: currentPosition.y,
    },
    scale: zoomLevel.value,
  });
};

const panRight = () => {
  if (!diagramStore.currentDiagram) return;

  const currentPosition = diagramStore.currentDiagram.viewport.position;
  const distance = panDistance.value / zoomLevel.value;

  animateViewport({
    position: {
      x: currentPosition.x + distance,
      y: currentPosition.y,
    },
    scale: zoomLevel.value,
  });
};

const resetView = () => {
  if (!diagramStore.currentDiagram) return;

  // Reset to default view with animation
  animateViewport({
    position: { x: 0, y: 0 },
    scale: 1,
  });

  // Update slider value
  sliderZoomLevel.value = 100;
};

const fitContent = () => {
  if (!diagramStore.currentDiagram || !props.stageRef) return;

  const stage = props.stageRef.getNode();
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Calculate the bounds of all symbols
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  // Check if there are any symbols
  const symbols = Object.values(diagramStore.currentDiagram.symbolInstances);
  if (symbols.length === 0) {
    resetView();
    return;
  }

  // Calculate the bounds
  symbols.forEach(symbol => {
    const x = symbol.position.x;
    const y = symbol.position.y;
    const width = symbol.dimensions?.width || 100;
    const height = symbol.dimensions?.height || 100;

    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + width);
    maxY = Math.max(maxY, y + height);
  });

  // Include connections in the bounds calculation
  const connections = Object.values(diagramStore.currentDiagram.connections);
  connections.forEach(connection => {
    if (connection.waypoints) {
      connection.waypoints.forEach(waypoint => {
        minX = Math.min(minX, waypoint.x);
        minY = Math.min(minY, waypoint.y);
        maxX = Math.max(maxX, waypoint.x);
        maxY = Math.max(maxY, waypoint.y);
      });
    }
  });

  // Add padding
  const padding = 50;
  minX -= padding;
  minY -= padding;
  maxX += padding;
  maxY += padding;

  // Calculate the content dimensions
  const contentWidth = maxX - minX;
  const contentHeight = maxY - minY;

  // Calculate the scale to fit
  const scaleX = stageWidth / contentWidth;
  const scaleY = stageHeight / contentHeight;
  const scale = Math.min(scaleX, scaleY, maxZoom.value);

  // Calculate the center position
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;

  // Update the viewport with animation
  animateViewport({
    position: {
      x: centerX - stageWidth / (2 * scale),
      y: centerY - stageHeight / (2 * scale),
    },
    scale: scale,
  });

  // Update slider value
  sliderZoomLevel.value = Math.round(scale * 100);
};

const zoomToSelection = () => {
  if (!diagramStore.currentDiagram || !props.stageRef) return;

  const stage = props.stageRef.getNode();
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Calculate the bounds of selected elements
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  // Check if there are any selected symbols
  const selectedSymbolIds = diagramStore.currentDiagram.selectedSymbolIds;
  if (selectedSymbolIds.length === 0 &&
      diagramStore.currentDiagram.selectedConnectionIds.length === 0 &&
      diagramStore.currentDiagram.selectedGroupIds.length === 0) {
    return;
  }

  // Calculate the bounds of selected symbols
  selectedSymbolIds.forEach(id => {
    const symbol = diagramStore.currentDiagram!.symbolInstances[id];
    if (symbol) {
      const x = symbol.position.x;
      const y = symbol.position.y;
      const width = symbol.dimensions?.width || 100;
      const height = symbol.dimensions?.height || 100;

      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x + width);
      maxY = Math.max(maxY, y + height);
    }
  });

  // Calculate the bounds of selected connections
  const selectedConnectionIds = diagramStore.currentDiagram.selectedConnectionIds;
  selectedConnectionIds.forEach(id => {
    const connection = diagramStore.currentDiagram!.connections[id];
    if (connection && connection.waypoints) {
      connection.waypoints.forEach(waypoint => {
        minX = Math.min(minX, waypoint.x);
        minY = Math.min(minY, waypoint.y);
        maxX = Math.max(maxX, waypoint.x);
        maxY = Math.max(maxY, waypoint.y);
      });
    }
  });

  // Calculate the bounds of selected groups
  const selectedGroupIds = diagramStore.currentDiagram.selectedGroupIds;
  selectedGroupIds.forEach(id => {
    const group = diagramStore.currentDiagram!.groups[id];
    if (group) {
      const x = group.position.x;
      const y = group.position.y;
      const width = group.size.width;
      const height = group.size.height;

      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x + width);
      maxY = Math.max(maxY, y + height);
    }
  });

  // Add padding
  const padding = 50;
  minX -= padding;
  minY -= padding;
  maxX += padding;
  maxY += padding;

  // Calculate the content dimensions
  const contentWidth = maxX - minX;
  const contentHeight = maxY - minY;

  // Calculate the scale to fit
  const scaleX = stageWidth / contentWidth;
  const scaleY = stageHeight / contentHeight;
  const scale = Math.min(scaleX, scaleY, maxZoom.value);

  // Calculate the center position
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;

  // Update the viewport with animation
  animateViewport({
    position: {
      x: centerX - stageWidth / (2 * scale),
      y: centerY - stageHeight / (2 * scale),
    },
    scale: scale,
  });

  // Update slider value
  sliderZoomLevel.value = Math.round(scale * 100);
};

// Animate viewport changes
const animateViewport = (targetViewport: DiagramViewport) => {
  if (!diagramStore.currentDiagram) return;

  // Get current viewport
  const currentViewport = diagramStore.currentDiagram.viewport;

  // Update the viewport immediately
  diagramStore.currentDiagram.viewport = targetViewport;
};
</script>

<style scoped>
.zoom-pan-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  z-index: 100;
  backdrop-filter: blur(4px);
}

.zoom-controls {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
}

.zoom-level {
  margin: 0 8px;
  font-size: 12px;
  min-width: 40px;
  text-align: center;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.zoom-level:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.zoom-slider {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  background-color: white;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.pan-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pan-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.pan-horizontal {
  display: flex;
  margin: 4px 0;
}

.view-controls {
  display: flex;
  justify-content: center;
}
</style>
