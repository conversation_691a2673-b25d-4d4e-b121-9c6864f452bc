/**
 * Simple test script to verify symbol loading
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test loading JSON symbol files
const symbolFiles = [
  'src/data/symbols/gb-transformers.json',
  'src/data/symbols/gb-switchgear.json',
  'src/data/symbols/gb-motors.json',
  'src/data/symbols/gb-measurement.json',
  'src/data/symbols/gb-protection.json',
  'src/data/symbols/gb-transmission.json',
  'src/data/symbols/gb-capacitors.json',
  'src/data/symbols/gb-reactors.json',
  'src/data/symbols/gb-relays.json'
];

let totalSymbols = 0;
let validSymbols = 0;

console.log('Testing GB Standard Symbol Files...\n');

symbolFiles.forEach(filePath => {
  try {
    const fullPath = path.join(__dirname, filePath);
    if (fs.existsSync(fullPath)) {
      const data = fs.readFileSync(fullPath, 'utf8');
      const symbols = JSON.parse(data);
      
      console.log(`✓ ${filePath}: ${symbols.length} symbols`);
      totalSymbols += symbols.length;
      
      // Validate each symbol
      symbols.forEach((symbol, index) => {
        if (symbol.id && symbol.category && symbol.name && symbol.svg) {
          validSymbols++;
        } else {
          console.log(`  ⚠ Symbol ${index} missing required fields`);
        }
      });
      
      // Show first symbol as example
      if (symbols.length > 0) {
        console.log(`  Example: ${symbols[0].name} (${symbols[0].id})`);
      }
      
    } else {
      console.log(`✗ ${filePath}: File not found`);
    }
  } catch (error) {
    console.log(`✗ ${filePath}: Error - ${error.message}`);
  }
  console.log('');
});

console.log(`Summary:`);
console.log(`- Total symbols: ${totalSymbols}`);
console.log(`- Valid symbols: ${validSymbols}`);
console.log(`- Success rate: ${((validSymbols / totalSymbols) * 100).toFixed(1)}%`);

// Test categories
const categories = new Set();
symbolFiles.forEach(filePath => {
  try {
    const fullPath = path.join(__dirname, filePath);
    if (fs.existsSync(fullPath)) {
      const data = fs.readFileSync(fullPath, 'utf8');
      const symbols = JSON.parse(data);
      symbols.forEach(symbol => {
        if (symbol.category) {
          categories.add(symbol.category);
        }
      });
    }
  } catch (error) {
    // Ignore errors for this test
  }
});

console.log(`\nCategories found: ${Array.from(categories).join(', ')}`);
