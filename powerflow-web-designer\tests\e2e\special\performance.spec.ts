import { test, expect } from '@playwright/test';
import { navigateToEditor, navigateToViewer, createNewDiagram, generateLargeDiagram } from '../helpers';

/**
 * Level 4: Special Performance Tests
 * These tests verify that the application performs well under various conditions.
 */

test.describe('Performance', () => {
  test('should load the homepage quickly', async ({ page }) => {
    // Measure the time it takes to load the homepage
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForSelector('h1');
    
    const loadTime = Date.now() - startTime;
    console.log(`Homepage load time: ${loadTime}ms`);
    
    // Check that the load time is reasonable (adjust threshold as needed)
    expect(loadTime).toBeLessThan(5000);
  });

  test('should render a large diagram efficiently', async ({ page }) => {
    await navigateToEditor(page);
    
    // Create a new diagram
    await createNewDiagram(page, 'Performance Test Diagram');
    
    // Measure the time it takes to generate and render a large diagram
    const startTime = Date.now();
    
    // Generate a large diagram with 100 symbols
    await generateLargeDiagram(page, 100);
    
    // Wait for rendering to complete
    await page.waitForTimeout(1000);
    
    const renderTime = Date.now() - startTime;
    console.log(`Large diagram render time: ${renderTime}ms`);
    
    // Check that the render time is reasonable (adjust threshold as needed)
    expect(renderTime).toBeLessThan(10000);
    
    // Verify that the diagram is actually rendered
    const symbolCount = await page.locator('.symbol-instance').count();
    console.log(`Rendered symbol count: ${symbolCount}`);
    expect(symbolCount).toBeGreaterThan(50);
  });

  test('should handle rapid user interactions', async ({ page }) => {
    await navigateToEditor(page);
    await createNewDiagram(page, 'Interaction Performance Test');
    
    // Add a few symbols to the diagram
    for (let i = 0; i < 5; i++) {
      const symbolLibrary = page.locator('.symbol-library');
      const firstSymbol = symbolLibrary.locator('.symbol-item').first();
      await firstSymbol.dragTo(page.locator('.diagram-canvas'));
    }
    
    // Measure the time it takes to perform rapid selections
    const startTime = Date.now();
    
    // Rapidly select different symbols
    const symbols = page.locator('.symbol-instance');
    for (let i = 0; i < 5; i++) {
      await symbols.nth(i % await symbols.count()).click();
      await page.waitForTimeout(100); // Small delay to simulate rapid clicks
    }
    
    const interactionTime = Date.now() - startTime;
    console.log(`Rapid interaction time: ${interactionTime}ms`);
    
    // Check that the interaction time is reasonable
    expect(interactionTime).toBeLessThan(5000);
  });

  test('should switch between editor and viewer quickly', async ({ page }) => {
    await navigateToEditor(page);
    await createNewDiagram(page, 'Navigation Performance Test');
    
    // Add a few symbols to the diagram
    const symbolLibrary = page.locator('.symbol-library');
    const firstSymbol = symbolLibrary.locator('.symbol-item').first();
    await firstSymbol.dragTo(page.locator('.diagram-canvas'));
    
    // Measure the time it takes to switch to viewer
    const startTime = Date.now();
    
    // Navigate to viewer
    await page.getByRole('button', { name: 'View' }).click();
    await page.waitForSelector('.viewer-header');
    
    const switchTime = Date.now() - startTime;
    console.log(`Editor to viewer switch time: ${switchTime}ms`);
    
    // Check that the switch time is reasonable
    expect(switchTime).toBeLessThan(3000);
  });
});
