<template>
  <div class="connection-waypoint-editor">
    <v-group>
      <!-- Waypoints for polyline connections -->
      <template v-if="connection.lineType === 'polyline' && connection.waypoints">
        <v-circle
          v-for="(waypoint, index) in connection.waypoints"
          :key="`waypoint-${index}`"
          :config="{
            x: waypoint.x,
            y: waypoint.y,
            radius: 6,
            fill: '#ffffff',
            stroke: '#0096FF',
            strokeWidth: 2,
            draggable: !isLocked && !readOnly,
            dragDistance: 1,
          }"
          @dragmove="(e) => handleWaypointDragMove(index, e)"
          @click="(e) => handleWaypointClick(index, e)"
          @contextmenu="(e) => handleWaypointContextMenu(index, e)"
        />
        
        <!-- Add waypoint buttons on line segments -->
        <v-circle
          v-for="(midpoint, index) in midpoints"
          :key="`midpoint-${index}`"
          :config="{
            x: midpoint.x,
            y: midpoint.y,
            radius: 4,
            fill: '#ffffff',
            stroke: '#0096FF',
            strokeWidth: 1,
            opacity: isHovered ? 1 : 0,
          }"
          @click="(e) => handleAddWaypoint(index, e)"
        />
      </template>
      
      <!-- Control points for bezier connections -->
      <template v-if="connection.lineType === 'bezier' && connection.controlPoints">
        <v-circle
          v-for="(controlPoint, index) in connection.controlPoints"
          :key="`control-${index}`"
          :config="{
            x: controlPoint.x,
            y: controlPoint.y,
            radius: 6,
            fill: '#ffffff',
            stroke: '#FF9600',
            strokeWidth: 2,
            draggable: !isLocked && !readOnly,
            dragDistance: 1,
          }"
          @dragmove="(e) => handleControlPointDragMove(index, e)"
        />
        
        <!-- Lines connecting control points to endpoints -->
        <v-line
          v-if="connection.controlPoints.length >= 1"
          :config="{
            points: [
              sourcePosition.x,
              sourcePosition.y,
              connection.controlPoints[0].x,
              connection.controlPoints[0].y
            ],
            stroke: '#FF9600',
            strokeWidth: 1,
            dash: [2, 2],
            opacity: isHovered ? 0.5 : 0,
          }"
        />
        
        <v-line
          v-if="connection.controlPoints.length >= 2"
          :config="{
            points: [
              connection.controlPoints[1].x,
              connection.controlPoints[1].y,
              targetPosition.x,
              targetPosition.y
            ],
            stroke: '#FF9600',
            strokeWidth: 1,
            dash: [2, 2],
            opacity: isHovered ? 0.5 : 0,
          }"
        />
      </template>
    </v-group>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { Connection, ConnectionLineType } from '@/types/connection';
import { Position } from '@/types/symbol';
import { addWaypoint, removeWaypoint, calculateBezierControlPoints } from '@/utils/connectionUtils';

// Props
const props = defineProps<{
  connection: Connection;
  sourcePosition: Position;
  targetPosition: Position;
  isSelected: boolean;
  isLocked: boolean;
  readOnly: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'waypointMove', connectionId: string, waypointIndex: number, position: Position): void;
  (e: 'waypointAdd', connectionId: string, position: Position, index: number): void;
  (e: 'waypointRemove', connectionId: string, waypointIndex: number): void;
  (e: 'controlPointMove', connectionId: string, controlPointIndex: number, position: Position): void;
  (e: 'contextMenu', event: MouseEvent, waypointIndex: number): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const isHovered = ref(false);

// Computed
const points = computed<Position[]>(() => {
  const result: Position[] = [props.sourcePosition];
  
  if (props.connection.waypoints) {
    result.push(...props.connection.waypoints);
  }
  
  result.push(props.targetPosition);
  
  return result;
});

const midpoints = computed<Position[]>(() => {
  const result: Position[] = [];
  
  // Calculate midpoints between each pair of points
  for (let i = 0; i < points.value.length - 1; i++) {
    const start = points.value[i];
    const end = points.value[i + 1];
    
    result.push({
      x: (start.x + end.x) / 2,
      y: (start.y + end.y) / 2
    });
  }
  
  return result;
});

// Methods
const handleWaypointDragMove = (index: number, e: any) => {
  const position = {
    x: e.target.x(),
    y: e.target.y(),
  };

  // Snap to grid if enabled
  const grid = diagramStore.currentDiagram?.grid;
  if (grid?.snapToGrid) {
    const gridSize = grid.size;
    position.x = Math.round(position.x / gridSize) * gridSize;
    position.y = Math.round(position.y / gridSize) * gridSize;
  }

  emit('waypointMove', props.connection.id, index, position);
};

const handleControlPointDragMove = (index: number, e: any) => {
  const position = {
    x: e.target.x(),
    y: e.target.y(),
  };

  // Snap to grid if enabled
  const grid = diagramStore.currentDiagram?.grid;
  if (grid?.snapToGrid) {
    const gridSize = grid.size;
    position.x = Math.round(position.x / gridSize) * gridSize;
    position.y = Math.round(position.y / gridSize) * gridSize;
  }

  emit('controlPointMove', props.connection.id, index, position);
};

const handleWaypointClick = (index: number, e: any) => {
  // Double click to remove waypoint
  if (e.evt.detail === 2) {
    emit('waypointRemove', props.connection.id, index);
  }
};

const handleWaypointContextMenu = (index: number, e: any) => {
  e.evt.preventDefault();
  e.evt.stopPropagation();
  
  emit('contextMenu', e.evt, index);
};

const handleAddWaypoint = (index: number, e: any) => {
  const position = {
    x: e.target.x(),
    y: e.target.y(),
  };
  
  // Snap to grid if enabled
  const grid = diagramStore.currentDiagram?.grid;
  if (grid?.snapToGrid) {
    const gridSize = grid.size;
    position.x = Math.round(position.x / gridSize) * gridSize;
    position.y = Math.round(position.y / gridSize) * gridSize;
  }
  
  emit('waypointAdd', props.connection.id, position, index);
};

// Watch for changes in connection type to initialize control points for bezier curves
watch(
  () => props.connection.lineType,
  (newType) => {
    if (newType === ConnectionLineType.BEZIER && (!props.connection.controlPoints || props.connection.controlPoints.length === 0)) {
      // Calculate default control points
      const controlPoints = calculateBezierControlPoints(props.sourcePosition, props.targetPosition);
      
      // Update the connection
      diagramStore.updateConnection({
        ...props.connection,
        controlPoints
      });
    }
  }
);
</script>

<style scoped>
.connection-waypoint-editor {
  pointer-events: none;
}

.connection-waypoint-editor :deep(circle) {
  pointer-events: all;
  cursor: pointer;
}
</style>
