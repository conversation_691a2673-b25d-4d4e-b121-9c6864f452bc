/**
 * Test file for enhanced busbar functionality
 */

import {
  generateBusbarConnectionPoints,
  createSymbolDefinition
} from '../types/symbol';
import { BusbarOrientation } from '../types/symbol';
import { 
  getBusbarProperties,
  updateBusbarConnectionPoints,
  isBusbarSymbol,
  generateBusbarSVG,
  validateBusbarProperties 
} from '../utils/busbarUtils';

// Test data
const testBusbarProperties = {
  orientation: BusbarOrientation.HORIZONTAL,
  length: 200,
  width: 20,
  connectionPointCount: 3,
  connectionPointSpacing: 60,
  autoDistributePoints: true,
};

const testDimensions = { width: 200, height: 60 };

// Test 1: Generate connection points for horizontal busbar
console.log('Test 1: Horizontal busbar connection points');
const horizontalPoints = generateBusbarConnectionPoints(
  BusbarOrientation.HORIZONTAL,
  testDimensions,
  3,
  true
);
console.log('Generated points:', horizontalPoints);

// Test 2: Generate connection points for vertical busbar
console.log('\nTest 2: Vertical busbar connection points');
const verticalPoints = generateBusbarConnectionPoints(
  BusbarOrientation.VERTICAL,
  { width: 60, height: 200 },
  3,
  true
);
console.log('Generated points:', verticalPoints);

// Test 3: Generate SVG for horizontal busbar
console.log('\nTest 3: Generate horizontal busbar SVG');
const horizontalSVG = generateBusbarSVG(testBusbarProperties);
console.log('Generated SVG:', horizontalSVG);

// Test 4: Generate SVG for vertical busbar
console.log('\nTest 4: Generate vertical busbar SVG');
const verticalBusbarProperties = {
  ...testBusbarProperties,
  orientation: BusbarOrientation.VERTICAL,
  length: 200,
  width: 20,
};
const verticalSVG = generateBusbarSVG(verticalBusbarProperties);
console.log('Generated SVG:', verticalSVG);

// Test 5: Validate busbar properties
console.log('\nTest 5: Validate busbar properties');
const validationErrors = validateBusbarProperties({
  length: 300,
  width: 25,
  connectionPointCount: 5,
  connectionPointSpacing: 50,
});
console.log('Validation errors:', validationErrors);

// Test 6: Validate invalid properties
console.log('\nTest 6: Validate invalid properties');
const invalidValidationErrors = validateBusbarProperties({
  length: 600, // Too large
  width: 5,    // Too small
  connectionPointCount: 15, // Too many
  connectionPointSpacing: 10, // Too small
});
console.log('Invalid validation errors:', invalidValidationErrors);

// Test 7: Create enhanced busbar symbol definition
console.log('\nTest 7: Create enhanced busbar symbol definition');
const enhancedBusbar = createSymbolDefinition(
  'test-busbar-horizontal',
  'busbar' as any,
  'Test Horizontal Busbar',
  horizontalSVG,
  testDimensions,
  horizontalPoints,
  [
    {
      id: 'voltage',
      name: 'Voltage',
      dataType: 'number' as any,
      defaultValue: 0,
      description: 'Busbar voltage (kV)',
    },
  ],
  {
    fillColor: '#ff0000',
    strokeColor: '#ff0000',
    lineWidth: 2,
    busbar: testBusbarProperties,
  }
);
console.log('Enhanced busbar definition:', enhancedBusbar);

// Test 8: Check if symbol is busbar
console.log('\nTest 8: Check if symbol is busbar');
console.log('Is busbar symbol:', isBusbarSymbol(enhancedBusbar));

// Test 9: Get busbar properties from symbol instance
console.log('\nTest 9: Get busbar properties from symbol instance');
const testSymbolInstance = {
  id: 'test-instance',
  definitionId: 'test-busbar-horizontal',
  position: { x: 100, y: 100 },
  rotation: 0,
  scale: 1,
  bindings: {},
  properties: {
    busbar: testBusbarProperties,
  },
};

const retrievedProperties = getBusbarProperties(testSymbolInstance, enhancedBusbar);
console.log('Retrieved properties:', retrievedProperties);

console.log('\n✅ All busbar tests completed successfully!');

export {
  testBusbarProperties,
  horizontalPoints,
  verticalPoints,
  enhancedBusbar,
  testSymbolInstance,
};
