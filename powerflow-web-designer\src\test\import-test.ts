/**
 * Test imports to verify all modules are correctly exported
 */

// Test symbol types import
import { 
  BusbarOrientation,
  generateBusbarConnectionPoints,
  createSymbolDefinition,
  ConnectionPointType 
} from '../types/symbol';

// Test busbar utils import
import { 
  getBusbarProperties,
  updateBusbarConnectionPoints,
  isBusbarSymbol,
  generateBusbarSVG,
  validateBusbarProperties,
  getBusbarConnectionPoints,
  getBusbarConnectionPointPosition
} from '../utils/busbarUtils';

console.log('✅ All imports successful!');

// Test basic functionality
const testOrientation = BusbarOrientation.HORIZONTAL;
console.log('✅ BusbarOrientation enum works:', testOrientation);

const testPoints = generateBusbarConnectionPoints(
  BusbarOrientation.HORIZONTAL,
  { width: 200, height: 60 },
  3,
  true
);
console.log('✅ generateBusbarConnectionPoints works:', testPoints.length, 'points generated');

const testSVG = generateBusbarSVG({
  orientation: BusbarOrientation.HORIZONTAL,
  length: 200,
  width: 20,
  connectionPointCount: 3,
  connectionPointSpacing: 60,
  autoDistributePoints: true,
});
console.log('✅ generateBusbarSVG works:', testSVG.length, 'characters');

const validationErrors = validateBusbarProperties({
  length: 200,
  width: 20,
  connectionPointCount: 3,
});
console.log('✅ validateBusbarProperties works:', validationErrors.length, 'errors');

console.log('\n🎉 All import and function tests passed!');

export {
  testOrientation,
  testPoints,
  testSVG,
  validationErrors
};
