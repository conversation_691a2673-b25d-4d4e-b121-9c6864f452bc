/**
 * Symbol definition types for PowerFlow Web Designer
 */

// Position type for coordinates
export interface Position {
  x: number;
  y: number;
}

// Size type for dimensions
export interface Size {
  width: number;
  height: number;
}

// Connection point type
export enum ConnectionPointType {
  INPUT = 'input',
  OUTPUT = 'output',
  BIDIRECTIONAL = 'bidirectional',
}

// Connection point definition
export interface ConnectionPoint {
  id: string;
  position: Position;
  type: ConnectionPointType;
  label?: string;
}

// Binding slot data type
export enum BindingDataType {
  BOOLEAN = 'boolean',
  NUMBER = 'number',
  STRING = 'string',
  ENUM = 'enum',
}

// Binding slot definition
export interface BindingSlot {
  id: string;
  name: string;
  dataType: BindingDataType;
  description?: string;
  defaultValue?: any;
  enumValues?: string[]; // For ENUM type
}

// Symbol category - Extended for GB standard compliance
export enum SymbolCategory {
  DATACENTER = 'datacenter',
  SWITCHGEAR = 'switchgear',
  TRANSFORMER = 'transformer',
  BUSBAR = 'busbar',
  MEASUREMENT = 'measurement',
  PROTECTION = 'protection',
  DISTRIBUTION = 'distribution',
  GENERATOR = 'generator',
  LOAD = 'load',
  MOTOR = 'motor',
  TRANSMISSION = 'transmission',
  CAPACITOR = 'capacitor',
  REACTOR = 'reactor',
  RELAY = 'relay',
  OTHER = 'other',
}

// Busbar orientation enum
export enum BusbarOrientation {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
}

// Enhanced busbar properties
export interface BusbarProperties {
  orientation: BusbarOrientation;
  length: number;
  width: number;
  connectionPointCount: number;
  connectionPointSpacing: number;
  autoDistributePoints: boolean;
}

// Symbol properties
export interface SymbolProperties {
  fillColor: string;
  strokeColor: string;
  lineWidth: number;
  // Enhanced properties for specific symbol types
  busbar?: BusbarProperties;
  [key: string]: any; // Allow for additional custom properties
}

// Symbol definition interface
export interface SymbolDefinition {
  id: string;
  category: SymbolCategory;
  name: string;
  svg: string;
  dimensions: Size;
  connectionPoints: ConnectionPoint[];
  bindingSlots: BindingSlot[];
  properties: SymbolProperties;
}

// Generate dynamic connection points for busbar
export function generateBusbarConnectionPoints(
  orientation: BusbarOrientation,
  dimensions: Size,
  connectionPointCount: number,
  autoDistribute: boolean = true
): ConnectionPoint[] {
  const points: ConnectionPoint[] = [];

  if (orientation === BusbarOrientation.HORIZONTAL) {
    // Horizontal busbar: connection points on top and bottom
    const spacing = autoDistribute ? dimensions.width / (connectionPointCount + 1) : dimensions.width / connectionPointCount;

    for (let i = 0; i < connectionPointCount; i++) {
      const x = autoDistribute ? spacing * (i + 1) : spacing * i + spacing / 2;

      // Top connection points
      points.push({
        id: `top_${i + 1}`,
        position: { x, y: 0 },
        type: ConnectionPointType.BIDIRECTIONAL,
        label: `上${i + 1}`,
      });

      // Bottom connection points
      points.push({
        id: `bottom_${i + 1}`,
        position: { x, y: dimensions.height },
        type: ConnectionPointType.BIDIRECTIONAL,
        label: `下${i + 1}`,
      });
    }
  } else {
    // Vertical busbar: connection points on left and right
    const spacing = autoDistribute ? dimensions.height / (connectionPointCount + 1) : dimensions.height / connectionPointCount;

    for (let i = 0; i < connectionPointCount; i++) {
      const y = autoDistribute ? spacing * (i + 1) : spacing * i + spacing / 2;

      // Left connection points
      points.push({
        id: `left_${i + 1}`,
        position: { x: 0, y },
        type: ConnectionPointType.BIDIRECTIONAL,
        label: `左${i + 1}`,
      });

      // Right connection points
      points.push({
        id: `right_${i + 1}`,
        position: { x: dimensions.width, y },
        type: ConnectionPointType.BIDIRECTIONAL,
        label: `右${i + 1}`,
      });
    }
  }

  return points;
}

// Factory function to create a new symbol definition
export function createSymbolDefinition(
  id: string,
  category: SymbolCategory,
  name: string,
  svg: string,
  dimensions: Size,
  connectionPoints: ConnectionPoint[] = [],
  bindingSlots: BindingSlot[] = [],
  properties: Partial<SymbolProperties> = {}
): SymbolDefinition {
  // Default properties
  const defaultProperties: SymbolProperties = {
    fillColor: '#ffffff',
    strokeColor: '#000000',
    lineWidth: 1,
  };

  return {
    id,
    category,
    name,
    svg,
    dimensions,
    connectionPoints,
    bindingSlots,
    properties: { ...defaultProperties, ...properties },
  };
}
