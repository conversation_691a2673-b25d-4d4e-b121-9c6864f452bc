<script setup lang="ts">
import LocaleProvider from './components/locale/LocaleProvider.vue';
import { ConfigProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
</script>

<template>
  <div class="app">
    <config-provider :locale="zhCN">
      <locale-provider>
        <router-view />
      </locale-provider>
    </config-provider>
  </div>
</template>

<style>
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.app {
  width: 100%;
  height: 100vh;
}
</style>
