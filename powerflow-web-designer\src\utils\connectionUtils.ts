/**
 * Utility functions for connections
 */

import type { Position } from '@/types/symbol';
import type { Connection } from '@/types/connection';
import { ConnectionLineType } from '@/types/connection';

/**
 * Detect line intersection between two line segments
 * @param line1Start Start point of first line
 * @param line1End End point of first line
 * @param line2Start Start point of second line
 * @param line2End End point of second line
 * @returns Intersection point or null if no intersection
 */
export function detectLineIntersections(
  line1Start: Position,
  line1End: Position,
  line2Start: Position,
  line2End: Position
): Position | null {
  // Line 1 represented as a1x + b1y = c1
  const a1 = line1End.y - line1Start.y;
  const b1 = line1Start.x - line1End.x;
  const c1 = a1 * line1Start.x + b1 * line1Start.y;

  // Line 2 represented as a2x + b2y = c2
  const a2 = line2End.y - line2Start.y;
  const b2 = line2Start.x - line2End.x;
  const c2 = a2 * line2Start.x + b2 * line2Start.y;

  const determinant = a1 * b2 - a2 * b1;

  // If lines are parallel, no intersection
  if (determinant === 0) {
    return null;
  }

  // Calculate intersection point
  const x = (b2 * c1 - b1 * c2) / determinant;
  const y = (a1 * c2 - a2 * c1) / determinant;

  // Check if intersection point is within both line segments
  const onLine1 = isPointOnLineSegment(line1Start, line1End, { x, y });
  const onLine2 = isPointOnLineSegment(line2Start, line2End, { x, y });

  if (onLine1 && onLine2) {
    return { x, y };
  }

  return null;
}

/**
 * Check if a point is on a line segment
 * @param lineStart Start point of line
 * @param lineEnd End point of line
 * @param point Point to check
 * @returns True if point is on line segment
 */
function isPointOnLineSegment(
  lineStart: Position,
  lineEnd: Position,
  point: Position
): boolean {
  // Check if point is within the bounding box of the line segment
  const minX = Math.min(lineStart.x, lineEnd.x) - 0.1; // Small epsilon for floating point errors
  const maxX = Math.max(lineStart.x, lineEnd.x) + 0.1;
  const minY = Math.min(lineStart.y, lineEnd.y) - 0.1;
  const maxY = Math.max(lineStart.y, lineEnd.y) + 0.1;

  return (
    point.x >= minX &&
    point.x <= maxX &&
    point.y >= minY &&
    point.y <= maxY
  );
}

/**
 * Calculate control points for a bezier curve
 * @param start Start point
 * @param end End point
 * @param curvature Curvature factor (0-1)
 * @returns Array of control points
 */
export function calculateBezierControlPoints(
  start: Position,
  end: Position,
  curvature: number = 0.5
): Position[] {
  const dx = end.x - start.x;
  const dy = end.y - start.y;
  const distance = Math.sqrt(dx * dx + dy * dy);

  // Calculate control points based on curvature
  const cp1 = {
    x: start.x + dx / 3,
    y: start.y + (dy / 3) * curvature
  };

  const cp2 = {
    x: end.x - dx / 3,
    y: end.y - (dy / 3) * curvature
  };

  return [cp1, cp2];
}

/**
 * Add a waypoint to a polyline connection
 * @param connection Connection to add waypoint to
 * @param position Position of the waypoint
 * @param index Index to insert the waypoint at (optional)
 * @returns Updated connection
 */
export function addWaypoint(
  connection: Connection,
  position: Position,
  index?: number
): Connection {
  // Make sure the connection is a polyline
  if (connection.lineType !== ConnectionLineType.POLYLINE) {
    return {
      ...connection,
      lineType: ConnectionLineType.POLYLINE,
      waypoints: [position]
    };
  }

  // Create a copy of the waypoints
  const waypoints = [...(connection.waypoints || [])];

  // Insert the waypoint at the specified index or append it
  if (index !== undefined && index >= 0 && index <= waypoints.length) {
    waypoints.splice(index, 0, position);
  } else {
    waypoints.push(position);
  }

  // Return updated connection
  return {
    ...connection,
    waypoints
  };
}

/**
 * Remove a waypoint from a polyline connection
 * @param connection Connection to remove waypoint from
 * @param index Index of the waypoint to remove
 * @returns Updated connection
 */
export function removeWaypoint(
  connection: Connection,
  index: number
): Connection {
  // Make sure the connection is a polyline with waypoints
  if (
    connection.lineType !== ConnectionLineType.POLYLINE ||
    !connection.waypoints ||
    connection.waypoints.length === 0 ||
    index < 0 ||
    index >= connection.waypoints.length
  ) {
    return connection;
  }

  // Create a copy of the waypoints and remove the specified one
  const waypoints = [...connection.waypoints];
  waypoints.splice(index, 1);

  // Return updated connection
  return {
    ...connection,
    waypoints
  };
}

/**
 * Find all intersections between a connection path and other connections
 * @param path Path to check for intersections
 * @param otherConnections Other connections to check against
 * @returns Array of intersection points with connection IDs
 */
export function findPathIntersections(
  path: Position[],
  otherConnections: Record<string, { id: string, waypoints: Position[] }>
): { point: Position, connectionId: string }[] {
  const intersections: { point: Position, connectionId: string }[] = [];

  // Check each segment of the path
  for (let i = 0; i < path.length - 1; i++) {
    const start = path[i];
    const end = path[i + 1];

    // Skip very short segments
    const segmentLength = Math.sqrt(
      Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2)
    );

    if (segmentLength < 5) {
      continue;
    }

    // Check against each other connection
    for (const connectionId in otherConnections) {
      const otherConnection = otherConnections[connectionId];
      const otherPath = otherConnection.waypoints;

      // Skip if the other path is too short
      if (!otherPath || otherPath.length < 2) {
        continue;
      }

      // Check each segment of the other path
      for (let j = 0; j < otherPath.length - 1; j++) {
        const otherStart = otherPath[j];
        const otherEnd = otherPath[j + 1];

        // Skip very short segments
        const otherSegmentLength = Math.sqrt(
          Math.pow(otherEnd.x - otherStart.x, 2) + Math.pow(otherEnd.y - otherStart.y, 2)
        );

        if (otherSegmentLength < 5) {
          continue;
        }

        // Check for intersection
        const intersection = detectLineIntersections(start, end, otherStart, otherEnd);

        if (intersection) {
          intersections.push({
            point: intersection,
            connectionId: otherConnection.id,
          });
        }
      }
    }
  }

  return intersections;
}

/**
 * Create jump-over crossings for a connection path
 * @param path Path to add jumps to
 * @param intersections Array of intersection points
 * @param jumpSize Size of the jump
 * @returns Path with jumps added
 */
export function createJumpOverCrossings(
  path: Position[],
  intersections: { point: Position, connectionId: string }[],
  jumpSize: number = 10
): Position[] {
  if (path.length < 2 || intersections.length === 0) {
    return path;
  }

  // Sort intersections by distance from start
  const sortedIntersections = [...intersections].sort((a, b) => {
    const distA = getDistanceFromStart(path, a.point);
    const distB = getDistanceFromStart(path, b.point);
    return distA - distB;
  });

  // Create a new path with jumps
  const newPath: Position[] = [];
  let currentSegmentStart = 0;

  for (const intersection of sortedIntersections) {
    // Find the segment containing the intersection
    let segmentIndex = -1;
    for (let i = 0; i < path.length - 1; i++) {
      const start = path[i];
      const end = path[i + 1];

      if (isPointOnLineSegment(start, end, intersection.point)) {
        segmentIndex = i;
        break;
      }
    }

    if (segmentIndex === -1) {
      continue; // Intersection not found on any segment
    }

    // Add all points up to the segment
    for (let i = currentSegmentStart; i <= segmentIndex; i++) {
      newPath.push(path[i]);
    }

    // Add jump points
    const start = path[segmentIndex];
    const end = path[segmentIndex + 1];

    // Calculate direction vector
    const dx = end.x - start.x;
    const dy = end.y - start.y;
    const length = Math.sqrt(dx * dx + dy * dy);

    if (length > 0) {
      // Calculate normalized direction vector
      const nx = dx / length;
      const ny = dy / length;

      // Calculate perpendicular vector
      const px = -ny;
      const py = nx;

      // Calculate distance from segment start to intersection
      const t = ((intersection.point.x - start.x) * nx + (intersection.point.y - start.y) * ny) / length;

      // Calculate jump points
      const jumpStart = {
        x: start.x + nx * (t * length - jumpSize),
        y: start.y + ny * (t * length - jumpSize)
      };

      const jumpMid1 = {
        x: start.x + nx * (t * length - jumpSize / 2) + px * jumpSize,
        y: start.y + ny * (t * length - jumpSize / 2) + py * jumpSize
      };

      const jumpMid2 = {
        x: start.x + nx * (t * length + jumpSize / 2) + px * jumpSize,
        y: start.y + ny * (t * length + jumpSize / 2) + py * jumpSize
      };

      const jumpEnd = {
        x: start.x + nx * (t * length + jumpSize),
        y: start.y + ny * (t * length + jumpSize)
      };

      // Add jump points to path
      newPath.push(jumpStart);
      newPath.push(jumpMid1);
      newPath.push(jumpMid2);
      newPath.push(jumpEnd);
    }

    // Update current segment
    currentSegmentStart = segmentIndex + 1;
  }

  // Add remaining points
  for (let i = currentSegmentStart; i < path.length; i++) {
    newPath.push(path[i]);
  }

  return newPath;
}

/**
 * Calculate the distance of a point from the start of a path
 * @param path Path to check
 * @param point Point to check
 * @returns Distance from start of path
 */
function getDistanceFromStart(path: Position[], point: Position): number {
  let distance = 0;
  let foundSegment = false;

  for (let i = 0; i < path.length - 1; i++) {
    const start = path[i];
    const end = path[i + 1];

    // Check if point is on this segment
    if (isPointOnLineSegment(start, end, point)) {
      // Add distance from segment start to point
      distance += Math.sqrt(
        Math.pow(point.x - start.x, 2) + Math.pow(point.y - start.y, 2)
      );
      foundSegment = true;
      break;
    }

    // Add segment length to distance
    distance += Math.sqrt(
      Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2)
    );
  }

  return foundSegment ? distance : Infinity;
}