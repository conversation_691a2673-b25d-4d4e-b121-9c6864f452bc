<template>
  <div
    v-if="visible"
    class="context-menu"
    :style="{
      left: `${position.x}px`,
      top: `${position.y}px`,
    }"
  >
    <a-menu>
      <!-- Symbol context menu items -->
      <template v-if="contextType === 'symbol'">
        <a-menu-item key="edit" @click="handleEdit">
          <template #icon><edit-outlined /></template>
          Edit Properties
        </a-menu-item>
        <a-menu-item key="copy" @click="handleCopy">
          <template #icon><copy-outlined /></template>
          Copy
        </a-menu-item>
        <a-menu-item key="cut" @click="handleCut" :disabled="readOnly">
          <template #icon><scissor-outlined /></template>
          Cut
        </a-menu-item>
        <a-menu-item key="delete" @click="handleDelete" :disabled="readOnly">
          <template #icon><delete-outlined /></template>
          Delete
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="create-group" @click="handleCreateGroup" :disabled="!canCreateGroup || readOnly">
          <template #icon><group-outlined /></template>
          Group
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="bring-to-front" @click="handleBringToFront" :disabled="readOnly">
          <template #icon><vertical-align-top-outlined /></template>
          Bring to Front
        </a-menu-item>
        <a-menu-item key="send-to-back" @click="handleSendToBack" :disabled="readOnly">
          <template #icon><vertical-align-bottom-outlined /></template>
          Send to Back
        </a-menu-item>
        <a-menu-divider />
        <a-sub-menu key="align" title="Align" :disabled="!canAlign || readOnly">
          <template #icon><menu-outlined /></template>
          <a-menu-item key="align-left" @click="handleAlign(AlignmentType.LEFT)">
            <template #icon><align-left-outlined /></template>
            Align Left
          </a-menu-item>
          <a-menu-item key="align-center" @click="handleAlign(AlignmentType.CENTER)">
            <template #icon><align-center-outlined /></template>
            Align Center
          </a-menu-item>
          <a-menu-item key="align-right" @click="handleAlign(AlignmentType.RIGHT)">
            <template #icon><align-right-outlined /></template>
            Align Right
          </a-menu-item>
          <a-menu-item key="align-top" @click="handleAlign(AlignmentType.TOP)">
            <template #icon><align-left-outlined class="rotate-90" /></template>
            Align Top
          </a-menu-item>
          <a-menu-item key="align-middle" @click="handleAlign(AlignmentType.MIDDLE)">
            <template #icon><align-center-outlined class="rotate-90" /></template>
            Align Middle
          </a-menu-item>
          <a-menu-item key="align-bottom" @click="handleAlign(AlignmentType.BOTTOM)">
            <template #icon><align-right-outlined class="rotate-90" /></template>
            Align Bottom
          </a-menu-item>
        </a-sub-menu>
        <a-sub-menu key="distribute" title="Distribute" :disabled="!canDistribute || readOnly">
          <template #icon><menu-outlined /></template>
          <a-menu-item key="distribute-horizontal" @click="handleDistribute(DistributionType.HORIZONTAL)">
            <template #icon><column-width-outlined /></template>
            Distribute Horizontally
          </a-menu-item>
          <a-menu-item key="distribute-vertical" @click="handleDistribute(DistributionType.VERTICAL)">
            <template #icon><column-height-outlined /></template>
            Distribute Vertically
          </a-menu-item>
        </a-sub-menu>
      </template>

      <!-- Group context menu items -->
      <template v-else-if="contextType === 'group'">
        <a-menu-item key="edit" @click="handleEdit">
          <template #icon><edit-outlined /></template>
          Edit Properties
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="ungroup" @click="handleUngroup" :disabled="readOnly">
          <template #icon><ungroup-outlined /></template>
          Ungroup
        </a-menu-item>
        <a-menu-item key="delete" @click="handleDelete" :disabled="readOnly">
          <template #icon><delete-outlined /></template>
          Delete
        </a-menu-item>
      </template>

      <!-- Connection context menu items -->
      <template v-else-if="contextType === 'connection'">
        <a-menu-item key="edit" @click="handleEdit">
          <template #icon><edit-outlined /></template>
          Edit Properties
        </a-menu-item>
        <a-menu-item key="delete" @click="handleDelete" :disabled="readOnly">
          <template #icon><delete-outlined /></template>
          Delete
        </a-menu-item>
        <a-menu-divider />
        <a-sub-menu key="line-type" title="Line Type" :disabled="readOnly">
          <template #icon><line-outlined /></template>
          <a-menu-item key="straight" @click="handleLineTypeChange('straight')">
            Straight
          </a-menu-item>
          <a-menu-item key="polyline" @click="handleLineTypeChange('polyline')">
            Polyline
          </a-menu-item>
          <a-menu-item key="bezier" @click="handleLineTypeChange('bezier')">
            Bezier
          </a-menu-item>
          <a-menu-item key="smart" @click="handleLineTypeChange('smart')">
            Smart
          </a-menu-item>
        </a-sub-menu>
      </template>

      <!-- Canvas context menu items -->
      <template v-else>
        <a-menu-item key="paste" @click="handlePaste" :disabled="readOnly || !canPaste">
          <template #icon><snippets-outlined /></template>
          Paste
        </a-menu-item>
        <a-menu-item key="select-all" @click="handleSelectAll">
          <template #icon><select-outlined /></template>
          Select All
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="fit-content" @click="handleFitContent">
          <template #icon><fullscreen-outlined /></template>
          Fit Content
        </a-menu-item>
        <a-menu-item key="reset-view" @click="handleResetView">
          <template #icon><home-outlined /></template>
          Reset View
        </a-menu-item>
      </template>
    </a-menu>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import {
  EditOutlined,
  CopyOutlined,
  ScissorOutlined,
  DeleteOutlined,
  SnippetsOutlined,
  SelectOutlined,
  FullscreenOutlined,
  HomeOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignBottomOutlined,
  LineOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  MenuOutlined,
  ColumnHeightOutlined,
  ColumnWidthOutlined,
  GroupOutlined,
  UngroupOutlined,
} from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';
import { Position } from '@/types/symbol';
import { ConnectionLineType } from '@/types/connection';
import { AlignmentType, DistributionType } from '@/utils/alignment';

// Props
const props = defineProps<{
  readOnly?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'edit'): void;
  (e: 'copy'): void;
  (e: 'cut'): void;
  (e: 'paste', x: number, y: number): void;
  (e: 'delete'): void;
  (e: 'select-all'): void;
  (e: 'fit-content'): void;
  (e: 'reset-view'): void;
  (e: 'bring-to-front'): void;
  (e: 'send-to-back'): void;
  (e: 'line-type-change', type: ConnectionLineType): void;
  (e: 'align', type: AlignmentType): void;
  (e: 'distribute', type: DistributionType): void;
  (e: 'create-group'): void;
  (e: 'ungroup'): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const visible = ref(false);
const position = ref<Position>({ x: 0, y: 0 });
const contextType = ref<'symbol' | 'connection' | 'canvas' | 'group'>('canvas');
const canPaste = ref(false);

// Computed
const canAlign = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return diagramStore.currentDiagram.selectedSymbolIds.length >= 2;
});

const canDistribute = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return diagramStore.currentDiagram.selectedSymbolIds.length >= 3;
});

const canCreateGroup = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return diagramStore.currentDiagram.selectedSymbolIds.length >= 2;
});

// Methods
const show = (
  x: number,
  y: number,
  type: 'symbol' | 'connection' | 'canvas' | 'group' = 'canvas',
  canPasteValue = false
) => {
  position.value = { x, y };
  contextType.value = type;
  canPaste.value = canPasteValue;
  visible.value = true;

  // Add click event listener to hide the menu when clicking outside
  setTimeout(() => {
    document.addEventListener('click', handleClickOutside);
  }, 0);
};

const hide = () => {
  visible.value = false;
  document.removeEventListener('click', handleClickOutside);
};

const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const menu = document.querySelector('.context-menu');

  if (menu && !menu.contains(target)) {
    hide();
  }
};

const handleEdit = () => {
  emit('edit');
  hide();
};

const handleCopy = () => {
  emit('copy');
  hide();
};

const handleCut = () => {
  emit('cut');
  hide();
};

const handlePaste = () => {
  emit('paste', position.value.x, position.value.y);
  hide();
};

const handleDelete = () => {
  emit('delete');
  hide();
};

const handleSelectAll = () => {
  emit('select-all');
  hide();
};

const handleFitContent = () => {
  emit('fit-content');
  hide();
};

const handleResetView = () => {
  emit('reset-view');
  hide();
};

const handleBringToFront = () => {
  emit('bring-to-front');
  hide();
};

const handleSendToBack = () => {
  emit('send-to-back');
  hide();
};

const handleLineTypeChange = (type: string) => {
  emit('line-type-change', type as ConnectionLineType);
  hide();
};

const handleAlign = (type: AlignmentType) => {
  diagramStore.alignSymbols(type);
  hide();
};

const handleDistribute = (type: DistributionType) => {
  diagramStore.distributeSymbols(type);
  hide();
};

const handleCreateGroup = () => {
  emit('create-group');
  hide();
};

const handleUngroup = () => {
  emit('ungroup');
  hide();
};

// Lifecycle hooks
onMounted(() => {
  // Add escape key event listener to hide the menu
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape' && visible.value) {
      hide();
    }
  });
});

onUnmounted(() => {
  // Remove event listeners
  document.removeEventListener('click', handleClickOutside);
});

// Expose methods to parent component
defineExpose({
  show,
  hide,
});
</script>

<style scoped>
.context-menu {
  position: fixed;
  z-index: 1000;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-width: 160px;
}

.rotate-90 {
  transform: rotate(90deg);
}
</style>
