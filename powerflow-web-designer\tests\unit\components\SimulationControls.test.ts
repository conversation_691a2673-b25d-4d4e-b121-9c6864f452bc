import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import SimulationControls from '../../../powerflow-web-designer/src/components/toolbar/SimulationControls.vue';
import { createPinia, setActivePinia } from 'pinia';
import { useDiagramStore } from '../../../powerflow-web-designer/src/stores/diagram';
import { useSimulationStore } from '../../../powerflow-web-designer/src/stores/simulation';

// Mock the Ant Design Vue components
vi.mock('ant-design-vue', () => {
  return {
    message: {
      success: vi.fn(),
      info: vi.fn(),
      warning: vi.fn(),
      error: vi.fn(),
    },
  };
});

// Mock the icons
vi.mock('@ant-design/icons-vue', () => ({
  PlayCircleOutlined: {
    name: 'PlayCircleOutlined',
    template: '<span />',
  },
  PauseCircleOutlined: {
    name: 'PauseCircleOutlined',
    template: '<span />',
  },
  StopOutlined: {
    name: 'StopOutlined',
    template: '<span />',
  },
  StepForwardOutlined: {
    name: 'StepForwardOutlined',
    template: '<span />',
  },
  SettingOutlined: {
    name: 'SettingOutlined',
    template: '<span />',
  },
  ReloadOutlined: {
    name: 'ReloadOutlined',
    template: '<span />',
  },
  ExportOutlined: {
    name: 'ExportOutlined',
    template: '<span />',
  },
}));

describe('SimulationControls.vue', () => {
  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia());
    
    // Reset mocks
    vi.clearAllMocks();
  });
  
  it('renders the simulation controls', () => {
    const wrapper = mount(SimulationControls, {
      global: {
        stubs: {
          'a-button': true,
          'a-button-group': true,
          'a-tooltip': true,
          'a-slider': true,
          'a-statistic': true,
          'a-dropdown': true,
          'a-menu': true,
          'a-menu-item': true,
          'a-menu-divider': true,
          'a-modal': true,
          'a-form': true,
          'a-form-item': true,
          'a-select': true,
          'a-select-option': true,
          'a-input-number': true,
          'a-switch': true,
        },
      },
    });
    
    // Check that the component renders
    expect(wrapper.exists()).toBe(true);
  });
  
  it('disables start button when simulation is running', async () => {
    // Setup the store with a running simulation
    const simulationStore = useSimulationStore();
    simulationStore.isRunning = true;
    
    const wrapper = mount(SimulationControls, {
      global: {
        stubs: {
          'a-button': {
            template: '<button :disabled="disabled"><slot /></button>',
            props: ['disabled'],
          },
          'a-button-group': true,
          'a-tooltip': {
            template: '<div><slot /></div>',
          },
          'a-slider': true,
          'a-statistic': true,
          'a-dropdown': true,
          'a-menu': true,
          'a-menu-item': true,
          'a-menu-divider': true,
          'a-modal': true,
          'a-form': true,
          'a-form-item': true,
          'a-select': true,
          'a-select-option': true,
          'a-input-number': true,
          'a-switch': true,
        },
      },
    });
    
    // Find the start button (first button in the group)
    const startButton = wrapper.findAll('button')[0];
    
    // Check that it's disabled
    expect(startButton.attributes('disabled')).toBeDefined();
  });
  
  it('disables pause button when simulation is not running', async () => {
    // Setup the store with a stopped simulation
    const simulationStore = useSimulationStore();
    simulationStore.isRunning = false;
    
    const wrapper = mount(SimulationControls, {
      global: {
        stubs: {
          'a-button': {
            template: '<button :disabled="disabled"><slot /></button>',
            props: ['disabled'],
          },
          'a-button-group': true,
          'a-tooltip': {
            template: '<div><slot /></div>',
          },
          'a-slider': true,
          'a-statistic': true,
          'a-dropdown': true,
          'a-menu': true,
          'a-menu-item': true,
          'a-menu-divider': true,
          'a-modal': true,
          'a-form': true,
          'a-form-item': true,
          'a-select': true,
          'a-select-option': true,
          'a-input-number': true,
          'a-switch': true,
        },
      },
    });
    
    // Find the pause button (second button in the group)
    const pauseButton = wrapper.findAll('button')[1];
    
    // Check that it's disabled
    expect(pauseButton.attributes('disabled')).toBeDefined();
  });
  
  it('calls startSimulation when start button is clicked', async () => {
    // Setup the store with a diagram
    const diagramStore = useDiagramStore();
    diagramStore.currentDiagram = {
      id: 'test-diagram',
      name: 'Test Diagram',
      symbolInstances: {},
      connections: {},
      layers: {},
      grid: { size: 20, visible: true, snapToGrid: true },
      selectedSymbolIds: [],
      selectedConnectionIds: [],
      selectedGroupIds: [],
    };
    
    // Mock the startSimulation method
    const simulationStore = useSimulationStore();
    const startSimulationSpy = vi.spyOn(simulationStore, 'startSimulation');
    
    const wrapper = mount(SimulationControls, {
      global: {
        stubs: {
          'a-button': {
            template: '<button @click="$emit(\'click\')"><slot /></button>',
          },
          'a-button-group': true,
          'a-tooltip': {
            template: '<div><slot /></div>',
          },
          'a-slider': true,
          'a-statistic': true,
          'a-dropdown': true,
          'a-menu': true,
          'a-menu-item': true,
          'a-menu-divider': true,
          'a-modal': true,
          'a-form': true,
          'a-form-item': true,
          'a-select': true,
          'a-select-option': true,
          'a-input-number': true,
          'a-switch': true,
        },
      },
    });
    
    // Find the start button (first button in the group)
    const startButton = wrapper.findAll('button')[0];
    
    // Click the button
    await startButton.trigger('click');
    
    // Check that startSimulation was called
    expect(startSimulationSpy).toHaveBeenCalled();
  });
});
