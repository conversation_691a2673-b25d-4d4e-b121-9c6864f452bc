<template>
  <div class="home">
    <div class="home-header">
      <h1>电力流程设计器</h1>
    </div>
    <div class="home-content">
      <div class="navigation">
        <a-button type="primary" size="large" @click="$router.push('/editor')">电力流程编辑器</a-button>
        <a-button size="large" @click="$router.push('/viewer')">电力流程查看器</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// Home component setup
</script>

<style scoped>
.home {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f2f5;
}

.home-header {
  height: 48px;
  background-color: var(--header-background-color);
  border-bottom: 1px solid var(--header-border-color);
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.home-header h1 {
  margin: 0;
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.home-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navigation {
  display: flex;
  gap: 1rem;
  background-color: white;
  padding: 40px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
