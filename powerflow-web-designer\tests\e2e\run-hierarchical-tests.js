// @ts-check
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Hierarchical Test Runner for PowerFlow Web Designer
 * This script runs tests in a hierarchical order, only proceeding to the next level
 * if the current level passes.
 */

// Create test results directory if it doesn't exist
const testResultsDir = path.join(__dirname, '..', '..', 'test-results');
if (!fs.existsSync(testResultsDir)) {
  fs.mkdirSync(testResultsDir, { recursive: true });
}

// Test levels and their corresponding directories
const testLevels = [
  {
    level: 1,
    name: 'Basic Tests',
    dir: path.join(__dirname, 'basic')
  },
  {
    level: 2,
    name: 'Core Functionality Tests',
    dir: path.join(__dirname, 'core')
  },
  {
    level: 3,
    name: 'Advanced Feature Tests',
    dir: path.join(__dirname, 'advanced')
  },
  {
    level: 4,
    name: 'Special Tests',
    dir: path.join(__dirname, 'special')
  }
];

// Configuration file path
const configFile = path.join(__dirname, '..', '..', 'playwright.config.ts');

// Run tests in order
let allTestsPassed = true;

for (const level of testLevels) {
  console.log(`\n=== Level ${level.level}: ${level.name} ===`);

  try {
    // Run the tests in this level
    const command = `npx playwright test "${level.dir}" --config="${configFile}"`;
    console.log(`Running command: ${command}`);

    execSync(command, { stdio: 'inherit' });
    console.log(`Level ${level.level} tests passed successfully.`);
  } catch (error) {
    console.error(`Level ${level.level} tests failed with error: ${error.message}`);
    allTestsPassed = false;
    break; // Stop testing if a level fails
  }
}

if (allTestsPassed) {
  console.log('\nAll tests completed successfully!');
} else {
  console.log('\nTest execution stopped due to failures.');
  process.exit(1);
}
