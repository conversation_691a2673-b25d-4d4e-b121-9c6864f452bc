<template>
  <div
    v-if="visible"
    class="waypoint-context-menu"
    :style="{
      left: `${position.x}px`,
      top: `${position.y}px`,
    }"
  >
    <div class="menu-item" @click="handleRemoveWaypoint">
      <i class="fas fa-trash"></i> Remove Waypoint
    </div>
    <div class="menu-item" @click="handleAddWaypointBefore">
      <i class="fas fa-plus"></i> Add Waypoint Before
    </div>
    <div class="menu-item" @click="handleAddWaypointAfter">
      <i class="fas fa-plus"></i> Add Waypoint After
    </div>
    <div class="menu-item" @click="handleSmoothCorner">
      <i class="fas fa-bezier-curve"></i> Smooth Corner
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { Position } from '@/types/symbol';
import { addWaypoint, removeWaypoint } from '@/utils/connectionUtils';

import { getConnectionPointPosition } from '@/types/symbolInstance';

// Props
const props = defineProps<{
  connectionId: string;
  waypointIndex: number;
  position: Position;
}>();

// Emits
const emit = defineEmits<{
  (e: 'close'): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const visible = ref(true);

// Methods
const handleRemoveWaypoint = () => {
  if (!diagramStore.currentDiagram) return;

  // Get the connection
  const connection = diagramStore.currentDiagram.connections[props.connectionId];
  if (!connection) return;

  // Remove the waypoint
  const updatedConnection = removeWaypoint(connection, props.waypointIndex);

  // Update the connection
  diagramStore.updateConnection(updatedConnection);

  // Close the menu
  emit('close');
};

const handleAddWaypointBefore = () => {
  if (!diagramStore.currentDiagram) return;

  // Get the connection
  const connection = diagramStore.currentDiagram.connections[props.connectionId];
  if (!connection || !connection.waypoints) return;

  // Get the current waypoint
  const currentWaypoint = connection.waypoints[props.waypointIndex];

  // Get the previous point (source or waypoint)
  const previousPoint = props.waypointIndex > 0
    ? connection.waypoints[props.waypointIndex - 1]
    : getConnectionEndpointPosition(connection.source);

  if (!previousPoint || !currentWaypoint) return;

  // Calculate the midpoint
  const midpoint = {
    x: (previousPoint.x + currentWaypoint.x) / 2,
    y: (previousPoint.y + currentWaypoint.y) / 2
  };

  // Add the waypoint
  const updatedConnection = addWaypoint(connection, midpoint, props.waypointIndex);

  // Update the connection
  diagramStore.updateConnection(updatedConnection);

  // Close the menu
  emit('close');
};

const handleAddWaypointAfter = () => {
  if (!diagramStore.currentDiagram) return;

  // Get the connection
  const connection = diagramStore.currentDiagram.connections[props.connectionId];
  if (!connection || !connection.waypoints) return;

  // Get the current waypoint
  const currentWaypoint = connection.waypoints[props.waypointIndex];

  // Get the next point (target or waypoint)
  const nextPoint = props.waypointIndex < connection.waypoints.length - 1
    ? connection.waypoints[props.waypointIndex + 1]
    : getConnectionEndpointPosition(connection.target);

  if (!nextPoint || !currentWaypoint) return;

  // Calculate the midpoint
  const midpoint = {
    x: (nextPoint.x + currentWaypoint.x) / 2,
    y: (nextPoint.y + currentWaypoint.y) / 2
  };

  // Add the waypoint
  const updatedConnection = addWaypoint(connection, midpoint, props.waypointIndex + 1);

  // Update the connection
  diagramStore.updateConnection(updatedConnection);

  // Close the menu
  emit('close');
};

const handleSmoothCorner = () => {
  if (!diagramStore.currentDiagram) return;

  // Get the connection
  const connection = diagramStore.currentDiagram.connections[props.connectionId];
  if (!connection || !connection.waypoints) return;

  // Get the current waypoint
  const currentWaypoint = connection.waypoints[props.waypointIndex];

  // Get the previous point (source or waypoint)
  const previousPoint = props.waypointIndex > 0
    ? connection.waypoints[props.waypointIndex - 1]
    : getConnectionEndpointPosition(connection.source);

  // Get the next point (target or waypoint)
  const nextPoint = props.waypointIndex < connection.waypoints.length - 1
    ? connection.waypoints[props.waypointIndex + 1]
    : getConnectionEndpointPosition(connection.target);

  if (!previousPoint || !nextPoint || !currentWaypoint) return;

  // Calculate the control points for a smooth curve
  const controlPoint1 = {
    x: (previousPoint.x + currentWaypoint.x) / 2,
    y: (previousPoint.y + currentWaypoint.y) / 2
  };

  const controlPoint2 = {
    x: (nextPoint.x + currentWaypoint.x) / 2,
    y: (nextPoint.y + currentWaypoint.y) / 2
  };

  // TODO: Implement smooth corner logic
  // This would require changing the connection type to bezier
  // and setting up the control points

  // Close the menu
  emit('close');
};

// Helper function to get connection endpoint position
const getConnectionEndpointPosition = (endpoint: { symbolInstanceId: string; connectionPointId: string }) => {
  if (!diagramStore.currentDiagram) {
    return { x: 0, y: 0 };
  }

  const symbol = diagramStore.currentDiagram.symbolInstances[endpoint.symbolInstanceId];
  if (!symbol) {
    return { x: 0, y: 0 };
  }

  const definition = getSymbolDefinition(symbol.definitionId);
  if (!definition) {
    return { x: 0, y: 0 };
  }

  const position = getConnectionPointPosition(symbol, definition, endpoint.connectionPointId);
  return position || { x: 0, y: 0 };
};

// Close the menu when clicking outside
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (target && !target.closest('.waypoint-context-menu')) {
    emit('close');
  }
};

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.waypoint-context-menu {
  position: fixed;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 180px;
}

.menu-item {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item i {
  width: 16px;
  text-align: center;
}
</style>
