<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoom Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .test-steps {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .expected-result {
            background: #e6f7ff;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #1890ff;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.pending {
            background: #fff7e6;
            color: #d46b08;
        }
        .status.pass {
            background: #f6ffed;
            color: #52c41a;
        }
        .status.fail {
            background: #fff2f0;
            color: #ff4d4f;
        }
        .keyboard-shortcuts {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .shortcut {
            display: inline-block;
            background: #333;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>PowerFlow Web Designer - Zoom Functionality Test</h1>
        <p>This document outlines the test cases for verifying the zoom functionality in the PowerFlow web designer.</p>
        
        <div class="test-section">
            <div class="test-title">Test 1: Toolbar Zoom Buttons <span class="status pending">PENDING</span></div>
            <div class="test-description">
                Verify that all zoom buttons in the main toolbar work correctly.
            </div>
            <div class="test-steps">
                <strong>Steps:</strong>
                <ol>
                    <li>Open the PowerFlow web designer</li>
                    <li>Add a few symbols to the canvas</li>
                    <li>Click the "Zoom In" button (magnifying glass with +)</li>
                    <li>Click the "Zoom Out" button (magnifying glass with -)</li>
                    <li>Click the "Fit Content" button (fullscreen icon)</li>
                    <li>Click the "Reset View" button (border icon)</li>
                </ol>
            </div>
            <div class="expected-result">
                <strong>Expected Result:</strong>
                <ul>
                    <li>Zoom In: Canvas should zoom in towards the center, increasing scale by 20%</li>
                    <li>Zoom Out: Canvas should zoom out from the center, decreasing scale by 20%</li>
                    <li>Fit Content: All symbols should be visible and centered with appropriate padding</li>
                    <li>Reset View: Canvas should return to 100% zoom at position (0,0)</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">Test 2: Keyboard Shortcuts <span class="status pending">PENDING</span></div>
            <div class="test-description">
                Verify that keyboard shortcuts for zoom functionality work correctly.
            </div>
            <div class="keyboard-shortcuts">
                <strong>Keyboard Shortcuts:</strong><br>
                <span class="shortcut">Ctrl + +</span> Zoom In<br>
                <span class="shortcut">Ctrl + -</span> Zoom Out<br>
                <span class="shortcut">Ctrl + 0</span> Reset View<br>
                <span class="shortcut">Ctrl + 1</span> Fit Content<br>
                <span class="shortcut">Ctrl + 2</span> Zoom to Selection
            </div>
            <div class="test-steps">
                <strong>Steps:</strong>
                <ol>
                    <li>Open the PowerFlow web designer</li>
                    <li>Add symbols to the canvas</li>
                    <li>Test each keyboard shortcut listed above</li>
                    <li>Select some symbols and test Ctrl+2</li>
                </ol>
            </div>
            <div class="expected-result">
                <strong>Expected Result:</strong>
                Same behavior as toolbar buttons, with Ctrl+2 zooming to fit selected elements.
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">Test 3: Zoom Positioning <span class="status pending">PENDING</span></div>
            <div class="test-description">
                Verify that zoom operations maintain proper positioning and center correctly.
            </div>
            <div class="test-steps">
                <strong>Steps:</strong>
                <ol>
                    <li>Add symbols to different areas of the canvas</li>
                    <li>Pan the view to focus on a specific area</li>
                    <li>Use zoom in/out buttons or keyboard shortcuts</li>
                    <li>Observe the zoom behavior</li>
                </ol>
            </div>
            <div class="expected-result">
                <strong>Expected Result:</strong>
                <ul>
                    <li>Zoom should occur towards the center of the visible canvas area</li>
                    <li>No "jumping" or sudden position changes</li>
                    <li>Smooth zoom transitions</li>
                    <li>Zoom limits respected (min: 10%, max: 500%)</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">Test 4: Fit Content Edge Cases <span class="status pending">PENDING</span></div>
            <div class="test-description">
                Test fit content functionality with various scenarios.
            </div>
            <div class="test-steps">
                <strong>Steps:</strong>
                <ol>
                    <li>Test with empty canvas (no symbols)</li>
                    <li>Test with single symbol</li>
                    <li>Test with symbols spread across large area</li>
                    <li>Test with symbols and connections</li>
                    <li>Test with symbols clustered in small area</li>
                </ol>
            </div>
            <div class="expected-result">
                <strong>Expected Result:</strong>
                <ul>
                    <li>Empty canvas: Should reset to default view</li>
                    <li>Single symbol: Should center and zoom appropriately</li>
                    <li>Large spread: Should fit all content with padding</li>
                    <li>With connections: Should include connection bounds</li>
                    <li>Clustered: Should not over-zoom (max 300% for fit content)</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">Test 5: Integration with Other Features <span class="status pending">PENDING</span></div>
            <div class="test-description">
                Verify zoom works correctly with other canvas features.
            </div>
            <div class="test-steps">
                <strong>Steps:</strong>
                <ol>
                    <li>Test zoom while symbols are selected</li>
                    <li>Test zoom during symbol dragging</li>
                    <li>Test zoom with grid enabled/disabled</li>
                    <li>Test zoom with different canvas sizes</li>
                </ol>
            </div>
            <div class="expected-result">
                <strong>Expected Result:</strong>
                <ul>
                    <li>Selection should remain visible and properly scaled</li>
                    <li>Dragging should work at all zoom levels</li>
                    <li>Grid should scale appropriately with zoom</li>
                    <li>Zoom should work regardless of canvas size</li>
                </ul>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 15px; background: #f0f0f0; border-radius: 6px;">
            <h3>Testing Instructions:</h3>
            <ol>
                <li>Start the PowerFlow web designer development server</li>
                <li>Open the application in your browser</li>
                <li>Go through each test case systematically</li>
                <li>Update the status badges above as you complete each test</li>
                <li>Report any issues found during testing</li>
            </ol>
        </div>
    </div>
</body>
</html>
