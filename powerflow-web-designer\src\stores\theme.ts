import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { ConnectionTheme } from '@/types/connection';
import { builtInThemes } from '@/utils/connectionThemes';

// Local storage key for user themes
const USER_THEMES_STORAGE_KEY = 'powerflow-user-themes';

// Load user themes from local storage
const loadUserThemes = (): ConnectionTheme[] => {
  try {
    const storedThemes = localStorage.getItem(USER_THEMES_STORAGE_KEY);
    if (storedThemes) {
      return JSON.parse(storedThemes);
    }
  } catch (error) {
    console.error('Failed to load user themes from local storage:', error);
  }
  return [];
};

// Save user themes to local storage
const saveUserThemes = (themes: ConnectionTheme[]): void => {
  try {
    localStorage.setItem(USER_THEMES_STORAGE_KEY, JSON.stringify(themes));
  } catch (error) {
    console.error('Failed to save user themes to local storage:', error);
  }
};

export const useThemeStore = defineStore('theme', () => {
  // State
  const themes = ref<ConnectionTheme[]>(loadUserThemes());

  // Computed
  const allThemes = computed(() => {
    return [...builtInThemes, ...themes.value];
  });

  // Actions
  const saveTheme = (theme: ConnectionTheme) => {
    // Check if the theme already exists
    const existingIndex = themes.value.findIndex(t => t.id === theme.id);
    
    if (existingIndex >= 0) {
      // Update existing theme
      themes.value[existingIndex] = { ...theme };
    } else {
      // Add new theme
      themes.value.push({ ...theme });
    }
    
    // Save to local storage
    saveUserThemes(themes.value);
  };

  const deleteTheme = (themeId: string) => {
    // Find the theme
    const themeIndex = themes.value.findIndex(t => t.id === themeId);
    
    if (themeIndex >= 0) {
      // Remove the theme
      themes.value.splice(themeIndex, 1);
      
      // Save to local storage
      saveUserThemes(themes.value);
    }
  };

  const setDefaultTheme = (themeId: string) => {
    // Reset default flag for all themes
    themes.value.forEach(theme => {
      theme.isDefault = false;
    });
    
    // Set the new default theme
    const theme = themes.value.find(t => t.id === themeId);
    if (theme) {
      theme.isDefault = true;
      
      // Save to local storage
      saveUserThemes(themes.value);
    }
  };

  return {
    themes,
    allThemes,
    saveTheme,
    deleteTheme,
    setDefaultTheme,
  };
});
