@echo off
echo PowerFlow Web Designer - Hierarchical Testing

REM Create results directory if it doesn't exist
mkdir ..\..\test-results 2>nul

echo.
echo === Level 1: Basic Tests ===
npx playwright test tests/e2e/basic --config=playwright.config.ts

REM Check if Level 1 tests passed
if %ERRORLEVEL% NEQ 0 (
  echo.
  echo Level 1 tests failed. Stopping test execution.
  exit /b %ERRORLEVEL%
)

echo.
echo === Level 2: Core Functionality Tests ===
npx playwright test tests/e2e/core --config=playwright.config.ts

REM Check if Level 2 tests passed
if %ERRORLEVEL% NEQ 0 (
  echo.
  echo Level 2 tests failed. Stopping test execution.
  exit /b %ERRORLEVEL%
)

echo.
echo === Level 3: Advanced Feature Tests ===
npx playwright test tests/e2e/advanced --config=playwright.config.ts

REM Check if Level 3 tests passed
if %ERRORLEVEL% NEQ 0 (
  echo.
  echo Level 3 tests failed. Stopping test execution.
  exit /b %ERRORLEVEL%
)

echo.
echo === Level 4: Special Tests ===
npx playwright test tests/e2e/special --config=playwright.config.ts

echo.
echo All tests completed!
