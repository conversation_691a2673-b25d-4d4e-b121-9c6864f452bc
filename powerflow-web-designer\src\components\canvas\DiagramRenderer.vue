<template>
  <div class="diagram-renderer" @contextmenu="handleCanvasContextMenu">
    <diagram-canvas
      ref="canvas"
      :text-tool-active="props.textToolActive"
      @mounted="handleCanvasMounted"
      @clear-selection="handleClearSelection"
      @select-in-rectangle="handleSelectInRectangle"
      @place-text="handlePlaceText"
    >
      <!-- Render groups first (behind symbols) -->
      <template #groups>
        <group-instance
          v-for="group in visibleGroups"
          :key="group.id"
          :group="group"
          :is-selected="isGroupSelected(group.id)"
          :is-locked="isGroupLocked(group.id)"
          :read-only="!!props.readOnly"
          @select="selectGroup"
          @move="moveGroup"
          @resize="resizeGroup"
          @contextmenu.stop="(e) => handleGroupContextMenu(e, group.id)"
        />
      </template>

      <template #symbols>
        <symbol-instance
          v-for="instance in visibleSymbols"
          :key="instance.id"
          :instance="instance"
          :is-selected="isSymbolSelected(instance.id)"
          :is-locked="isSymbolLocked(instance.id)"
          :read-only="!!props.readOnly"
          :highlighted-connection-point-id="
            highlightedTargetSymbolId === instance.id ? highlightedTargetPointId : null
          "
          @select="selectSymbol"
          @deselect="deselectSymbol"
          @move="moveSymbol"
          @move-end="handleMoveEnd"
          @move-multi-element="handleMoveMultiElement"
          @connection-start="(symbolId, pointId) => startConnection(String(symbolId), pointId)"
          @connection-end="(symbolId, pointId) => endConnection(String(symbolId), pointId)"
          @contextmenu.stop="(e) => handleSymbolContextMenu(e, instance.id)"
        />
      </template>

      <template #text-elements>
        <text-element-instance
          v-for="textElement in visibleTextElements"
          :key="textElement.id"
          :text-element="textElement"
          :is-selected="isTextElementSelected(textElement.id)"
          :read-only="!!props.readOnly"
          @select="selectTextElement"
          @move="moveTextElement"
          @resize="resizeTextElement"
          @edit="editTextElement"
          @contextmenu.stop="(e) => handleTextElementContextMenu(e, textElement.id)"
        />
      </template>

      <template #connections>
        <connection-line
          v-for="connection in validConnections"
          :key="connection.id"
          :connection="connection"
          :source-position="getConnectionEndpointPosition(connection.source)"
          :target-position="getConnectionEndpointPosition(connection.target)"
          :is-selected="isConnectionSelected(connection.id)"
          :is-locked="isConnectionLocked(connection.id)"
          :read-only="!!props.readOnly"
          :show-success-feedback="showSuccessFeedback && successConnectionId === connection.id"
          @select="selectConnection"
          @deselect="deselectConnection"
          @waypoint-move="moveConnectionWaypoint"
          @contextmenu.stop="(e) => handleConnectionContextMenu(e, connection.id)"
        />
      </template>

      <!-- Temporary connection line when creating a new connection -->
      <template #temp-connection>
        <!-- 连接线阴影效果 -->
        <v-line
          v-if="tempConnection.active"
          :config="{
            points: [
              tempConnection.startX,
              tempConnection.startY,
              tempConnection.endX,
              tempConnection.endY
            ],
            stroke: 'rgba(24, 144, 255, 0.3)',
            strokeWidth: 8,
            lineCap: 'round',
            lineJoin: 'round',
          }"
        />
        <!-- 主连接线 -->
        <v-line
          v-if="tempConnection.active"
          :config="{
            points: [
              tempConnection.startX,
              tempConnection.startY,
              tempConnection.endX,
              tempConnection.endY
            ],
            stroke: '#1890ff',
            strokeWidth: 2.5,
            dash: [10, 5],
            lineCap: 'round',
            lineJoin: 'round',
            shadowColor: 'rgba(24, 144, 255, 0.5)',
            shadowBlur: 4,
            shadowOffset: { x: 0, y: 0 },
            shadowOpacity: 0.5,
          }"
        />
        <!-- 动画效果线 -->
        <v-line
          v-if="tempConnection.active"
          :config="{
            points: [
              tempConnection.startX,
              tempConnection.startY,
              tempConnection.endX,
              tempConnection.endY
            ],
            stroke: '#1890ff',
            strokeWidth: 1.5,
            dash: [5, 10],
            lineCap: 'round',
            lineJoin: 'round',
            dashOffset: animationOffset,
          }"
        />
        <!-- 起点指示器（固定在起始连接点位置） -->
        <v-circle
          v-if="tempConnection.active"
          :config="{
            x: tempConnection.startX,
            y: tempConnection.startY,
            radius: 8,
            fill: '#1890ff',
            stroke: '#096dd9',
            strokeWidth: 2,
            shadowColor: 'rgba(24, 144, 255, 0.5)',
            shadowBlur: 10,
            shadowOffset: { x: 0, y: 0 },
            shadowOpacity: 0.5,
          }"
        />
        <!-- 终点指示器（跟随鼠标移动，但样式不同于连接点） -->
        <v-circle
          v-if="tempConnection.active"
          :config="{
            x: tempConnection.endX,
            y: tempConnection.endY,
            radius: 5,
            fill: 'rgba(255, 255, 255, 0.8)',
            stroke: '#1890ff',
            strokeWidth: 1.5,
            dash: [2, 2],
          }"
        />
      </template>

      <template #guides>
        <alignment-guides
          :active-symbol-id="activeSymbolId"
          :show-guides="showAlignmentGuides"
          :threshold="10"
        />
      </template>
    </diagram-canvas>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { SymbolInstance as SymbolInstanceType, getConnectionPointPosition } from '@/types/symbolInstance';
import { Connection, ConnectionType, ConnectionLineType, createConnection, defaultRoutingOptions } from '@/types/connection';
import { Position } from '@/types/symbol';
import { TextElement } from '@/types/textElement';
import { DiagramGroup } from '@/types/diagram';
import { AlignmentType, DistributionType } from '@/utils/alignment';
import {
  getBusbarConnectionPoints,
  getBusbarConnectionPointPosition,
  isBusbarSymbol
} from '@/utils/busbarUtils';
import DiagramCanvas from './DiagramCanvas.vue';
import SymbolInstance from './SymbolInstance.vue';
import TextElementInstance from './TextElementInstance.vue';
import ConnectionLine from './ConnectionLine.vue';
import GroupInstance from './GroupInstance.vue';
import AlignmentGuides from './AlignmentGuides.vue';

// Props
const props = defineProps<{
  readOnly?: boolean;
  textToolActive?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'contextMenu', event: MouseEvent, type: 'symbol' | 'connection' | 'canvas' | 'group' | 'text-element', id?: string): void;
  (e: 'canvasMounted', canvas: any): void;
  (e: 'edit-text-element', id: string): void;
  (e: 'text-element-contextmenu', event: MouseEvent, id: string): void;
  (e: 'place-text', position: { x: number; y: number }): void;
}>();

// Store
const diagramStore = useDiagramStore();

// Refs
const canvas = ref(null);

// Expose canvas reference to parent
defineExpose({
  canvas
});

// Handle canvas mounted event
const handleCanvasMounted = () => {
  if (canvas.value) {
    emit('canvasMounted', canvas.value);
  }
};

// Handle text placement
const handlePlaceText = (position: { x: number; y: number }) => {
  emit('place-text', position);
};

// Handle clear selection
const handleClearSelection = () => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  diagramStore.currentDiagram.selectedSymbolIds = [];
  diagramStore.currentDiagram.selectedConnectionIds = [];
  diagramStore.currentDiagram.selectedTextElementIds = [];
  diagramStore.currentDiagram.selectedGroupIds = [];
  diagramStore.currentDiagram.selectedConnectionId = undefined;
};

// Handle rectangle selection
const handleSelectInRectangle = (rect: { x: number; y: number; width: number; height: number }) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  const selectedSymbolIds: string[] = [];
  const selectedConnectionIds: string[] = [];
  const selectedTextElementIds: string[] = [];
  const selectedGroupIds: string[] = [];

  console.log('Selection rectangle:', rect);

  // Check symbols within the rectangle
  visibleSymbols.value.forEach(symbol => {
    if (isSymbolLocked(symbol.id)) return; // Skip locked symbols

    // Get symbol definition for dimensions
    const definition = diagramStore.getSymbolDefinition(symbol.definitionId);
    if (!definition) return;

    // Calculate symbol bounds considering scale and rotation
    const symbolBounds = {
      x: symbol.position.x,
      y: symbol.position.y,
      width: definition.dimensions.width * (symbol.scale || 1),
      height: definition.dimensions.height * (symbol.scale || 1)
    };

    console.log(`Checking symbol ${symbol.id}:`, symbolBounds);

    // Check if symbol intersects with selection rectangle
    // Use a more inclusive intersection check - if any part of the symbol is within the rectangle
    if (isRectangleIntersection(rect, symbolBounds)) {
      selectedSymbolIds.push(symbol.id);
      console.log(`Symbol ${symbol.id} selected`);
    }
  });

  // Check connections within the rectangle (check if any point of the connection is within the rectangle)
  validConnections.value.forEach(connection => {
    if (isConnectionLocked(connection.id)) return; // Skip locked connections

    const sourcePos = getConnectionEndpointPosition(connection.source);
    const targetPos = getConnectionEndpointPosition(connection.target);

    // Check if either endpoint is within the rectangle or if any waypoint is within the rectangle
    let isConnectionInRect = isPointInRectangle(sourcePos, rect) || isPointInRectangle(targetPos, rect);

    // Also check waypoints if they exist
    if (!isConnectionInRect && connection.waypoints) {
      isConnectionInRect = connection.waypoints.some(waypoint => isPointInRectangle(waypoint, rect));
    }

    if (isConnectionInRect) {
      selectedConnectionIds.push(connection.id);
    }
  });

  // Check text elements within the rectangle
  visibleTextElements.value.forEach(textElement => {
    if (isTextElementLocked(textElement.id)) return; // Skip locked text elements

    // Get text element bounds
    const textBounds = {
      x: textElement.position.x,
      y: textElement.position.y,
      width: textElement.size?.width || 100, // Default width if not specified
      height: textElement.size?.height || 30, // Default height if not specified
    };

    // Check if text element intersects with selection rectangle
    if (isRectangleIntersection(rect, textBounds)) {
      selectedTextElementIds.push(textElement.id);
    }
  });

  // Check groups within the rectangle
  visibleGroups.value.forEach(group => {
    if (isGroupLocked(group.id)) return; // Skip locked groups

    // Check if any symbol in the group is within the rectangle
    const groupSymbolsInRect = group.symbolInstanceIds.some(symbolId => {
      const symbol = diagramStore.currentDiagram?.symbolInstances[symbolId];
      if (!symbol) return false;

      const definition = diagramStore.getSymbolDefinition(symbol.definitionId);
      if (!definition) return false;

      const symbolBounds = {
        x: symbol.position.x,
        y: symbol.position.y,
        width: definition.dimensions.width * symbol.scale,
        height: definition.dimensions.height * symbol.scale
      };

      return isRectangleIntersection(rect, symbolBounds);
    });

    if (groupSymbolsInRect) {
      selectedGroupIds.push(group.id);
    }
  });

  // Update selection based on what was found
  if (selectedSymbolIds.length > 0 || selectedConnectionIds.length > 0 || selectedTextElementIds.length > 0 || selectedGroupIds.length > 0) {
    diagramStore.currentDiagram.selectedSymbolIds = selectedSymbolIds;
    diagramStore.currentDiagram.selectedConnectionIds = selectedConnectionIds;
    diagramStore.currentDiagram.selectedTextElementIds = selectedTextElementIds;
    diagramStore.currentDiagram.selectedGroupIds = selectedGroupIds;
    diagramStore.currentDiagram.selectedConnectionId = selectedConnectionIds[0] || undefined;
  }
};

// Helper function to check if two rectangles intersect
const isRectangleIntersection = (rect1: { x: number; y: number; width: number; height: number }, rect2: { x: number; y: number; width: number; height: number }): boolean => {
  return !(rect1.x + rect1.width < rect2.x ||
           rect2.x + rect2.width < rect1.x ||
           rect1.y + rect1.height < rect2.y ||
           rect2.y + rect2.height < rect1.y);
};

// Helper function to check if a point is within a rectangle
const isPointInRectangle = (point: Position, rect: { x: number; y: number; width: number; height: number }): boolean => {
  return point.x >= rect.x &&
         point.x <= rect.x + rect.width &&
         point.y >= rect.y &&
         point.y <= rect.y + rect.height;
};
const tempConnection = ref({
  active: false,
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
  sourceSymbolId: '',
  sourcePointId: '',
});

// 目标连接点高亮
const highlightedTargetSymbolId = ref<string | null>(null);
const highlightedTargetPointId = ref<string | null>(null);

// 连接线动画效果
const animationOffset = ref(0);
const animationInterval = ref<number | null>(null);

// 连接成功反馈
const successConnectionId = ref<string | null>(null);
const showSuccessFeedback = ref(false);

// Alignment guide state
const activeSymbolId = ref<string | undefined>(undefined);
const showAlignmentGuides = computed(() => diagramStore.alignmentGuidesVisible);

// Computed
// PERFORMANCE OPTIMIZATION: Use shallowRef to prevent deep reactivity on symbol arrays
const visibleSymbols = computed(() => {
  if (!diagramStore.currentDiagram) return [];

  const symbols: SymbolInstanceType[] = [];
  // Process layers in reverse order to respect z-index (bottom to top)
  const layers = [...diagramStore.currentDiagram.layers].reverse();

  // Get all visible symbols from all visible layers
  layers.forEach(layer => {
    if (layer.visible) {
      layer.symbolInstanceIds.forEach(id => {
        const symbol = diagramStore.currentDiagram?.symbolInstances[id];
        if (symbol) {
          // Add a layer property to the symbol for reference
          const symbolWithLayer = {
            ...symbol,
            _layerId: layer.id,
            _layerLocked: layer.locked
          };
          symbols.push(symbolWithLayer);
        }
      });
    }
  });

  return symbols;
});

const visibleConnections = computed(() => {
  if (!diagramStore.currentDiagram) return [];

  const connections: Connection[] = [];
  // Process layers in reverse order to respect z-index (bottom to top)
  const layers = [...diagramStore.currentDiagram.layers].reverse();

  // Get all visible connections from all visible layers
  layers.forEach(layer => {
    if (layer.visible) {
      layer.connectionIds.forEach(id => {
        const connection = diagramStore.currentDiagram?.connections[id];
        if (connection) {
          // Add a layer property to the connection for reference
          const connectionWithLayer = {
            ...connection,
            _layerId: layer.id,
            _layerLocked: layer.locked
          };
          connections.push(connectionWithLayer);
        }
      });
    }
  });

  return connections;
});

const visibleGroups = computed(() => {
  if (!diagramStore.currentDiagram) return [];

  // Get all groups
  const groups: DiagramGroup[] = Object.values(diagramStore.currentDiagram.groups);

  // Filter groups to only include those with visible symbols
  return groups.filter(group => {
    // Check if any symbol in the group is visible
    return group.symbolInstanceIds.some(symbolId => {
      return visibleSymbols.value.some(symbol => symbol.id === symbolId);
    });
  });
});

const visibleTextElements = computed(() => {
  if (!diagramStore.currentDiagram) return [];

  const textElements: TextElement[] = [];
  // Process layers in reverse order to respect z-index (bottom to top)
  const layers = [...diagramStore.currentDiagram.layers].reverse();

  // Get all visible text elements from all visible layers
  layers.forEach(layer => {
    if (layer.visible && layer.textElementIds) {
      layer.textElementIds.forEach(id => {
        const textElement = diagramStore.currentDiagram?.textElements[id];
        if (textElement) {
          // Add a layer property to the text element for reference
          const textElementWithLayer = {
            ...textElement,
            _layerId: layer.id,
            _layerLocked: layer.locked
          };
          textElements.push(textElementWithLayer);
        }
      });
    }
  });

  return textElements;
});

// Filter connections to only include those with valid source and target
const validConnections = computed(() => {
  return visibleConnections.value.filter(connection => {
    // Check if connection has valid source and target
    if (!connection.source || !connection.target) {
      console.warn(`Connection ${connection.id} has missing source or target`);
      return false;
    }

    // Check if source and target have valid symbolInstanceId and connectionPointId
    if (!connection.source.symbolInstanceId || !connection.source.connectionPointId ||
        !connection.target.symbolInstanceId || !connection.target.connectionPointId) {
      console.warn(`Connection ${connection.id} has invalid source or target properties`);
      return false;
    }

    // Check if source and target symbols exist in the diagram
    if (!diagramStore.currentDiagram) return false;

    const sourceSymbol = diagramStore.currentDiagram.symbolInstances[connection.source.symbolInstanceId];
    const targetSymbol = diagramStore.currentDiagram.symbolInstances[connection.target.symbolInstanceId];

    if (!sourceSymbol || !targetSymbol) {
      console.warn(`Connection ${connection.id} references non-existent symbols`);
      return false;
    }

    return true;
  });
});

// Methods

const isSymbolSelected = (id: string) => {
  return diagramStore.currentDiagram?.selectedSymbolIds.includes(id) || false;
};

const isSymbolLocked = (id: string) => {
  if (!diagramStore.currentDiagram) return false;

  // Find the symbol in the visible symbols
  const symbol = visibleSymbols.value.find(s => s.id === id);

  // Check if the symbol's layer is locked
  return symbol?._layerLocked || false;
};

const isConnectionSelected = (id: string) => {
  return diagramStore.currentDiagram?.selectedConnectionIds.includes(id) || false;
};

const isConnectionLocked = (id: string) => {
  if (!diagramStore.currentDiagram) return false;

  // Find the connection in the visible connections
  const connection = visibleConnections.value.find(c => c.id === id);

  // Check if the connection's layer is locked
  return connection?._layerLocked || false;
};

const isGroupSelected = (id: string) => {
  return diagramStore.currentDiagram?.selectedGroupIds.includes(id) || false;
};

const isGroupLocked = (id: string) => {
  if (!diagramStore.currentDiagram) return false;

  // Check if the group is locked
  return diagramStore.currentDiagram.groups[id]?.locked || false;
};

const isTextElementSelected = (id: string) => {
  return diagramStore.currentDiagram?.selectedTextElementIds.includes(id) || false;
};

const isTextElementLocked = (id: string) => {
  if (!diagramStore.currentDiagram) return false;

  // Find the text element in the visible text elements
  const textElement = visibleTextElements.value.find(t => t.id === id);

  // Check if the text element's layer is locked
  return textElement?._layerLocked || false;
};

const selectTextElement = (id: string, event?: MouseEvent) => {
  try {
    if (!diagramStore.currentDiagram || props.readOnly) return;

    // Verify the text element exists
    if (!diagramStore.currentDiagram.textElements[id]) {
      console.warn(`Cannot select text element with ID ${id}: Text element not found in diagram`);
      return;
    }

    // Check if the text element is in a locked layer
    if (isTextElementLocked(id)) return;

    // Check if Ctrl key is pressed for multi-select
    const isMultiSelect = event?.ctrlKey || isCtrlPressed.value;

    if (!isMultiSelect) {
      // Clear previous selection
      diagramStore.currentDiagram.selectedSymbolIds = [];
      diagramStore.currentDiagram.selectedConnectionIds = [];
      diagramStore.currentDiagram.selectedTextElementIds = [id];
      diagramStore.currentDiagram.selectedGroupIds = [];
      diagramStore.currentDiagram.selectedConnectionId = undefined;
    } else {
      // Toggle selection if Ctrl is pressed
      const currentIndex = diagramStore.currentDiagram.selectedTextElementIds.indexOf(id);
      if (currentIndex !== -1) {
        // Remove from selection if already selected
        diagramStore.currentDiagram.selectedTextElementIds.splice(currentIndex, 1);
      } else {
        // Add to selection if not already selected
        diagramStore.currentDiagram.selectedTextElementIds.push(id);
      }

      // Clear other selections when multi-selecting text elements
      diagramStore.currentDiagram.selectedSymbolIds = [];
      diagramStore.currentDiagram.selectedConnectionIds = [];
      diagramStore.currentDiagram.selectedGroupIds = [];
      diagramStore.currentDiagram.selectedConnectionId = undefined;
    }
  } catch (error) {
    console.error('Error selecting text element:', error);
  }
};

const moveTextElement = (id: string, position: { x: number; y: number }) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the text element is in a locked layer
  if (isTextElementLocked(id)) return;

  const textElement = diagramStore.currentDiagram.textElements[id];
  if (textElement) {
    // Update the text element position
    textElement.position = position;
    diagramStore.modified = true;
  }
};

const resizeTextElement = (id: string, size: { width: number; height: number }) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the text element is in a locked layer
  if (isTextElementLocked(id)) return;

  const textElement = diagramStore.currentDiagram.textElements[id];
  if (textElement) {
    // Update the text element size
    textElement.size = size;
    diagramStore.modified = true;
  }
};

const editTextElement = (id: string) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the text element is in a locked layer
  if (isTextElementLocked(id)) return;

  // Emit edit event to parent component
  emit('edit-text-element', id);
};

const handleTextElementContextMenu = (event: MouseEvent, id: string) => {
  event.preventDefault();
  if (props.readOnly) return;

  // Select the text element if not already selected
  if (!isTextElementSelected(id)) {
    selectTextElement(id);
  }

  // Emit context menu event to parent component
  emit('text-element-contextmenu', event, id);
};

// Track keyboard state for multi-selection
const isCtrlPressed = ref(false);

const selectSymbol = (id: string, event?: MouseEvent) => {
  try {
    if (!diagramStore.currentDiagram || props.readOnly) return;

    // Verify the symbol exists
    if (!diagramStore.currentDiagram.symbolInstances[id]) {
      console.warn(`Cannot select symbol with ID ${id}: Symbol not found in diagram`);
      return;
    }

    // Check if the symbol is in a locked layer
    if (isSymbolLocked(id)) return;

    // Check if Ctrl key is pressed for multi-select
    const isMultiSelect = event?.ctrlKey || isCtrlPressed.value;

    if (!isMultiSelect) {
      // Clear previous selection
      diagramStore.currentDiagram.selectedSymbolIds = [id];
      diagramStore.currentDiagram.selectedConnectionIds = [];
      diagramStore.currentDiagram.selectedTextElementIds = [];
      diagramStore.currentDiagram.selectedGroupIds = [];
      diagramStore.currentDiagram.selectedConnectionId = undefined;
    } else {
      // Toggle selection if Ctrl is pressed
      const currentIndex = diagramStore.currentDiagram.selectedSymbolIds.indexOf(id);
      if (currentIndex !== -1) {
        // Remove from selection if already selected
        diagramStore.currentDiagram.selectedSymbolIds.splice(currentIndex, 1);
      } else {
        // Add to selection if not already selected
        diagramStore.currentDiagram.selectedSymbolIds.push(id);
      }

      // Clear other selections when multi-selecting symbols
      diagramStore.currentDiagram.selectedConnectionIds = [];
      diagramStore.currentDiagram.selectedTextElementIds = [];
      diagramStore.currentDiagram.selectedGroupIds = [];
      diagramStore.currentDiagram.selectedConnectionId = undefined;
    }
  } catch (error) {
    console.error('Error selecting symbol:', error);
  }
};

const deselectSymbol = (id: string) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Remove from selection
  const index = diagramStore.currentDiagram.selectedSymbolIds.indexOf(id);
  if (index !== -1) {
    diagramStore.currentDiagram.selectedSymbolIds.splice(index, 1);
  }
};

const moveSymbol = (id: string, x: number, y: number) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the symbol is in a locked layer
  if (isSymbolLocked(id)) return;

  // Set the active symbol for alignment guides
  activeSymbolId.value = id;

  const symbol = diagramStore.currentDiagram.symbolInstances[id];
  if (symbol) {
    // Update the symbol position
    symbol.position = { x, y };
    diagramStore.modified = true;

    // Trigger connection line updates for dynamic following during drag
    // This ensures connection lines update in real-time as symbols move
    triggerConnectionUpdates(id);
  }
};

// Helper function to trigger connection line updates when symbols move
const triggerConnectionUpdates = (symbolId: string) => {
  if (!diagramStore.currentDiagram) return;

  // Find all connections that involve this symbol
  const connections = Object.values(diagramStore.currentDiagram.connections);
  const affectedConnections = connections.filter(connection =>
    connection.source.symbolInstanceId === symbolId ||
    connection.target.symbolInstanceId === symbolId
  );

  // Force re-computation of connection endpoints by updating the diagram store
  // This will trigger reactive updates in ConnectionLine components
  if (affectedConnections.length > 0) {
    // Use nextTick to ensure the symbol position update is processed first
    nextTick(() => {
      // Trigger a minimal update to force connection line re-rendering
      diagramStore.modified = true;
    });
  }
};

const handleMoveMultiElement = (type: string, id: string, x: number, y: number, index?: number) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  switch (type) {
    case 'symbol':
      // Check if the symbol is in a locked layer
      if (isSymbolLocked(id)) return;

      const symbol = diagramStore.currentDiagram.symbolInstances[id];
      if (symbol) {
        symbol.position = { x, y };
        diagramStore.modified = true;
      }
      break;

    case 'connection-waypoint':
      // Check if the connection is in a locked layer
      if (isConnectionLocked(id)) return;

      const connection = diagramStore.currentDiagram.connections[id];
      if (connection && connection.waypoints && index !== undefined) {
        connection.waypoints[index] = { x, y };
        diagramStore.modified = true;
      }
      break;

    case 'group':
      // Check if the group is locked
      if (isGroupLocked(id)) return;

      const group = diagramStore.currentDiagram.groups[id];
      if (group) {
        group.position = { x, y };
        diagramStore.modified = true;
      }
      break;

    default:
      console.warn(`Unknown multi-element type: ${type}`);
  }
};

const handleMoveEnd = (id: string) => {
  // Clear the active symbol ID to hide alignment guides
  activeSymbolId.value = undefined;
};

// Zoom and pan methods
const zoomIn = () => {
  if (!diagramStore.currentDiagram || !canvas.value) return;

  const currentScale = diagramStore.currentDiagram.viewport.scale;
  const newScale = Math.min(currentScale * 1.2, 5);

  // Get the stage center point
  const stage = canvas.value.stage.getNode();
  const centerX = stage.width() / 2;
  const centerY = stage.height() / 2;

  // Calculate the new position
  const mousePointTo = {
    x: (centerX - stage.x()) / currentScale,
    y: (centerY - stage.y()) / currentScale,
  };

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    position: {
      x: centerX / newScale - mousePointTo.x,
      y: centerY / newScale - mousePointTo.y,
    },
    scale: newScale,
  };
};

const zoomOut = () => {
  if (!diagramStore.currentDiagram || !canvas.value) return;

  const currentScale = diagramStore.currentDiagram.viewport.scale;
  const newScale = Math.max(currentScale / 1.2, 0.1);

  // Get the stage center point
  const stage = canvas.value.stage.getNode();
  const centerX = stage.width() / 2;
  const centerY = stage.height() / 2;

  // Calculate the new position
  const mousePointTo = {
    x: (centerX - stage.x()) / currentScale,
    y: (centerY - stage.y()) / currentScale,
  };

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    position: {
      x: centerX / newScale - mousePointTo.x,
      y: centerY / newScale - mousePointTo.y,
    },
    scale: newScale,
  };
};

const resetView = () => {
  if (!diagramStore.currentDiagram) return;

  // Reset to default view
  diagramStore.currentDiagram.viewport = {
    position: { x: 0, y: 0 },
    scale: 1,
  };
};

const fitContent = () => {
  if (!diagramStore.currentDiagram || !canvas.value) return;

  const stage = canvas.value.stage.getNode();
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Calculate the bounds of all symbols
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  // Check if there are any symbols
  const symbols = Object.values(diagramStore.currentDiagram.symbolInstances) as SymbolInstanceType[];
  if (symbols.length === 0) {
    resetView();
    return;
  }

  // Calculate the bounds
  symbols.forEach(symbol => {
    const x = symbol.position.x;
    const y = symbol.position.y;
    const width = symbol.dimensions?.width || 100;
    const height = symbol.dimensions?.height || 100;

    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + width);
    maxY = Math.max(maxY, y + height);
  });

  // Include connections in the bounds calculation
  const connections = Object.values(diagramStore.currentDiagram.connections);
  connections.forEach(connection => {
    if (connection.waypoints) {
      connection.waypoints.forEach(waypoint => {
        minX = Math.min(minX, waypoint.x);
        minY = Math.min(minY, waypoint.y);
        maxX = Math.max(maxX, waypoint.x);
        maxY = Math.max(maxY, waypoint.y);
      });
    }
  });

  // Add padding
  const padding = 50;
  minX -= padding;
  minY -= padding;
  maxX += padding;
  maxY += padding;

  // Calculate the content dimensions
  const contentWidth = maxX - minX;
  const contentHeight = maxY - minY;

  // Calculate the scale to fit
  const scaleX = stageWidth / contentWidth;
  const scaleY = stageHeight / contentHeight;
  const scale = Math.min(scaleX, scaleY, 5);

  // Calculate the center position
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    position: {
      x: centerX - stageWidth / (2 * scale),
      y: centerY - stageHeight / (2 * scale),
    },
    scale: scale,
  };
};

const zoomToSelection = () => {
  if (!diagramStore.currentDiagram || !canvas.value) return;

  const stage = canvas.value.stage.getNode();
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Calculate the bounds of selected elements
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  // Check if there are any selected symbols
  const selectedSymbolIds = diagramStore.currentDiagram.selectedSymbolIds;
  if (selectedSymbolIds.length === 0 &&
      diagramStore.currentDiagram.selectedConnectionIds.length === 0 &&
      diagramStore.currentDiagram.selectedGroupIds.length === 0) {
    return;
  }

  // Calculate the bounds of selected symbols
  selectedSymbolIds.forEach(id => {
    const symbol = diagramStore.currentDiagram!.symbolInstances[id];
    if (symbol) {
      const x = symbol.position.x;
      const y = symbol.position.y;
      const width = symbol.dimensions?.width || 100;
      const height = symbol.dimensions?.height || 100;

      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x + width);
      maxY = Math.max(maxY, y + height);
    }
  });

  // Calculate the bounds of selected connections
  const selectedConnectionIds = diagramStore.currentDiagram.selectedConnectionIds;
  selectedConnectionIds.forEach(id => {
    const connection = diagramStore.currentDiagram!.connections[id];
    if (connection && connection.waypoints) {
      connection.waypoints.forEach(waypoint => {
        minX = Math.min(minX, waypoint.x);
        minY = Math.min(minY, waypoint.y);
        maxX = Math.max(maxX, waypoint.x);
        maxY = Math.max(maxY, waypoint.y);
      });
    }
  });

  // Calculate the bounds of selected groups
  const selectedGroupIds = diagramStore.currentDiagram.selectedGroupIds;
  selectedGroupIds.forEach(id => {
    const group = diagramStore.currentDiagram!.groups[id];
    if (group) {
      const x = group.position.x;
      const y = group.position.y;
      const width = group.size.width;
      const height = group.size.height;

      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x + width);
      maxY = Math.max(maxY, y + height);
    }
  });

  // Add padding
  const padding = 50;
  minX -= padding;
  minY -= padding;
  maxX += padding;
  maxY += padding;

  // Calculate the content dimensions
  const contentWidth = maxX - minX;
  const contentHeight = maxY - minY;

  // Calculate the scale to fit
  const scaleX = stageWidth / contentWidth;
  const scaleY = stageHeight / contentHeight;
  const scale = Math.min(scaleX, scaleY, 5);

  // Calculate the center position
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    position: {
      x: centerX - stageWidth / (2 * scale),
      y: centerY - stageHeight / (2 * scale),
    },
    scale: scale,
  };
};

const selectConnection = (id: string) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the connection is in a locked layer
  if (isConnectionLocked(id)) return;

  // Check if Ctrl key is pressed for multi-select
  const isMultiSelect = isCtrlPressed.value;

  if (!isMultiSelect) {
    // Clear previous selection
    diagramStore.currentDiagram.selectedSymbolIds = [];
    diagramStore.currentDiagram.selectedConnectionIds = [id];
    diagramStore.currentDiagram.selectedTextElementIds = [];
    diagramStore.currentDiagram.selectedGroupIds = [];

    // Set the selected connection ID for the style editor
    diagramStore.currentDiagram.selectedConnectionId = id;
  } else {
    // Add to selection if not already selected
    if (!diagramStore.currentDiagram.selectedConnectionIds.includes(id)) {
      diagramStore.currentDiagram.selectedConnectionIds.push(id);

      // Set the selected connection ID for the style editor
      diagramStore.currentDiagram.selectedConnectionId = id;
    }
  }
};

const selectGroup = (id: string) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the group is locked
  if (isGroupLocked(id)) return;

  // Check if Ctrl key is pressed for multi-select
  const isMultiSelect = isCtrlPressed.value;

  if (!isMultiSelect) {
    // Clear previous selection
    diagramStore.currentDiagram.selectedSymbolIds = [];
    diagramStore.currentDiagram.selectedConnectionIds = [];
    diagramStore.currentDiagram.selectedTextElementIds = [];
    diagramStore.currentDiagram.selectedGroupIds = [id];
    diagramStore.currentDiagram.selectedConnectionId = undefined;
  } else {
    // Add to selection if not already selected
    if (!diagramStore.currentDiagram.selectedGroupIds.includes(id)) {
      diagramStore.currentDiagram.selectedGroupIds.push(id);
    }
  }
};

const moveGroup = (id: string, x: number, y: number) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the group is locked
  if (isGroupLocked(id)) return;

  // Update the group position
  diagramStore.updateGroupPosition(id, { x, y });
};

const resizeGroup = (id: string, width: number, height: number) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the group is locked
  if (isGroupLocked(id)) return;

  // Update the group size
  diagramStore.updateGroupSize(id, { width, height });
};

const deselectConnection = (id: string) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Remove from selection
  const index = diagramStore.currentDiagram.selectedConnectionIds.indexOf(id);
  if (index !== -1) {
    diagramStore.currentDiagram.selectedConnectionIds.splice(index, 1);

    // Clear the selected connection ID if it matches
    if (diagramStore.currentDiagram.selectedConnectionId === id) {
      diagramStore.currentDiagram.selectedConnectionId = undefined;
    }
  }
};

const moveConnectionWaypoint = (connectionId: string, waypointIndex: number, position: Position) => {
  if (!diagramStore.currentDiagram || props.readOnly) return;

  // Check if the connection is in a locked layer
  if (isConnectionLocked(connectionId)) return;

  const connection = diagramStore.currentDiagram.connections[connectionId];
  if (connection) {
    if (connection.lineType === ConnectionLineType.POLYLINE && connection.waypoints) {
      // Update the waypoint position
      connection.waypoints[waypointIndex] = position;
    } else if (connection.lineType === ConnectionLineType.BEZIER && connection.controlPoints) {
      // Update the control point position
      connection.controlPoints[waypointIndex] = position;
    }

    diagramStore.modified = true;
  }
};

const getConnectionEndpointPosition = (endpoint: { symbolInstanceId: string; connectionPointId: string } | undefined) => {
  // Default position if anything is missing
  const defaultPosition = { x: 0, y: 0 };

  // Check if endpoint is defined
  if (!endpoint) {
    console.warn('Connection endpoint is undefined');
    return defaultPosition;
  }

  // Check if endpoint has required properties
  if (!endpoint.symbolInstanceId || !endpoint.connectionPointId) {
    console.warn('Connection endpoint is missing required properties', endpoint);
    return defaultPosition;
  }

  // Check if diagram exists
  if (!diagramStore.currentDiagram) {
    console.warn('Current diagram is undefined');
    return defaultPosition;
  }

  // Check if symbol exists
  const symbol = diagramStore.currentDiagram.symbolInstances[endpoint.symbolInstanceId];
  if (!symbol) {
    console.warn(`Symbol with ID ${endpoint.symbolInstanceId} not found`);
    return defaultPosition;
  }

  // Check if symbol definition exists
  const definition = diagramStore.getSymbolDefinition(symbol.definitionId);
  if (!definition) {
    console.warn(`Symbol definition with ID ${symbol.definitionId} not found`);
    return defaultPosition;
  }

  // Get connection point position (use busbar-specific function for busbar symbols)
  const position = isBusbarSymbol(definition)
    ? getBusbarConnectionPointPosition(symbol, definition, endpoint.connectionPointId)
    : getConnectionPointPosition(symbol, definition, endpoint.connectionPointId);

  if (!position) {
    console.warn(`Failed to get position for connection point ${endpoint.connectionPointId} on symbol ${endpoint.symbolInstanceId}`);
    return defaultPosition;
  }

  return position;
};

const startConnection = (symbolId: string, pointId: string) => {
  // 首先确保结束任何现有的临时连接
  if (tempConnection.value.active) {
    console.log('Ending previous temporary connection before starting a new one');
    endTempConnection();
  }

  console.log('Start connection:', symbolId, pointId);

  if (!diagramStore.currentDiagram || props.readOnly) {
    console.warn('Cannot start connection: diagram not loaded or read-only mode');
    return;
  }

  // Check if the symbol is in a locked layer
  if (isSymbolLocked(symbolId)) {
    console.warn('Cannot start connection: symbol is locked');
    return;
  }

  const symbol = diagramStore.currentDiagram.symbolInstances[symbolId];
  if (!symbol) {
    console.warn('Cannot start connection: symbol not found', symbolId);
    return;
  }

  const definition = diagramStore.getSymbolDefinition(symbol.definitionId);
  if (!definition) {
    console.warn('Cannot start connection: symbol definition not found', symbol.definitionId);
    return;
  }

  // 获取连接点位置（对busbar符号使用专用函数）
  const pointPosition = isBusbarSymbol(definition)
    ? getBusbarConnectionPointPosition(symbol, definition, pointId)
    : getConnectionPointPosition(symbol, definition, pointId);

  if (!pointPosition) {
    console.warn('Cannot start connection: connection point position not found', pointId);
    return;
  }

  console.log('Connection point position:', pointPosition);

  // 确保symbolId是一个有效的字符串
  const validSymbolId = String(symbolId);

  // Start the temporary connection
  tempConnection.value = {
    active: true,
    startX: pointPosition.x,
    startY: pointPosition.y,
    endX: pointPosition.x,
    endY: pointPosition.y,
    sourceSymbolId: validSymbolId,
    sourcePointId: pointId,
  };

  console.log('Temporary connection started:', tempConnection.value);
  console.log('Source symbol ID type:', typeof tempConnection.value.sourceSymbolId);

  // 启动连接线动画
  startConnectionAnimation();

  // 创建一个处理鼠标抬起事件的函数，它将接收鼠标事件
  const handleMouseUp = (e: any) => {
    endTempConnection(e); // 传递鼠标事件
  };

  // Add mouse move event listener to update the temporary connection
  document.addEventListener('mousemove', updateTempConnection);
  document.addEventListener('mouseup', handleMouseUp);
};

const updateTempConnection = (e: any) => {
  if (!tempConnection.value.active) return;

  // Get the mouse position relative to the canvas
  const canvasElement = canvas.value?.$el;
  if (!canvasElement) {
    console.warn('Canvas element not found for updateTempConnection');
    return;
  }

  const rect = canvasElement.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  // Apply viewport transformation if available
  let adjustedX = x;
  let adjustedY = y;

  if (diagramStore.currentDiagram && diagramStore.currentDiagram.viewport) {
    const viewport = diagramStore.currentDiagram.viewport;
    // Convert screen coordinates to diagram coordinates
    adjustedX = x / viewport.scale + viewport.position.x;
    adjustedY = y / viewport.scale + viewport.position.y;
  }

  // 确保坐标是有效的数字
  if (isNaN(adjustedX) || isNaN(adjustedY) || !isFinite(adjustedX) || !isFinite(adjustedY)) {
    console.warn('Invalid coordinates for temporary connection:', adjustedX, adjustedY);
    return;
  }

  // Update the temporary connection end point
  tempConnection.value.endX = adjustedX;
  tempConnection.value.endY = adjustedY;

  // 查找鼠标位置下的符号和连接点
  const target = findSymbolAndConnectionPointAtPosition({ x: adjustedX, y: adjustedY });

  // 如果找到了目标连接点，并且不是源符号，则高亮显示
  if (target && target.symbolId && target.pointId && target.symbolId !== tempConnection.value.sourceSymbolId) {
    highlightedTargetSymbolId.value = target.symbolId;
    highlightedTargetPointId.value = target.pointId;
  } else {
    // 如果没有找到目标连接点，或者是源符号，则清除高亮
    highlightedTargetSymbolId.value = null;
    highlightedTargetPointId.value = null;
  }
};

// 启动连接线动画
const startConnectionAnimation = () => {
  // 停止现有的动画（如果有）
  stopConnectionAnimation();

  // 重置动画偏移量
  animationOffset.value = 0;

  // 启动新的动画
  animationInterval.value = window.setInterval(() => {
    // 更新动画偏移量
    animationOffset.value = (animationOffset.value + 1) % 15;
  }, 50);
};

// 停止连接线动画
const stopConnectionAnimation = () => {
  if (animationInterval.value !== null) {
    window.clearInterval(animationInterval.value);
    animationInterval.value = null;
  }
};

const endTempConnection = (e?: any) => {
  console.log('Ending temporary connection');

  // 保存源符号和连接点ID，以便在重置后仍然可以使用它们
  const sourceSymbolId = tempConnection.value.sourceSymbolId;
  const sourcePointId = tempConnection.value.sourcePointId;
  const wasActive = tempConnection.value.active;

  // 停止连接线动画
  stopConnectionAnimation();

  // Remove event listeners
  document.removeEventListener('mousemove', updateTempConnection);
  document.removeEventListener('mouseup', endTempConnection);

  // 如果有鼠标事件，尝试根据鼠标位置创建连接
  if (e && wasActive && sourceSymbolId) {
    // 获取鼠标位置
    const canvasElement = canvas.value?.$el;
    if (canvasElement) {
      const rect = canvasElement.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // 转换为图表坐标
      let diagramX = x;
      let diagramY = y;

      if (diagramStore.currentDiagram && diagramStore.currentDiagram.viewport) {
        const viewport = diagramStore.currentDiagram.viewport;
        diagramX = x / viewport.scale + viewport.position.x;
        diagramY = y / viewport.scale + viewport.position.y;
      }

      // 查找鼠标位置下的符号和连接点
      const target = findSymbolAndConnectionPointAtPosition({ x: diagramX, y: diagramY });

      if (target && target.symbolId && target.pointId) {
        console.log('Found target at mouse position:', target);

        // 检查是否是不同的符号
        if (target.symbolId !== sourceSymbolId) {
          // 创建连接
          createConnection(
            sourceSymbolId,
            sourcePointId,
            target.symbolId,
            target.pointId
          );
        } else {
          console.warn('Cannot connect to the same symbol');
        }
      } else {
        console.log('No valid target found at mouse position');
      }
    }
  }

  // Reset the temporary connection
  tempConnection.value = {
    active: false,
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
    sourceSymbolId: '',
    sourcePointId: '',
  };

  // 清除高亮的目标连接点
  highlightedTargetSymbolId.value = null;
  highlightedTargetPointId.value = null;

  // 通知所有符号实例清除连接点状态
  // 发送一个自定义事件，通知所有组件连接操作已结束
  document.dispatchEvent(new CustomEvent('connection-operation-end', {
    detail: {
      sourceSymbolId,
      sourcePointId,
      success: successConnectionId.value !== null
    }
  }));

  console.log('Temporary connection reset');
};

// 创建连接的通用方法
const createConnection = (sourceSymbolId: string, sourcePointId: string, targetSymbolId: string, targetPointId: string) => {
  console.log('Creating connection between:', {
    source: { symbolId: sourceSymbolId, pointId: sourcePointId },
    target: { symbolId: targetSymbolId, pointId: targetPointId }
  });

  if (!diagramStore.currentDiagram) {
    console.warn('Cannot create connection: diagram not loaded');
    return null;
  }

  if (props.readOnly) {
    console.warn('Cannot create connection: read-only mode');
    return null;
  }

  // 检查源符号和目标符号是否存在
  if (!diagramStore.currentDiagram.symbolInstances[sourceSymbolId]) {
    console.warn('Cannot create connection: source symbol not found', sourceSymbolId);
    return null;
  }

  if (!diagramStore.currentDiagram.symbolInstances[targetSymbolId]) {
    console.warn('Cannot create connection: target symbol not found', targetSymbolId);
    return null;
  }

  // 检查源符号和目标符号是否被锁定
  if (isSymbolLocked(sourceSymbolId)) {
    console.warn('Cannot create connection: source symbol is locked');
    return null;
  }

  if (isSymbolLocked(targetSymbolId)) {
    console.warn('Cannot create connection: target symbol is locked');
    return null;
  }

  // 检查是否是同一个符号
  if (sourceSymbolId === targetSymbolId) {
    console.warn('Cannot create connection: cannot connect to the same symbol');
    return null;
  }

  // 查找活动图层（第一个未锁定的可见图层）
  const activeLayer = diagramStore.currentDiagram.layers.find(
    layer => layer.visible && !layer.locked
  );

  if (!activeLayer) {
    console.warn('Cannot create connection: no unlocked visible layer available');
    return null;
  }

  try {
    // ENSURE ORTHOGONAL ROUTING: Force orthogonal routing options for new connections
    const orthogonalRoutingOptions = {
      ...defaultRoutingOptions,
      routingStrategy: 'orthogonal',
      preferStraightLines: false,
      snapToGrid: false
    };

    console.log('Creating connection with mandatory orthogonal routing options:', orthogonalRoutingOptions);

    // 创建新连接
    const connectionId = diagramStore.addConnection(
      sourceSymbolId,
      sourcePointId,
      targetSymbolId,
      targetPointId,
      ConnectionType.CABLE,
      ConnectionLineType.SMART, // 使用智能连接类型
      activeLayer.id, // 使用活动图层
      {}, // 样式
      undefined, // 标签
      orthogonalRoutingOptions // 强制使用正交路由选项
    );

    console.log('Connection created with ID:', connectionId, 'using orthogonal routing');

    // 显示连接成功反馈
    successConnectionId.value = connectionId;
    showSuccessFeedback.value = true;

    // 显示成功提示
    console.log('Connection created successfully!');

    // 创建一个临时的成功提示元素
    const successMessage = document.createElement('div');
    successMessage.textContent = '连接成功！';
    successMessage.style.position = 'fixed';
    successMessage.style.top = '20px';
    successMessage.style.left = '50%';
    successMessage.style.transform = 'translateX(-50%)';
    successMessage.style.backgroundColor = '#52c41a';
    successMessage.style.color = 'white';
    successMessage.style.padding = '8px 16px';
    successMessage.style.borderRadius = '4px';
    successMessage.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
    successMessage.style.zIndex = '9999';
    successMessage.style.opacity = '0';
    successMessage.style.transition = 'opacity 0.3s ease-in-out';

    document.body.appendChild(successMessage);

    // 显示提示
    setTimeout(() => {
      successMessage.style.opacity = '1';
    }, 10);

    // 3秒后隐藏反馈和提示
    setTimeout(() => {
      if (successConnectionId.value === connectionId) {
        showSuccessFeedback.value = false;
        successConnectionId.value = null;
      }

      // 淡出并移除提示
      successMessage.style.opacity = '0';
      setTimeout(() => {
        if (document.body.contains(successMessage)) {
          document.body.removeChild(successMessage);
        }
      }, 300);
    }, 3000);

    return connectionId;
  } catch (error) {
    console.error('Error creating connection:', error);
    return null;
  }
};

// 处理连接结束事件（从SymbolInstance组件发出）
const endConnection = (symbolId: string, pointId: string) => {
  // 确保symbolId是一个有效的字符串
  const targetSymbolId = String(symbolId);

  console.log('End connection event received:', targetSymbolId, pointId);

  if (!diagramStore.currentDiagram) {
    console.warn('Cannot end connection: diagram not loaded');
    endTempConnection();
    return;
  }

  if (!tempConnection.value.active) {
    console.warn('Cannot end connection: no active temporary connection');
    return;
  }

  // 检查是否是不同的符号
  const sourceId = String(tempConnection.value.sourceSymbolId);
  if (sourceId === targetSymbolId) {
    console.warn('Cannot end connection from event: cannot connect to the same symbol');
    endTempConnection();
    return;
  }

  // 创建连接
  createConnection(
    sourceId,
    tempConnection.value.sourcePointId,
    targetSymbolId,
    pointId
  );

  // 重置临时连接
  endTempConnection();
};

// Context menu handlers
const handleCanvasContextMenu = (event: MouseEvent) => {
  // Clear selection when clicking on the canvas
  if (diagramStore.currentDiagram && !props.readOnly) {
    diagramStore.currentDiagram.selectedSymbolIds = [];
    diagramStore.currentDiagram.selectedConnectionIds = [];
    diagramStore.currentDiagram.selectedGroupIds = [];
    diagramStore.currentDiagram.selectedConnectionId = undefined;
  }

  // Emit the context menu event
  emit('contextMenu', event, 'canvas');
};

const handleSymbolContextMenu = (event: MouseEvent, id: string) => {
  // Select the symbol if not already selected
  if (!isSymbolSelected(id)) {
    selectSymbol(id);
  }

  // Emit the context menu event
  emit('contextMenu', event, 'symbol', id);
};

const handleConnectionContextMenu = (event: MouseEvent, id: string) => {
  // Select the connection if not already selected
  if (!isConnectionSelected(id)) {
    selectConnection(id);
  }

  // Emit the context menu event
  emit('contextMenu', event, 'connection', id);
};

const handleGroupContextMenu = (event: MouseEvent, id: string) => {
  // Select the group if not already selected
  if (!isGroupSelected(id)) {
    selectGroup(id);
  }

  // Emit the context menu event
  emit('contextMenu', event, 'group', id);
};

// Handle keyboard shortcuts for alignment, distribution, and grid settings
const handleKeyDown = (event: KeyboardEvent) => {
  if (props.readOnly) return;

  // Track Ctrl key state for multi-selection
  if (event.key === 'Control') {
    isCtrlPressed.value = true;
  }

  // Check if Ctrl/Cmd key is pressed
  const ctrlKey = event.ctrlKey || event.metaKey;
  const shiftKey = event.shiftKey;

  // Alignment shortcuts (with Ctrl+Shift)
  if (ctrlKey && shiftKey) {
    // Align Left: Ctrl+Shift+L
    if (event.key === 'l' || event.key === 'L') {
      event.preventDefault();
      diagramStore.alignSymbols(AlignmentType.LEFT);
    }

    // Align Center: Ctrl+Shift+C
    if (event.key === 'c' || event.key === 'C') {
      event.preventDefault();
      diagramStore.alignSymbols(AlignmentType.CENTER);
    }

    // Align Right: Ctrl+Shift+R
    if (event.key === 'r' || event.key === 'R') {
      event.preventDefault();
      diagramStore.alignSymbols(AlignmentType.RIGHT);
    }

    // Align Top: Ctrl+Shift+T
    if (event.key === 't' || event.key === 'T') {
      event.preventDefault();
      diagramStore.alignSymbols(AlignmentType.TOP);
    }

    // Align Middle: Ctrl+Shift+M
    if (event.key === 'm' || event.key === 'M') {
      event.preventDefault();
      diagramStore.alignSymbols(AlignmentType.MIDDLE);
    }

    // Align Bottom: Ctrl+Shift+B
    if (event.key === 'b' || event.key === 'B') {
      event.preventDefault();
      diagramStore.alignSymbols(AlignmentType.BOTTOM);
    }

    // Distribute Horizontally: Ctrl+Shift+H
    if (event.key === 'h' || event.key === 'H') {
      event.preventDefault();
      diagramStore.distributeSymbols(DistributionType.HORIZONTAL);
    }

    // Distribute Vertically: Ctrl+Shift+V
    if (event.key === 'v' || event.key === 'V') {
      event.preventDefault();
      diagramStore.distributeSymbols(DistributionType.VERTICAL);
    }

    // Toggle Snap to Grid: Ctrl+Shift+G
    if (event.key === 'g' || event.key === 'G') {
      event.preventDefault();
      if (diagramStore.currentDiagram) {
        const currentSnapToGrid = diagramStore.currentDiagram.grid.snapToGrid;
        diagramStore.updateGridSettings({ snapToGrid: !currentSnapToGrid });
      }
    }
  }

  // Grid shortcuts (with Ctrl)
  if (ctrlKey && !shiftKey) {
    // Toggle Grid Visibility: Ctrl+G
    if (event.key === 'g' || event.key === 'G') {
      event.preventDefault();
      if (diagramStore.currentDiagram) {
        const currentGridVisible = diagramStore.currentDiagram.grid.visible;
        diagramStore.updateGridSettings({ visible: !currentGridVisible });
      }
    }

    // Decrease Grid Size: Ctrl+[
    if (event.key === '[') {
      event.preventDefault();
      if (diagramStore.currentDiagram) {
        const currentGridSize = diagramStore.currentDiagram.grid.size;
        const newGridSize = Math.max(5, currentGridSize - 5);
        diagramStore.updateGridSettings({ size: newGridSize });
      }
    }

    // Increase Grid Size: Ctrl+]
    if (event.key === ']') {
      event.preventDefault();
      if (diagramStore.currentDiagram) {
        const currentGridSize = diagramStore.currentDiagram.grid.size;
        const newGridSize = Math.min(100, currentGridSize + 5);
        diagramStore.updateGridSettings({ size: newGridSize });
      }
    }

    // Note: Zoom shortcuts are handled by KeyboardShortcuts.vue and Editor.vue
    // to avoid duplicate event handling. Only keeping zoom to selection here
    // as it's specific to the diagram renderer.

    // Zoom to Selection: Ctrl+2
    if (event.key === '2') {
      event.preventDefault();
      zoomToSelection();
    }
  }

  // Pan shortcuts (with arrow keys)
  if (event.key.startsWith('Arrow')) {
    // Only handle arrow keys if no input element is focused
    if (document.activeElement === document.body) {
      event.preventDefault();

      const distance = 50; // pixels
      const scale = diagramStore.currentDiagram?.viewport.scale || 1;
      const adjustedDistance = distance / scale;

      if (!diagramStore.currentDiagram) return;

      const currentPosition = diagramStore.currentDiagram.viewport.position;
      let newPosition = { ...currentPosition };

      switch (event.key) {
        case 'ArrowUp':
          newPosition.y -= adjustedDistance;
          break;
        case 'ArrowDown':
          newPosition.y += adjustedDistance;
          break;
        case 'ArrowLeft':
          newPosition.x -= adjustedDistance;
          break;
        case 'ArrowRight':
          newPosition.x += adjustedDistance;
          break;
      }

      // Update viewport
      diagramStore.currentDiagram.viewport = {
        position: newPosition,
        scale: diagramStore.currentDiagram.viewport.scale,
      };
    }
  }
};

// 确保画面已加载
const ensureDiagramLoaded = () => {
  // 如果没有当前画面，则创建一个新画面
  if (!diagramStore.currentDiagram) {
    console.log('DiagramRenderer: No current diagram, creating a new one...');

    // 创建一个新画面
    diagramStore.createDiagram('新画面');

    // 确保新画面已保存到 localStorage
    diagramStore.saveDiagramsToLocalStorage();
    console.log('DiagramRenderer: New diagram created and saved to localStorage');
  } else {
    console.log('DiagramRenderer: Current diagram already loaded:', diagramStore.currentDiagram.name);
  }

  return !!diagramStore.currentDiagram;
};

// 添加一个方法来检查两个符号ID是否相同
const isSameSymbol = (id1: string, id2: string) => {
  // 打印详细信息以便调试
  console.log('Comparing symbol IDs:', {
    id1,
    id2,
    typeId1: typeof id1,
    typeId2: typeof id2,
    isEqual: id1 === id2,
    isEqualStrict: id1 === id2 && typeof id1 === typeof id2
  });

  // 如果两个ID都是有效的字符串，则直接比较
  if (typeof id1 === 'string' && id1 && typeof id2 === 'string' && id2) {
    return id1 === id2;
  }

  // 如果任一ID无效，则认为它们不同
  return false;
};

// 检查连接点是否被高亮
const isConnectionPointHighlighted = (symbolId: string, pointId: string) => {
  return highlightedTargetSymbolId.value === symbolId &&
         highlightedTargetPointId.value === pointId;
};

// 根据鼠标位置查找符号和连接点
const findSymbolAndConnectionPointAtPosition = (position: { x: number, y: number }) => {
  if (!diagramStore.currentDiagram) return null;

  console.log('Finding symbol at position:', position);

  // 遍历所有可见符号
  for (const symbol of visibleSymbols.value) {
    const definition = diagramStore.getSymbolDefinition(symbol.definitionId);
    if (!definition) continue;

    // 计算符号的边界框
    const symbolBounds = {
      x: symbol.position.x,
      y: symbol.position.y,
      width: definition.dimensions.width * symbol.scale,
      height: definition.dimensions.height * symbol.scale
    };

    // 检查鼠标位置是否在符号边界框内
    if (
      position.x >= symbolBounds.x &&
      position.x <= symbolBounds.x + symbolBounds.width &&
      position.y >= symbolBounds.y &&
      position.y <= symbolBounds.y + symbolBounds.height
    ) {
      console.log('Found symbol:', symbol.id);

      // 获取连接点（对busbar符号使用动态连接点）
      const connectionPoints = isBusbarSymbol(definition)
        ? getBusbarConnectionPoints(symbol, definition)
        : definition.connectionPoints;

      console.log(`Symbol ${symbol.id} has ${connectionPoints.length} connection points (busbar: ${isBusbarSymbol(definition)})`);

      // 遍历符号的连接点
      for (const point of connectionPoints) {
        // 计算连接点的绝对位置（对busbar符号使用专用函数）
        const pointPosition = isBusbarSymbol(definition)
          ? getBusbarConnectionPointPosition(symbol, definition, point.id)
          : getConnectionPointPosition(symbol, definition, point.id);

        if (!pointPosition) continue;

        // 检查鼠标位置是否在连接点附近（使用50像素的容差，与snap distance一致）
        const distance = Math.sqrt(
          Math.pow(position.x - pointPosition.x, 2) +
          Math.pow(position.y - pointPosition.y, 2)
        );

        console.log(`Checking connection point ${point.id} at (${pointPosition.x}, ${pointPosition.y}), distance: ${distance}`);

        if (distance <= 50) { // 使用与snap distance相同的值
          console.log('Found connection point:', point.id);
          return {
            symbolId: symbol.id,
            pointId: point.id,
            position: pointPosition
          };
        }
      }

      // 如果没有找到连接点，但找到了符号，返回符号ID
      return {
        symbolId: symbol.id,
        pointId: null,
        position: null
      };
    }
  }

  // 如果没有找到符号，返回null
  return null;
};

// Handle keyboard key up events
const handleKeyUp = (event: KeyboardEvent) => {
  // Track Ctrl key state for multi-selection
  if (event.key === 'Control') {
    isCtrlPressed.value = false;
  }
};

// Lifecycle hooks
onMounted(() => {
  console.log('DiagramRenderer component mounted');

  // 画面加载现在由Editor.vue统一处理，这里只需要检查
  if (!diagramStore.currentDiagram) {
    console.warn('DiagramRenderer: No current diagram available, waiting for Editor to load it');
  } else {
    console.log('DiagramRenderer: Current diagram available:', diagramStore.currentDiagram.name);
  }

  // Add keyboard event listeners for alignment shortcuts and multi-selection
  window.addEventListener('keydown', handleKeyDown);
  window.addEventListener('keyup', handleKeyUp);
});

onUnmounted(() => {
  console.log('DiagramRenderer component unmounted');

  // 停止连接线动画
  stopConnectionAnimation();

  // 移除事件监听器
  document.removeEventListener('mousemove', updateTempConnection);
  document.removeEventListener('mouseup', endTempConnection);

  // Remove keyboard event listeners
  window.removeEventListener('keydown', handleKeyDown);
  window.removeEventListener('keyup', handleKeyUp);
});
</script>

<style scoped>
.diagram-renderer {
  width: 100%;
  height: 100%;
}
</style>
