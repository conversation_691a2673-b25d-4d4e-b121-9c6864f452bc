<template>
  <div class="trend-chart-editor">
    <div class="section-title">趋势图表</div>

    <div v-if="selectedSymbol">
      <!-- Trend charts list -->
      <div class="trend-charts-list">
        <a-list
          size="small"
          :data-source="trendCharts"
          :locale="{ emptyText: '未配置趋势图表' }"
        >
          <template #renderItem="{ item, index }">
            <a-list-item>
              <div class="chart-item">
                <span>{{ getBindingName(item.bindingId) }}</span>
                <div class="chart-actions">
                  <a-button
                    type="text"
                    size="small"
                    @click="() => editChart(index)"
                  >
                    <template #icon><EditOutlined /></template>
                  </a-button>
                  <a-button
                    type="text"
                    size="small"
                    danger
                    @click="() => removeChart(index)"
                  >
                    <template #icon><DeleteOutlined /></template>
                  </a-button>
                </div>
              </div>
            </a-list-item>
          </template>
        </a-list>
      </div>

      <!-- Add new chart button -->
      <div class="add-chart">
        <a-button
          type="primary"
          size="small"
          @click="addNewChart"
        >
          <template #icon><PlusOutlined /></template>
          添加趋势图表
        </a-button>
      </div>

      <!-- Edit chart modal -->
      <a-modal
        v-model:visible="isEditModalVisible"
        title="编辑趋势图表"
        @ok="saveChart"
        @cancel="cancelEdit"
        :okButtonProps="{ disabled: !isValidChart }"
      >
        <a-form layout="vertical" size="small">
          <!-- Binding selection -->
          <a-form-item label="绑定">
            <a-select
              v-model:value="editingChart.bindingId"
              style="width: 100%"
              placeholder="选择绑定"
              size="small"
            >
              <a-select-option
                v-for="slot in symbolBindingSlots"
                :key="slot.id"
                :value="slot.id"
              >
                {{ slot.name }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <!-- Position -->
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="X位置">
                <a-input-number
                  v-model:value="editingChart.position.x"
                  style="width: 100%"
                  :min="-100"
                  :max="100"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="Y位置">
                <a-input-number
                  v-model:value="editingChart.position.y"
                  style="width: 100%"
                  :min="-100"
                  :max="100"
                  size="small"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Title -->
          <a-form-item label="标题">
            <a-input
              v-model:value="editingChart.title"
              placeholder="图表标题"
              size="small"
            />
          </a-form-item>

          <!-- Size -->
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="宽度">
                <a-input-number
                  v-model:value="editingChart.width"
                  style="width: 100%"
                  :min="50"
                  :max="300"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="高度">
                <a-input-number
                  v-model:value="editingChart.height"
                  style="width: 100%"
                  :min="30"
                  :max="200"
                  size="small"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Colors -->
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="线条颜色">
                <a-input
                  v-model:value="editingChart.lineColor"
                  placeholder="#1890ff"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="填充颜色">
                <a-input
                  v-model:value="editingChart.fillColor"
                  placeholder="rgba(24, 144, 255, 0.2)"
                  size="small"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Background settings -->
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="背景颜色">
                <a-input
                  v-model:value="editingChart.backgroundColor"
                  placeholder="#ffffff"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="背景透明度">
                <a-slider
                  v-model:value="editingChart.backgroundOpacity"
                  :min="0"
                  :max="1"
                  :step="0.1"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Text color -->
          <a-form-item label="文本颜色">
            <a-input
              v-model:value="editingChart.textColor"
              placeholder="#333333"
              size="small"
            />
          </a-form-item>

          <!-- Display options -->
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item>
                <a-checkbox
                  v-model:checked="editingChart.showTitle"
                >
                  显示标题
                </a-checkbox>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item>
                <a-checkbox
                  v-model:checked="editingChart.showCurrentValue"
                >
                  显示当前值
                </a-checkbox>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Value format -->
          <a-row :gutter="8">
            <a-col :span="8">
              <a-form-item label="格式">
                <a-select
                  v-model:value="editingChart.valueFormat"
                  style="width: 100%"
                  placeholder="选择格式"
                  size="small"
                >
                  <a-select-option value="integer">整数</a-select-option>
                  <a-select-option value="decimal">小数</a-select-option>
                  <a-select-option value="percentage">百分比</a-select-option>
                  <a-select-option value="scientific">科学计数</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="精度">
                <a-input-number
                  v-model:value="editingChart.valuePrecision"
                  style="width: 100%"
                  :min="0"
                  :max="10"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="单位">
                <a-input
                  v-model:value="editingChart.valueUnit"
                  placeholder="例如: kW, V, A"
                  size="small"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- Chart options -->
          <a-row :gutter="8">
            <a-col :span="8">
              <a-form-item label="最大点数">
                <a-input-number
                  v-model:value="editingChart.maxPoints"
                  style="width: 100%"
                  :min="10"
                  :max="1000"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="最小值">
                <a-input-number
                  v-model:value="editingChart.minValue"
                  style="width: 100%"
                  size="small"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="最大值">
                <a-input-number
                  v-model:value="editingChart.maxValue"
                  style="width: 100%"
                  size="small"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item>
            <a-checkbox
              v-model:checked="editingChart.autoScale"
            >
              自动缩放
            </a-checkbox>
          </a-form-item>
        </a-form>
      </a-modal>
    </div>

    <div v-else class="no-selection">
      未选择符号
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { SymbolInstance } from '@/types/symbolInstance';
import { BindingSlot, Position } from '@/types/symbol';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';

// Interface for trend chart
interface TrendChart {
  bindingId: string;
  position: Position;
  title?: string;
  width?: number;
  height?: number;
  lineColor?: string;
  fillColor?: string;
  backgroundColor?: string;
  backgroundOpacity?: number;
  textColor?: string;
  showTitle?: boolean;
  showCurrentValue?: boolean;
  valueFormat?: string;
  valuePrecision?: number;
  valueUnit?: string;
  maxPoints?: number;
  minValue?: number;
  maxValue?: number;
  autoScale?: boolean;
}

// Store
const diagramStore = useDiagramStore();

// State
const isEditModalVisible = ref(false);
const editingChartIndex = ref<number | null>(null);
const editingChart = ref<TrendChart>({
  bindingId: '',
  position: { x: 0, y: 0 },
  title: '',
  width: 120,
  height: 60,
  lineColor: '#1890ff',
  fillColor: 'rgba(24, 144, 255, 0.2)',
  backgroundColor: '#ffffff',
  backgroundOpacity: 0.7,
  textColor: '#333333',
  showTitle: true,
  showCurrentValue: true,
  valueFormat: 'decimal',
  valuePrecision: 2,
  valueUnit: '',
  maxPoints: 100,
  minValue: undefined,
  maxValue: undefined,
  autoScale: true
});

// Computed
const selectedSymbol = computed<SymbolInstance | null>(() => {
  if (!diagramStore.currentDiagram) return null;
  if (!diagramStore.currentDiagram.selectedSymbolIds) return null;
  if (diagramStore.currentDiagram.selectedSymbolIds.length === 0) return null;

  const symbolId = diagramStore.currentDiagram.selectedSymbolIds[0];
  return diagramStore.currentDiagram.symbolInstances[symbolId] || null;
});

const symbolBindingSlots = computed<BindingSlot[]>(() => {
  if (!selectedSymbol.value) return [];

  const definition = diagramStore.getSymbolDefinition(selectedSymbol.value.definitionId);
  if (!definition) return [];

  return definition.bindingSlots;
});

const trendCharts = computed<TrendChart[]>(() => {
  if (!selectedSymbol.value) return [];

  const charts = selectedSymbol.value.properties.trendCharts;
  if (!charts || !Array.isArray(charts)) return [];

  return charts as TrendChart[];
});

const isValidChart = computed(() => {
  return editingChart.value.bindingId !== '';
});

// Methods
const getBindingName = (bindingId: string): string => {
  const slot = symbolBindingSlots.value.find(s => s.id === bindingId);
  return slot ? slot.name : bindingId;
};

const addNewChart = () => {
  // Reset editing chart to defaults
  editingChart.value = {
    bindingId: symbolBindingSlots.value.length > 0 ? symbolBindingSlots.value[0].id : '',
    position: { x: 0, y: 0 },
    title: '',
    width: 120,
    height: 60,
    lineColor: '#1890ff',
    fillColor: 'rgba(24, 144, 255, 0.2)',
    backgroundColor: '#ffffff',
    backgroundOpacity: 0.7,
    textColor: '#333333',
    showTitle: true,
    showCurrentValue: true,
    valueFormat: 'decimal',
    valuePrecision: 2,
    valueUnit: '',
    maxPoints: 100,
    minValue: undefined,
    maxValue: undefined,
    autoScale: true
  };

  editingChartIndex.value = null;
  isEditModalVisible.value = true;
};

const editChart = (index: number) => {
  if (!selectedSymbol.value || !trendCharts.value[index]) return;

  // Clone the chart for editing
  editingChart.value = JSON.parse(JSON.stringify(trendCharts.value[index]));
  editingChartIndex.value = index;
  isEditModalVisible.value = true;
};

const removeChart = (index: number) => {
  if (!selectedSymbol.value || !trendCharts.value[index]) return;

  // Get current charts
  const charts = [...trendCharts.value];

  // Remove the chart at the specified index
  charts.splice(index, 1);

  // Update the symbol properties
  updateSymbolTrendCharts(charts);
};

const saveChart = () => {
  if (!selectedSymbol.value || !isValidChart.value) return;

  // Get current charts or initialize empty array
  const charts = [...trendCharts.value];

  if (editingChartIndex.value !== null) {
    // Update existing chart
    charts[editingChartIndex.value] = { ...editingChart.value };
  } else {
    // Add new chart
    charts.push({ ...editingChart.value });
  }

  // Update the symbol properties
  updateSymbolTrendCharts(charts);

  // Close the modal
  isEditModalVisible.value = false;
};

const cancelEdit = () => {
  isEditModalVisible.value = false;
};

const updateSymbolTrendCharts = (charts: TrendChart[]) => {
  if (!selectedSymbol.value) return;

  // Update the symbol properties
  diagramStore.updateSymbolProperties(
    selectedSymbol.value.id,
    {
      ...selectedSymbol.value.properties,
      trendCharts: charts
    }
  );
};
</script>

<style scoped>
.trend-chart-editor {
  padding: 8px;
}

.section-title {
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--primary-color);
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 2px;
}

.trend-charts-list {
  margin-bottom: 8px;
}

.chart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 11px;
}

.chart-actions {
  display: flex;
  gap: 2px;
}

.add-chart {
  margin-top: 4px;
}

.no-selection {
  color: #999;
  font-style: italic;
  text-align: center;
  margin-top: 8px;
  font-size: 11px;
}

/* Compact modal form styles */
:deep(.ant-modal-body .ant-form-item) {
  margin-bottom: 8px;
}

:deep(.ant-modal-body .ant-form-item-label > label) {
  font-size: 11px;
  height: 16px;
  line-height: 16px;
}

:deep(.ant-modal-body .ant-input),
:deep(.ant-modal-body .ant-input-number),
:deep(.ant-modal-body .ant-select) {
  font-size: 11px;
}

:deep(.ant-list-item) {
  padding: 4px 0;
}

:deep(.ant-list-item-meta-title) {
  font-size: 11px;
}
</style>
