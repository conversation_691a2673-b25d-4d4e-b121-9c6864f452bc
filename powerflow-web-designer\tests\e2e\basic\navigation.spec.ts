import { test, expect } from '@playwright/test';

/**
 * Level 1: Basic Navigation Tests
 * These tests verify that navigation between pages works correctly.
 */

test.describe('Basic Navigation', () => {
  test('should navigate between routes', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');

    // Check that we're on the home page
    await expect(page.url()).toContain('/');

    // Take a screenshot of the initial page
    await page.screenshot({ path: './test-results/navigation-initial.png' });

    // Try to navigate to a different route directly
    await page.goto('/about');

    // Wait for navigation to complete
    await page.waitForLoadState('domcontentloaded');

    // Log the new URL
    console.log(`Navigated to: ${page.url()}`);

    // Take a screenshot of the result
    await page.screenshot({ path: './test-results/navigation-result.png' });

    // Basic assertion - just check that the page is still loaded
    await expect(page).toBeTruthy();
  });

  test('should handle browser navigation', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');

    // Store the initial URL
    const initialUrl = page.url();
    console.log(`Initial URL: ${initialUrl}`);

    // Try to navigate to a different route
    await page.goto('/about');

    // Log the new URL
    console.log(`Navigated to: ${page.url()}`);

    // Go back in browser history
    await page.goBack();

    // Check that we're back at the initial URL
    console.log(`After going back: ${page.url()}`);

    // Basic assertion - just check that the page is still loaded
    await expect(page).toBeTruthy();
  });
});
