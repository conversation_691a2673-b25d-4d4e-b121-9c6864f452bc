<template>
  <div class="diagram-canvas" ref="canvasContainer">
    <v-stage
      ref="stage"
      :config="stageConfig"
      @wheel="handleWheel"
      @mousedown="handleMouseDown"
      @mouseup="handleMouseUp"
      @mousemove="handleMouseMove"
      @click="handleClick"
    >
      <v-layer ref="gridLayer">
        <!-- Grid will be rendered here -->
        <template v-if="gridConfig.pattern === 'lines'">
          <!-- Minor grid lines -->
          <v-line
            v-for="line in minorGridLines"
            :key="line.id"
            :config="line"
          />

          <!-- Major grid lines -->
          <v-line
            v-for="line in majorGridLines"
            :key="line.id"
            :config="line"
          />
        </template>

        <template v-else-if="gridConfig.pattern === 'dots'">
          <!-- Grid dots -->
          <v-circle
            v-for="dot in gridDots"
            :key="dot.id"
            :config="dot"
          />
        </template>
      </v-layer>
      <v-layer ref="groupLayer">
        <!-- Groups will be rendered here -->
        <slot name="groups"></slot>
      </v-layer>
      <v-layer ref="connectionLayer">
        <!-- Connections will be rendered here (behind symbols) -->
        <slot name="connections"></slot>
      </v-layer>
      <v-layer ref="mainLayer">
        <!-- Symbol instances will be rendered here (on top of connections) -->
        <slot name="symbols"></slot>
      </v-layer>
      <v-layer ref="textLayer">
        <!-- Text elements will be rendered here (on top of symbols) -->
        <slot name="text-elements"></slot>
      </v-layer>
      <v-layer ref="selectionLayer">
        <!-- Selection rectangles and transformers will be rendered here -->
        <v-transformer
          ref="transformer"
          :config="transformerConfig"
        />

        <!-- Selection rectangle overlay -->
        <v-rect
          v-if="isSelecting && selectionRect"
          :config="{
            x: selectionRect.x,
            y: selectionRect.y,
            width: selectionRect.width,
            height: selectionRect.height,
            stroke: '#1890ff',
            strokeWidth: 2,
            dash: [8, 4],
            fill: 'rgba(24, 144, 255, 0.08)',
            opacity: 0.9,
            listening: false,
          }"
        />

        <!-- Selection rectangle border for better visibility -->
        <v-rect
          v-if="isSelecting && selectionRect"
          :config="{
            x: selectionRect.x - 1,
            y: selectionRect.y - 1,
            width: selectionRect.width + 2,
            height: selectionRect.height + 2,
            stroke: 'rgba(24, 144, 255, 0.3)',
            strokeWidth: 1,
            fill: 'transparent',
            listening: false,
          }"
        />

        <slot name="temp-connection"></slot>
      </v-layer>
      <v-layer ref="guideLayer">
        <!-- Alignment guides will be rendered here -->
        <slot name="guides"></slot>
      </v-layer>
    </v-stage>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { DiagramViewport, DiagramGrid } from '@/types/diagram';
import { Position } from '@/types/symbol';

// Props
interface Props {
  textToolActive?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  textToolActive: false,
});

// Store
const diagramStore = useDiagramStore();

// Refs
const canvasContainer = ref<HTMLElement | null>(null);
const stage = ref(null);
const gridLayer = ref(null);
const groupLayer = ref(null);
const mainLayer = ref(null);
const textLayer = ref(null);
const connectionLayer = ref(null);
const selectionLayer = ref(null);
const transformer = ref(null);

// State
const stageSize = ref({ width: 800, height: 600 });
const viewport = ref<DiagramViewport>({
  position: { x: 0, y: 0 },
  scale: 1
});
const isDragging = ref(false);
const lastPointerPosition = ref<Position | null>(null);

// Computed properties
const stageConfig = computed(() => ({
  width: stageSize.value.width,
  height: stageSize.value.height,
  draggable: false,
  x: -viewport.value.position.x,
  y: -viewport.value.position.y,
  scaleX: viewport.value.scale,
  scaleY: viewport.value.scale,
}));

const transformerConfig = computed(() => ({
  rotateEnabled: true,
  enabledAnchors: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
  borderStroke: '#0096FF',
  borderStrokeWidth: 1,
  anchorStroke: '#0096FF',
  anchorFill: '#fff',
  anchorSize: 8,
  keepRatio: false,
  centeredScaling: false,
}));

const gridConfig = computed(() => {
  const grid = diagramStore.currentDiagram?.grid || {
    visible: false, // Grid disabled by default
    size: 20,
    snapToGrid: false, // Snap to grid disabled by default
    majorGridSize: 100,
    minorColor: '#e0e0e0',
    majorColor: '#c0c0c0',
    pattern: 'lines',
  };
  return grid;
});

// Calculate the effective grid size based on zoom level
const effectiveGridSize = computed(() => {
  const baseSize = gridConfig.value.size;
  const scale = viewport.value.scale;

  // Adjust grid size based on zoom level
  if (scale < 0.5) {
    return baseSize * 2; // Show fewer grid lines when zoomed out
  } else if (scale > 2) {
    return baseSize / 2; // Show more grid lines when zoomed in
  }

  return baseSize;
});

// Calculate the effective major grid size based on zoom level
const effectiveMajorGridSize = computed(() => {
  const baseMajorSize = gridConfig.value.majorGridSize;
  const scale = viewport.value.scale;

  // Adjust major grid size based on zoom level
  if (scale < 0.5) {
    return baseMajorSize * 2; // Show fewer major grid lines when zoomed out
  } else if (scale > 2) {
    return baseMajorSize / 2; // Show more major grid lines when zoomed in
  }

  return baseMajorSize;
});

// Minor grid lines
const minorGridLines = computed(() => {
  if (!gridConfig.value.visible) return [];

  const lines: any[] = [];
  const gridSize = effectiveGridSize.value;
  const majorGridSize = effectiveMajorGridSize.value;
  const width = stageSize.value.width;
  const height = stageSize.value.height;
  const minorColor = gridConfig.value.minorColor;

  // Calculate grid offset based on viewport position
  const offsetX = viewport.value.position.x % gridSize;
  const offsetY = viewport.value.position.y % gridSize;

  // Create vertical minor grid lines
  for (let x = offsetX; x < width; x += gridSize) {
    // Skip if this is a major grid line
    if (Math.round(x % majorGridSize) === 0) continue;

    lines.push({
      id: `v-minor-${x}`,
      points: [x, 0, x, height],
      stroke: minorColor,
      strokeWidth: 1,
      opacity: 0.5,
    });
  }

  // Create horizontal minor grid lines
  for (let y = offsetY; y < height; y += gridSize) {
    // Skip if this is a major grid line
    if (Math.round(y % majorGridSize) === 0) continue;

    lines.push({
      id: `h-minor-${y}`,
      points: [0, y, width, y],
      stroke: minorColor,
      strokeWidth: 1,
      opacity: 0.5,
    });
  }

  return lines;
});

// Major grid lines
const majorGridLines = computed(() => {
  if (!gridConfig.value.visible) return [];

  const lines: any[] = [];
  const majorGridSize = effectiveMajorGridSize.value;
  const width = stageSize.value.width;
  const height = stageSize.value.height;
  const majorColor = gridConfig.value.majorColor;

  // Calculate grid offset based on viewport position
  const offsetX = viewport.value.position.x % majorGridSize;
  const offsetY = viewport.value.position.y % majorGridSize;

  // Create vertical major grid lines
  for (let x = offsetX; x < width; x += majorGridSize) {
    lines.push({
      id: `v-major-${x}`,
      points: [x, 0, x, height],
      stroke: majorColor,
      strokeWidth: 1,
      opacity: 0.7,
    });
  }

  // Create horizontal major grid lines
  for (let y = offsetY; y < height; y += majorGridSize) {
    lines.push({
      id: `h-major-${y}`,
      points: [0, y, width, y],
      stroke: majorColor,
      strokeWidth: 1,
      opacity: 0.7,
    });
  }

  return lines;
});

// Grid dots
const gridDots = computed(() => {
  if (!gridConfig.value.visible) return [];

  const dots: any[] = [];
  const gridSize = effectiveGridSize.value;
  const width = stageSize.value.width;
  const height = stageSize.value.height;
  const majorGridSize = effectiveMajorGridSize.value;
  const minorColor = gridConfig.value.minorColor;
  const majorColor = gridConfig.value.majorColor;

  // Calculate grid offset based on viewport position
  const offsetX = viewport.value.position.x % gridSize;
  const offsetY = viewport.value.position.y % gridSize;

  // Create grid dots
  for (let x = offsetX; x < width; x += gridSize) {
    for (let y = offsetY; y < height; y += gridSize) {
      // Determine if this is a major grid intersection
      const isMajor = Math.round(x % majorGridSize) === 0 && Math.round(y % majorGridSize) === 0;

      dots.push({
        id: `dot-${x}-${y}`,
        x: x,
        y: y,
        radius: isMajor ? 2 : 1,
        fill: isMajor ? majorColor : minorColor,
        opacity: isMajor ? 0.7 : 0.5,
      });
    }
  }

  return dots;
});

// Methods
const updateStageSize = () => {
  if (canvasContainer.value) {
    stageSize.value = {
      width: canvasContainer.value.clientWidth,
      height: canvasContainer.value.clientHeight,
    };
  }
};

const handleWheel = (e: any) => {
  e.evt.preventDefault();

  // 获取 stage 实例
  const stageNode = stage.value?.getNode?.();
  if (!stageNode || typeof stageNode.getPointerPosition !== 'function') {
    console.warn('Stage not properly initialized for wheel event');
    return;
  }

  const oldScale = viewport.value.scale;
  const pointer = stageNode.getPointerPosition();

  // 确保指针位置有效
  if (!pointer) {
    console.warn('Invalid pointer position for wheel event');
    return;
  }

  // 确保 stage 的 x() 和 y() 方法可用
  if (typeof stageNode.x !== 'function' || typeof stageNode.y !== 'function') {
    console.warn('Stage x/y methods not available');
    return;
  }

  const mousePointTo = {
    x: (pointer.x - stageNode.x()) / oldScale,
    y: (pointer.y - stageNode.y()) / oldScale,
  };

  // Calculate new scale
  const scaleBy = 1.1;
  const newScale = e.evt.deltaY < 0 ? oldScale * scaleBy : oldScale / scaleBy;

  // Limit scale
  const limitedScale = Math.max(0.1, Math.min(newScale, 5));

  // Update viewport
  viewport.value = {
    position: {
      x: pointer.x / limitedScale - mousePointTo.x,
      y: pointer.y / limitedScale - mousePointTo.y,
    },
    scale: limitedScale,
  };

  // Notify about viewport change
  handleViewportChange();
};

// Selection state
const isSelecting = ref(false);
const selectionStart = ref<{ x: number; y: number } | null>(null);
const selectionRect = ref<{ x: number; y: number; width: number; height: number } | null>(null);

const handleMouseDown = (e: any) => {
  // Check if it's a left click
  if (e.evt.button === 0) {
    // 获取 stage 实例 - vue-konva 的 stage ref 需要通过 getNode() 方法获取
    const stageNode = stage.value?.getNode?.();
    if (!stageNode || typeof stageNode.getPointerPosition !== 'function') {
      console.warn('Stage not properly initialized for mouse down event');
      return;
    }

    const pointer = stageNode.getPointerPosition();
    if (!pointer) {
      console.warn('Invalid pointer position for mouse down event');
      return;
    }

    // Check if clicking on empty space (stage background or grid layer)
    const targetName = e.target.name?.() || e.target.className;
    const isEmptySpace = e.target === stageNode ||
                        targetName === 'gridLayer' ||
                        !targetName ||
                        e.target.parent?.name?.() === 'gridLayer';

    if (isEmptySpace) {
      console.log('Starting selection on empty space');

      // Check if Ctrl key is pressed for multi-selection
      if (!e.evt.ctrlKey) {
        // Clear selection if not holding Ctrl
        emit('clearSelection');
      }

      // Start selection rectangle
      isSelecting.value = true;
      selectionStart.value = { x: pointer.x, y: pointer.y };
      selectionRect.value = { x: pointer.x, y: pointer.y, width: 0, height: 0 };

      e.evt.preventDefault();
      return;
    }
  }

  // Handle canvas panning - middle-mouse click or shift+left click
  if (e.evt.button === 1 || (e.evt.button === 0 && e.evt.shiftKey)) {
    // 获取 stage 实例
    const stageNode = stage.value?.getNode?.();
    if (!stageNode || typeof stageNode.getPointerPosition !== 'function') {
      console.warn('Stage not properly initialized for mouse down event');
      return;
    }

    const pointer = stageNode.getPointerPosition();
    if (!pointer) {
      console.warn('Invalid pointer position for mouse down event');
      return;
    }

    isDragging.value = true;
    lastPointerPosition.value = pointer;
    e.evt.preventDefault();
  }
};

const handleMouseUp = () => {
  // Handle selection rectangle completion
  if (isSelecting.value && selectionRect.value) {
    // Find elements within selection rectangle
    const rect = selectionRect.value;
    console.log('Selection completed with rectangle:', rect);

    if (rect.width > 5 || rect.height > 5) { // Only process if rectangle is large enough
      console.log('Emitting selectInRectangle event');
      emit('selectInRectangle', rect);
    } else {
      console.log('Rectangle too small, not selecting');
    }

    // Reset selection state
    isSelecting.value = false;
    selectionStart.value = null;
    selectionRect.value = null;
  }

  // Handle canvas panning
  isDragging.value = false;
  lastPointerPosition.value = null;
};

const handleMouseMove = (e: any) => {
  // 获取 stage 实例
  const stageNode = stage.value?.getNode?.();
  if (!stageNode || typeof stageNode.getPointerPosition !== 'function') {
    console.warn('Stage not properly initialized for mouse move event');
    return;
  }

  const currentPointer = stageNode.getPointerPosition();
  if (!currentPointer) {
    console.warn('Invalid pointer position for mouse move event');
    return;
  }

  // Handle selection rectangle update
  if (isSelecting.value && selectionStart.value) {
    const startX = selectionStart.value.x;
    const startY = selectionStart.value.y;
    const currentX = currentPointer.x;
    const currentY = currentPointer.y;

    selectionRect.value = {
      x: Math.min(startX, currentX),
      y: Math.min(startY, currentY),
      width: Math.abs(currentX - startX),
      height: Math.abs(currentY - startY),
    };

    console.log('Selection rectangle updated:', selectionRect.value);
    e.evt.preventDefault();
    return;
  }

  // Handle canvas panning
  if (!isDragging.value || !lastPointerPosition.value) return;

  const dx = (currentPointer.x - lastPointerPosition.value.x) / viewport.value.scale;
  const dy = (currentPointer.y - lastPointerPosition.value.y) / viewport.value.scale;

  viewport.value = {
    ...viewport.value,
    position: {
      x: viewport.value.position.x - dx,
      y: viewport.value.position.y - dy,
    },
  };

  lastPointerPosition.value = currentPointer;
  e.evt.preventDefault();

  // Notify about viewport change
  handleViewportChange();
};

const handleClick = (e: any) => {
  // Get stage instance
  const stageNode = stage.value?.getNode?.();
  if (!stageNode) {
    console.warn('Stage not properly initialized for click event');
    return;
  }

  // Check if clicking on empty space
  const targetName = e.target.name?.() || e.target.className;
  const isEmptySpace = e.target === stageNode ||
                      targetName === 'gridLayer' ||
                      !targetName ||
                      e.target.parent?.name?.() === 'gridLayer';

  if (isEmptySpace) {
    // Handle text tool placement
    if (props.textToolActive) {
      const pointer = stageNode.getPointerPosition();
      if (pointer) {
        // Convert screen coordinates to world coordinates
        const worldPosition = {
          x: (pointer.x - viewport.value.position.x) / viewport.value.scale,
          y: (pointer.y - viewport.value.position.y) / viewport.value.scale,
        };

        console.log('Placing text at position:', worldPosition);
        emit('placeText', worldPosition);
        return;
      }
    }

    // Default behavior: clear selection
    if (transformer.value && typeof transformer.value.nodes === 'function') {
      transformer.value.nodes([]);
    }

    if (diagramStore.currentDiagram) {
      diagramStore.currentDiagram.selectedSymbolIds.length = 0;
      diagramStore.currentDiagram.selectedConnectionIds.length = 0;
      diagramStore.currentDiagram.selectedTextElementIds.length = 0;
      diagramStore.currentDiagram.selectedGroupIds.length = 0;
    }
  }
};

// 等待画面数据准备就绪
const waitForDiagramReady = async () => {
  const maxWaitTime = 5000; // 最大等待5秒
  const checkInterval = 50; // 每50ms检查一次
  let waitedTime = 0;

  while (!diagramStore.currentDiagram && waitedTime < maxWaitTime) {
    await new Promise(resolve => setTimeout(resolve, checkInterval));
    waitedTime += checkInterval;
  }

  if (!diagramStore.currentDiagram) {
    console.warn('DiagramCanvas: Timeout waiting for diagram to be ready, proceeding with default');
  } else {
    console.log('DiagramCanvas: Diagram is ready:', diagramStore.currentDiagram.name);
  }
};

// Lifecycle hooks
const emit = defineEmits<{
  (e: 'mounted', canvas: any): void;
  (e: 'clearSelection'): void;
  (e: 'selectInRectangle', rect: { x: number; y: number; width: number; height: number }): void;
  (e: 'placeText', position: { x: number; y: number }): void;
}>();

onMounted(async () => {
  console.log('DiagramCanvas component mounted');

  // 更新舞台大小
  updateStageSize();
  window.addEventListener('resize', updateStageSize);

  // 等待画面数据加载完成后再初始化
  await waitForDiagramReady();

  // 初始化视口
  initializeViewport();

  // 等待舞台初始化
  await nextTick();

  // 确保舞台已经正确初始化
  const stageNode = stage.value?.getNode?.();
  if (stage.value && stageNode) {
    console.log('Stage initialized successfully');
    // Emit mounted event with canvas reference
    emit('mounted', {
      stage: stage.value,
      gridLayer: gridLayer.value,
      mainLayer: mainLayer.value,
      connectionLayer: connectionLayer.value,
      selectionLayer: selectionLayer.value,
      transformer: transformer.value
    });
  } else {
    console.warn('Stage initialization failed');
  }
});

// 初始化视口
const initializeViewport = () => {
  // 使用默认视口
  const defaultViewport = {
    position: { x: 0, y: 0 },
    scale: 1
  };

  // 如果当前画面不可用，使用默认视口
  if (!diagramStore.currentDiagram) {
    console.log('DiagramCanvas: No current diagram available, using default viewport');
    viewport.value = { ...defaultViewport };
    return;
  }

  // 初始化视口
  if (diagramStore.currentDiagram.viewport) {
    const diagramViewport = diagramStore.currentDiagram.viewport;
    console.log('DiagramCanvas: Initializing viewport from diagram:', diagramViewport);

    viewport.value = {
      position: {
        x: diagramViewport.position.x,
        y: diagramViewport.position.y
      },
      scale: diagramViewport.scale
    };
  } else {
    console.log('DiagramCanvas: Diagram has no viewport, using default');

    // 使用默认视口
    viewport.value = { ...defaultViewport };

    // 更新画面的视口
    diagramStore.currentDiagram.viewport = { ...viewport.value };
  }
};

// When the diagram changes, update the viewport
watch(() => diagramStore.currentDiagram?.id, (newDiagramId) => {
  if (diagramStore.currentDiagram?.viewport && newDiagramId) {
    const diagramViewport = diagramStore.currentDiagram.viewport;
    viewport.value = {
      position: {
        x: diagramViewport.position.x,
        y: diagramViewport.position.y
      },
      scale: diagramViewport.scale
    };
  }
});

// Watch for external viewport changes (e.g., from toolbar zoom buttons)
watch(() => diagramStore.currentDiagram?.viewport, (newViewport) => {
  if (newViewport && diagramStore.currentDiagram) {
    // Only update if the viewport actually changed to avoid infinite loops
    const currentViewport = viewport.value;
    const hasChanged =
      Math.abs(currentViewport.position.x - newViewport.position.x) > 0.001 ||
      Math.abs(currentViewport.position.y - newViewport.position.y) > 0.001 ||
      Math.abs(currentViewport.scale - newViewport.scale) > 0.001;

    if (hasChanged) {
      console.log('DiagramCanvas: External viewport change detected, updating local viewport');
      viewport.value = {
        position: {
          x: newViewport.position.x,
          y: newViewport.position.y
        },
        scale: newViewport.scale
      };
    }
  }
}, { deep: true });

// Define a method to update the diagram viewport
const updateDiagramViewport = () => {
  if (diagramStore.currentDiagram) {
    diagramStore.currentDiagram.viewport = {
      position: {
        x: viewport.value.position.x,
        y: viewport.value.position.y
      },
      scale: viewport.value.scale
    };
  }
};

// Define a timeout variable for debouncing
let viewportUpdateTimeout: number | null = null;

// Update the diagram viewport when user interactions change the viewport
const handleViewportChange = () => {
  // Debounce the update to avoid excessive store updates
  if (viewportUpdateTimeout) {
    clearTimeout(viewportUpdateTimeout);
  }
  viewportUpdateTimeout = window.setTimeout(() => {
    updateDiagramViewport();
    viewportUpdateTimeout = null;
  }, 100);
};
defineExpose({
  stage,
  gridLayer,
  mainLayer,
  connectionLayer,
  selectionLayer,
  transformer,
  viewport,
  updateStageSize,
  getWorldPosition: (screenPosition: Position) => {
    if (!stage.value) return screenPosition;
    const scale = viewport.value.scale;
    return {
      x: (screenPosition.x + viewport.value.position.x) / scale,
      y: (screenPosition.y + viewport.value.position.y) / scale
    };
  },
  getScreenPosition: (worldPosition: Position) => {
    if (!stage.value) return worldPosition;
    const scale = viewport.value.scale;
    return {
      x: worldPosition.x * scale - viewport.value.position.x,
      y: worldPosition.y * scale - viewport.value.position.y
    };
  }
});
</script>

<style scoped>
.diagram-canvas {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
}
</style>
