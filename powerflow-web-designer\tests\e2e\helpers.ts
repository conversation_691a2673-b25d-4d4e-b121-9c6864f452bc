import { Page, expect } from '@playwright/test';

/**
 * Helper functions for PowerFlow Web Designer end-to-end tests
 *
 * These functions provide common operations used across multiple test files.
 */

/**
 * Navigate to the home page
 */
export async function navigateToHome(page: Page): Promise<void> {
  await page.goto('/');
  await expect(page.locator('#app')).toBeVisible();
}

/**
 * Navigate to a specific route
 */
export async function navigateToRoute(page: Page, route: string): Promise<void> {
  await page.goto(route);
  await expect(page.locator('#app')).toBeVisible();
}

/**
 * Navigate to the editor page
 */
export async function navigateToEditor(page: Page): Promise<void> {
  // Navigate to the editor route
  await page.goto('/editor');

  // Wait for the page to load
  await page.waitForLoadState('domcontentloaded');

  // Check that the app container is visible
  await expect(page.locator('#app')).toBeVisible();

  // Take a screenshot for debugging
  await page.screenshot({ path: './test-results/editor-navigation.png' });
}

/**
 * Navigate to the viewer page
 */
export async function navigateToViewer(page: Page): Promise<void> {
  // Navigate to the viewer route
  await page.goto('/viewer');

  // Wait for the page to load
  await page.waitForLoadState('domcontentloaded');

  // Check that the app container is visible
  await expect(page.locator('#app')).toBeVisible();

  // Take a screenshot for debugging
  await page.screenshot({ path: './test-results/viewer-navigation.png' });
}

/**
 * Take a screenshot with a descriptive name
 */
export async function takeScreenshot(page: Page, name: string): Promise<void> {
  await page.screenshot({ path: `./test-results/${name}.png` });
}

/**
 * Check if an element exists on the page
 */
export async function elementExists(page: Page, selector: string): Promise<boolean> {
  const count = await page.locator(selector).count();
  return count > 0;
}

/**
 * Click a button if it exists
 */
export async function clickIfExists(page: Page, selector: string): Promise<boolean> {
  try {
    if (await elementExists(page, selector)) {
      await page.locator(selector).first().click();
      return true;
    }
  } catch (error) {
    console.log(`Error clicking element ${selector}: ${error.message}`);
  }
  return false;
}

/**
 * Create a new diagram in the editor
 */
export async function createNewDiagram(page: Page, name: string = 'Test Diagram'): Promise<void> {
  // Navigate to the editor
  await navigateToEditor(page);

  try {
    // Look for a New button
    if (await elementExists(page, 'button:has-text("New")')) {
      await page.locator('button:has-text("New")').click();

      // Try to find a diagram name input field
      if (await elementExists(page, 'input[placeholder*="name" i], input[aria-label*="name" i]')) {
        await page.locator('input[placeholder*="name" i], input[aria-label*="name" i]').first().fill(name);

        // Look for a Create or OK button
        if (await elementExists(page, 'button:has-text("Create"), button:has-text("OK")')) {
          await page.locator('button:has-text("Create"), button:has-text("OK")').first().click();
        }
      }
    } else {
      // If no New button, try to interact with the canvas directly
      console.log('No New button found, trying to interact with canvas directly');

      // Take a screenshot for debugging
      await page.screenshot({ path: './test-results/new-diagram-no-button.png' });
    }

    // Wait for any potential loading to complete
    await page.waitForTimeout(500);

  } catch (error) {
    console.log(`Error creating new diagram: ${error.message}`);
    await page.screenshot({ path: './test-results/create-diagram-error.png' });
  }
}

/**
 * Add a symbol to the diagram
 */
export async function addSymbolToDiagram(page: Page, symbolType: string): Promise<void> {
  try {
    // Try to find a Symbols button or palette
    if (await elementExists(page, 'button:has-text("Symbols")')) {
      // Open the symbol palette
      await page.locator('button:has-text("Symbols")').click();

      // Try to find the symbol in the palette
      if (await elementExists(page, `.symbol-palette, .symbol-library`)) {
        const symbolSelector = `.symbol-palette, .symbol-library`;

        // Try to find the specific symbol
        if (await elementExists(page, `${symbolSelector} :text("${symbolType}")`)) {
          await page.locator(`${symbolSelector} :text("${symbolType}")`).first().click();

          // Click on the canvas to place the symbol
          if (await elementExists(page, '.diagram-canvas, .canvas')) {
            await page.locator('.diagram-canvas, .canvas').first().click();
          }
        } else {
          console.log(`Symbol "${symbolType}" not found in palette`);
          await page.screenshot({ path: './test-results/symbol-not-found.png' });
        }

        // Close the symbol palette if it's still open
        if (await elementExists(page, 'button:has-text("Symbols")')) {
          await page.locator('button:has-text("Symbols")').click();
        }
      } else {
        console.log('Symbol palette not found');
        await page.screenshot({ path: './test-results/symbol-palette-not-found.png' });
      }
    } else {
      console.log('Symbols button not found');
      await page.screenshot({ path: './test-results/symbols-button-not-found.png' });

      // Try alternative approach - click directly on the canvas
      if (await elementExists(page, '.diagram-canvas, .canvas')) {
        await page.locator('.diagram-canvas, .canvas').first().click();
      }
    }
  } catch (error) {
    console.log(`Error adding symbol: ${error.message}`);
    await page.screenshot({ path: './test-results/add-symbol-error.png' });
  }
}

/**
 * Create a connection between two symbols
 */
export async function createConnectionSimple(page: Page, sourceIndex: number, targetIndex: number): Promise<void> {
  // Get the symbols
  const symbols = page.locator('.symbol-instance');
  const source = symbols.nth(sourceIndex);
  const target = symbols.nth(targetIndex);

  // Get the bounding boxes
  const sourceBounds = await source.boundingBox();
  const targetBounds = await target.boundingBox();

  if (sourceBounds && targetBounds) {
    // Start drag from the center of the source symbol
    await page.mouse.move(
      sourceBounds.x + sourceBounds.width / 2,
      sourceBounds.y + sourceBounds.height / 2
    );
    await page.mouse.down();

    // Move to the center of the target symbol
    await page.mouse.move(
      targetBounds.x + targetBounds.width / 2,
      targetBounds.y + targetBounds.height / 2
    );
    await page.mouse.up();
  }

  // Wait for the connection to be created
  await page.waitForTimeout(500); // Allow time for the connection to be rendered
}

/**
 * Select a symbol on the canvas (simple version)
 * @deprecated Use the more robust version below
 */
export async function selectSymbolSimple(page: Page, symbolIndex: number): Promise<void> {
  const symbols = page.locator('.symbol-instance');
  await symbols.nth(symbolIndex).click();
}

/**
 * Open the property panel for a symbol (simple version)
 * @deprecated Use the more robust version below
 */
export async function openPropertyPanelSimple(page: Page, symbolIndex: number): Promise<void> {
  await selectSymbolSimple(page, symbolIndex);
  await page.getByRole('button', { name: 'Properties' }).click();
}

/**
 * Create a group from selected symbols
 */
export async function createGroup(page: Page, symbolIndices: number[]): Promise<void> {
  // Select the first symbol
  await selectSymbolSimple(page, symbolIndices[0]);

  // Hold Shift and select additional symbols
  for (let i = 1; i < symbolIndices.length; i++) {
    await page.keyboard.down('Shift');
    await selectSymbolSimple(page, symbolIndices[i]);
    await page.keyboard.up('Shift');
  }

  // Right-click to open context menu
  const symbols = page.locator('.symbol-instance');
  const firstSymbol = symbols.nth(symbolIndices[0]);
  await firstSymbol.click({ button: 'right' });

  // Click the "Group" option
  await page.getByText('Group').click();

  // Wait for the group to be created
  await page.waitForTimeout(500);
}

/**
 * Apply a connection theme to a connection (simple version)
 * @deprecated Use the more robust version below
 */
export async function applyConnectionThemeSimple(page: Page, connectionIndex: number, themeName: string): Promise<void> {
  // Select the connection
  const connections = page.locator('.connection');
  await connections.nth(connectionIndex).click();

  // Open the connection theme selector
  await page.getByRole('button', { name: 'Connection Themes' }).click();

  // Select the theme
  await page.locator('.connection-theme-selector').getByText(themeName).click();

  // Wait for the theme to be applied
  await page.waitForTimeout(500);
}

/**
 * Create a custom connection theme (simple version)
 * @deprecated Use the more robust version below
 */
export async function createCustomConnectionThemeSimple(
  page: Page,
  themeName: string,
  color: string = 'Blue',
  thickness: string = '2',
  lineStyle: string = 'dashed'
): Promise<void> {
  // Open the connection theme selector
  await page.getByRole('button', { name: 'Connection Themes' }).click();

  // Click the "Create Custom Theme" button
  await page.locator('.connection-theme-selector').getByRole('button', { name: 'Create Custom Theme' }).click();

  // Check that the theme editor is visible
  await expect(page.locator('.connection-theme-editor')).toBeVisible();

  // Fill in the theme name
  await page.locator('.connection-theme-editor').getByLabel('Theme Name').fill(themeName);

  // Select color
  await page.locator('.connection-theme-editor').getByLabel('Color').selectOption(color);

  // Set thickness
  await page.locator('.connection-theme-editor').getByLabel('Thickness').fill(thickness);

  // Select line style
  await page.locator('.connection-theme-editor').getByLabel('Line Style').selectOption(lineStyle);

  // Save the theme
  await page.locator('.connection-theme-editor').getByRole('button', { name: 'Save' }).click();

  // Wait for the theme to be saved
  await page.waitForTimeout(500);
}

/**
 * Wait for a specific element to be visible with a custom timeout
 */
export async function waitForElement(page: Page, selector: string, timeoutMs: number = 5000): Promise<boolean> {
  try {
    await page.locator(selector).first().waitFor({ state: 'visible', timeout: timeoutMs });
    return true;
  } catch (error) {
    console.log(`Timeout waiting for element ${selector}: ${error.message}`);
    return false;
  }
}

/**
 * Fill a form field if it exists
 */
export async function fillFormField(page: Page, selector: string, value: string): Promise<boolean> {
  try {
    if (await elementExists(page, selector)) {
      await page.locator(selector).first().fill(value);
      return true;
    }
  } catch (error) {
    console.log(`Error filling form field ${selector}: ${error.message}`);
  }
  return false;
}

/**
 * Perform a drag and drop operation if both elements exist
 */
export async function dragAndDrop(
  page: Page,
  sourceSelector: string,
  targetSelector: string
): Promise<boolean> {
  try {
    // Check if both elements exist
    const sourceExists = await elementExists(page, sourceSelector);
    const targetExists = await elementExists(page, targetSelector);

    if (sourceExists && targetExists) {
      const source = page.locator(sourceSelector).first();
      const target = page.locator(targetSelector).first();

      // Get bounding boxes
      const sourceBounds = await source.boundingBox();
      const targetBounds = await target.boundingBox();

      if (sourceBounds && targetBounds) {
        // Perform drag and drop
        await page.mouse.move(
          sourceBounds.x + sourceBounds.width / 2,
          sourceBounds.y + sourceBounds.height / 2
        );
        await page.mouse.down();
        await page.mouse.move(
          targetBounds.x + targetBounds.width / 2,
          targetBounds.y + targetBounds.height / 2
        );
        await page.mouse.up();

        // Wait for any animations to complete
        await page.waitForTimeout(500);
        return true;
      }
    }
  } catch (error) {
    console.log(`Error during drag and drop: ${error.message}`);
  }
  return false;
}

/**
 * Run an accessibility scan on the current page
 */
export async function runAccessibilityScan(page: Page): Promise<any> {
  try {
    // Check if axe is available
    const axeAvailable = await page.evaluate(() => {
      return typeof window.axe !== 'undefined';
    });

    if (!axeAvailable) {
      // Inject axe-core if not available
      await page.addScriptTag({
        url: 'https://cdnjs.cloudflare.com/ajax/libs/axe-core/4.7.0/axe.min.js'
      });
    }

    // Run the accessibility scan
    return await page.evaluate(() => {
      return window.axe.run();
    });
  } catch (error) {
    console.log(`Error running accessibility scan: ${error.message}`);
    return { violations: [] };
  }
}

/**
 * Select a symbol in the diagram
 */
export async function selectSymbol(page: Page, index: number): Promise<boolean> {
  try {
    // Look for symbol elements with various possible selectors
    const symbolSelectors = [
      '.symbol-instance',
      '.node',
      '.element',
      '.component',
      '.shape',
      'svg g',
      '[data-element-type]'
    ];

    // Try each selector
    for (const selector of symbolSelectors) {
      if (await elementExists(page, selector)) {
        const count = await page.locator(selector).count();
        console.log(`Found ${count} elements with selector: ${selector}`);

        if (count > index) {
          // Click on the symbol at the specified index
          await page.locator(selector).nth(index).click();
          console.log(`Selected element at index ${index} with selector: ${selector}`);

          // Take a screenshot for debugging
          await takeScreenshot(page, `selected-symbol-${selector.replace(/[^a-zA-Z0-9]/g, '-')}`);
          return true;
        }
      }
    }

    console.log(`No selectable elements found at index ${index}`);
    return false;
  } catch (error) {
    console.log(`Error selecting symbol: ${error.message}`);
    await takeScreenshot(page, 'select-symbol-error');
    return false;
  }
}

/**
 * Open the property panel for a symbol (robust version)
 */
export async function openPropertyPanel(page: Page, symbolIndex: number): Promise<boolean> {
  try {
    // First select the symbol
    const selected = await selectSymbol(page, symbolIndex);

    if (!selected) {
      console.log('Failed to select symbol, cannot open property panel');
      return false;
    }

    // Look for property panel elements with various possible selectors
    const panelSelectors = [
      '.property-panel',
      '.properties-panel',
      '.inspector',
      '.sidebar',
      '[role="complementary"]',
      '.panel'
    ];

    // Check if any property panel is visible
    for (const selector of panelSelectors) {
      if (await elementExists(page, selector)) {
        console.log(`Found property panel with selector: ${selector}`);

        // Take a screenshot for debugging
        await takeScreenshot(page, `property-panel-${selector.replace(/[^a-zA-Z0-9]/g, '-')}`);
        return true;
      }
    }

    // If no panel is visible, try to open it
    const panelButtons = [
      'button:has-text("Properties")',
      'button:has-text("Inspector")',
      'button:has-text("Panel")',
      '[aria-label*="properties" i]',
      '[aria-label*="inspector" i]'
    ];

    // Try to click any button that might open the panel
    for (const buttonSelector of panelButtons) {
      if (await elementExists(page, buttonSelector)) {
        await page.locator(buttonSelector).first().click();
        console.log(`Clicked button with selector: ${buttonSelector}`);

        // Wait a moment for the panel to appear
        await page.waitForTimeout(500);

        // Check if any panel is now visible
        for (const selector of panelSelectors) {
          if (await elementExists(page, selector)) {
            console.log(`Property panel now visible with selector: ${selector}`);
            return true;
          }
        }
      }
    }

    console.log('Could not find or open property panel');
    return false;
  } catch (error) {
    console.log(`Error opening property panel: ${error.message}`);
    await takeScreenshot(page, 'open-property-panel-error');
    return false;
  }
}

/**
 * Create a connection between two symbols
 */
export async function createConnection(page: Page, sourceIndex: number, targetIndex: number): Promise<boolean> {
  try {
    // Look for symbol elements with various possible selectors
    const symbolSelectors = [
      '.symbol-instance',
      '.node',
      '.element',
      '.component',
      '.shape',
      'svg g',
      '[data-element-type]'
    ];

    // Find a valid selector that has enough elements
    let validSelector = '';
    let elementsCount = 0;

    for (const selector of symbolSelectors) {
      if (await elementExists(page, selector)) {
        const count = await page.locator(selector).count();
        console.log(`Found ${count} elements with selector: ${selector}`);

        if (count > Math.max(sourceIndex, targetIndex)) {
          validSelector = selector;
          elementsCount = count;
          break;
        }
      }
    }

    if (!validSelector) {
      console.log(`No selector found with enough elements for indices ${sourceIndex} and ${targetIndex}`);
      return false;
    }

    // Get the source and target elements
    const sourceElement = page.locator(validSelector).nth(sourceIndex);
    const targetElement = page.locator(validSelector).nth(targetIndex);

    // Get the bounding boxes
    const sourceBounds = await sourceElement.boundingBox();
    const targetBounds = await targetElement.boundingBox();

    if (!sourceBounds || !targetBounds) {
      console.log('Could not get bounding boxes for source or target elements');
      return false;
    }

    // Take a screenshot before creating the connection
    await takeScreenshot(page, 'before-connection');

    // Try to create a connection using mouse actions
    try {
      // Method 1: Drag from source to target
      await page.mouse.move(
        sourceBounds.x + sourceBounds.width / 2,
        sourceBounds.y + sourceBounds.height / 2
      );
      await page.mouse.down();
      await page.mouse.move(
        targetBounds.x + targetBounds.width / 2,
        targetBounds.y + targetBounds.height / 2,
        { steps: 10 } // Move in steps for better tracking
      );
      await page.mouse.up();

      // Wait a moment for the connection to be created
      await page.waitForTimeout(500);

    } catch (mouseError) {
      console.log(`Error with mouse method: ${mouseError.message}`);

      // Method 2: Try alternative approach - click source then target
      try {
        await sourceElement.click();
        await page.waitForTimeout(300);
        await targetElement.click();
        await page.waitForTimeout(500);
      } catch (clickError) {
        console.log(`Error with click method: ${clickError.message}`);
        return false;
      }
    }

    // Take a screenshot after creating the connection
    await takeScreenshot(page, 'after-connection');

    // Check if a connection was created
    const connectionSelectors = [
      '.connection-path',
      '.connection',
      '.edge',
      'path',
      'line',
      'svg path',
      'svg line'
    ];

    // Check if any connection element exists
    for (const selector of connectionSelectors) {
      if (await elementExists(page, selector)) {
        const count = await page.locator(selector).count();
        console.log(`Found ${count} connection elements with selector: ${selector}`);
        if (count > 0) {
          return true;
        }
      }
    }

    console.log('No connection elements found after attempt');
    return false;
  } catch (error) {
    console.log(`Error creating connection: ${error.message}`);
    await takeScreenshot(page, 'create-connection-error');
    return false;
  }
}

/**
 * Apply a connection theme to a connection
 */
export async function applyConnectionTheme(page: Page, themeName: string): Promise<boolean> {
  try {
    // Look for connection theme button
    const themeButtonSelectors = [
      'button:has-text("Connection Themes")',
      'button:has-text("Themes")',
      'button:has-text("Styles")',
      '[aria-label*="theme" i]',
      '[aria-label*="style" i]'
    ];

    // Try to find and click a theme button
    let themeButtonFound = false;
    for (const selector of themeButtonSelectors) {
      if (await elementExists(page, selector)) {
        await page.locator(selector).first().click();
        console.log(`Clicked theme button with selector: ${selector}`);
        themeButtonFound = true;

        // Wait a moment for the theme selector to appear
        await page.waitForTimeout(500);
        break;
      }
    }

    if (!themeButtonFound) {
      console.log('Could not find connection theme button');
      return false;
    }

    // Look for theme selector
    const themeSelectorSelectors = [
      '.connection-theme-selector',
      '.theme-selector',
      '.style-selector',
      '.dropdown-menu',
      '.menu',
      '.popup'
    ];

    // Check if theme selector is visible
    let themeSelectorFound = false;
    let themeSelectorSelector = '';

    for (const selector of themeSelectorSelectors) {
      if (await elementExists(page, selector)) {
        console.log(`Found theme selector with selector: ${selector}`);
        themeSelectorFound = true;
        themeSelectorSelector = selector;

        // Take a screenshot of the theme selector
        await takeScreenshot(page, `theme-selector-${selector.replace(/[^a-zA-Z0-9]/g, '-')}`);
        break;
      }
    }

    if (!themeSelectorFound) {
      console.log('Could not find theme selector');
      return false;
    }

    // Try to find and click the specified theme
    try {
      // Look for the theme by name
      const themeItemSelector = `${themeSelectorSelector} :text("${themeName}")`;

      if (await elementExists(page, themeItemSelector)) {
        await page.locator(themeItemSelector).first().click();
        console.log(`Selected theme: ${themeName}`);

        // Wait a moment for the theme to be applied
        await page.waitForTimeout(500);

        // Take a screenshot after applying the theme
        await takeScreenshot(page, `after-theme-${themeName.replace(/[^a-zA-Z0-9]/g, '-')}`);
        return true;
      } else {
        console.log(`Theme "${themeName}" not found in selector`);

        // Close the theme selector
        await page.keyboard.press('Escape');
        return false;
      }
    } catch (error) {
      console.log(`Error selecting theme: ${error.message}`);

      // Try to close the theme selector
      await page.keyboard.press('Escape');
      return false;
    }
  } catch (error) {
    console.log(`Error applying connection theme: ${error.message}`);
    await takeScreenshot(page, 'apply-theme-error');
    return false;
  }
}

/**
 * Create a custom connection theme
 */
export async function createCustomConnectionTheme(
  page: Page,
  themeName: string,
  color: string = 'Blue',
  thickness: string = '2',
  lineStyle: string = 'dashed'
): Promise<boolean> {
  try {
    // First open the connection theme selector
    const themeButtonSelectors = [
      'button:has-text("Connection Themes")',
      'button:has-text("Themes")',
      'button:has-text("Styles")',
      '[aria-label*="theme" i]',
      '[aria-label*="style" i]'
    ];

    // Try to find and click a theme button
    let themeButtonFound = false;
    for (const selector of themeButtonSelectors) {
      if (await elementExists(page, selector)) {
        await page.locator(selector).first().click();
        console.log(`Clicked theme button with selector: ${selector}`);
        themeButtonFound = true;

        // Wait a moment for the theme selector to appear
        await page.waitForTimeout(500);
        break;
      }
    }

    if (!themeButtonFound) {
      console.log('Could not find connection theme button');
      return false;
    }

    // Look for "Create Custom Theme" button
    const createThemeButtonSelectors = [
      'button:has-text("Create Custom Theme")',
      'button:has-text("New Theme")',
      'button:has-text("Add Theme")',
      'button:has-text("Custom")',
      'button:has-text("+")'
    ];

    // Try to find and click the create theme button
    let createButtonFound = false;
    for (const selector of createThemeButtonSelectors) {
      if (await elementExists(page, selector)) {
        await page.locator(selector).first().click();
        console.log(`Clicked create theme button with selector: ${selector}`);
        createButtonFound = true;

        // Wait a moment for the theme editor to appear
        await page.waitForTimeout(500);
        break;
      }
    }

    if (!createButtonFound) {
      console.log('Could not find create custom theme button');

      // Try to close the theme selector
      await page.keyboard.press('Escape');
      return false;
    }

    // Look for theme editor
    const themeEditorSelectors = [
      '.connection-theme-editor',
      '.theme-editor',
      '.style-editor',
      '.modal',
      '.dialog',
      'form'
    ];

    // Check if theme editor is visible
    let themeEditorFound = false;
    let themeEditorSelector = '';

    for (const selector of themeEditorSelectors) {
      if (await elementExists(page, selector)) {
        console.log(`Found theme editor with selector: ${selector}`);
        themeEditorFound = true;
        themeEditorSelector = selector;

        // Take a screenshot of the theme editor
        await takeScreenshot(page, `theme-editor-${selector.replace(/[^a-zA-Z0-9]/g, '-')}`);
        break;
      }
    }

    if (!themeEditorFound) {
      console.log('Could not find theme editor');
      return false;
    }

    // Fill in the theme details
    try {
      // Fill in theme name
      const nameInputSelectors = [
        `${themeEditorSelector} input[placeholder*="name" i]`,
        `${themeEditorSelector} [aria-label*="name" i]`,
        `${themeEditorSelector} input:first-child`
      ];

      let nameInputFound = false;
      for (const selector of nameInputSelectors) {
        if (await elementExists(page, selector)) {
          await page.locator(selector).first().fill(themeName);
          console.log(`Filled theme name: ${themeName}`);
          nameInputFound = true;
          break;
        }
      }

      if (!nameInputFound) {
        console.log('Could not find theme name input');
      }

      // Select color
      const colorSelectSelectors = [
        `${themeEditorSelector} select[aria-label*="color" i]`,
        `${themeEditorSelector} [aria-label*="color" i]`,
        `${themeEditorSelector} select:nth-child(2)`
      ];

      let colorSelectFound = false;
      for (const selector of colorSelectSelectors) {
        if (await elementExists(page, selector)) {
          await page.locator(selector).first().selectOption(color);
          console.log(`Selected color: ${color}`);
          colorSelectFound = true;
          break;
        }
      }

      if (!colorSelectFound) {
        console.log('Could not find color select');
      }

      // Set thickness
      const thicknessInputSelectors = [
        `${themeEditorSelector} input[aria-label*="thickness" i]`,
        `${themeEditorSelector} [aria-label*="thickness" i]`,
        `${themeEditorSelector} input[type="number"]`
      ];

      let thicknessInputFound = false;
      for (const selector of thicknessInputSelectors) {
        if (await elementExists(page, selector)) {
          await page.locator(selector).first().fill(thickness);
          console.log(`Set thickness: ${thickness}`);
          thicknessInputFound = true;
          break;
        }
      }

      if (!thicknessInputFound) {
        console.log('Could not find thickness input');
      }

      // Select line style
      const lineStyleSelectSelectors = [
        `${themeEditorSelector} select[aria-label*="style" i]`,
        `${themeEditorSelector} [aria-label*="style" i]`,
        `${themeEditorSelector} select:nth-child(4)`
      ];

      let lineStyleSelectFound = false;
      for (const selector of lineStyleSelectSelectors) {
        if (await elementExists(page, selector)) {
          await page.locator(selector).first().selectOption(lineStyle);
          console.log(`Selected line style: ${lineStyle}`);
          lineStyleSelectFound = true;
          break;
        }
      }

      if (!lineStyleSelectFound) {
        console.log('Could not find line style select');
      }

      // Save the theme
      const saveButtonSelectors = [
        `${themeEditorSelector} button:has-text("Save")`,
        `${themeEditorSelector} button:has-text("OK")`,
        `${themeEditorSelector} button:has-text("Create")`,
        `${themeEditorSelector} button[type="submit"]`
      ];

      let saveButtonFound = false;
      for (const selector of saveButtonSelectors) {
        if (await elementExists(page, selector)) {
          await page.locator(selector).first().click();
          console.log('Clicked save button');
          saveButtonFound = true;

          // Wait a moment for the theme to be saved
          await page.waitForTimeout(500);
          break;
        }
      }

      if (!saveButtonFound) {
        console.log('Could not find save button');

        // Try to close the theme editor
        await page.keyboard.press('Escape');
        return false;
      }

      // Take a screenshot after creating the theme
      await takeScreenshot(page, `after-create-theme-${themeName.replace(/[^a-zA-Z0-9]/g, '-')}`);
      return true;
    } catch (error) {
      console.log(`Error filling theme details: ${error.message}`);

      // Try to close the theme editor
      await page.keyboard.press('Escape');
      return false;
    }
  } catch (error) {
    console.log(`Error creating custom connection theme: ${error.message}`);
    await takeScreenshot(page, 'create-theme-error');
    return false;
  }
}
