{"name": "powerflow-web-designer-wrapper", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cd powerflow-web-designer && npm run dev", "build": "cd powerflow-web-designer && npm run build", "preview": "cd powerflow-web-designer && npm run preview", "lint": "cd powerflow-web-designer && npm run lint", "format": "cd powerflow-web-designer && npm run format", "test": "cd powerflow-web-designer && npm run test", "test:watch": "cd powerflow-web-designer && npm run test:watch", "test:coverage": "cd powerflow-web-designer && npm run test:coverage", "test:e2e": "cd powerflow-web-designer && npm run test:e2e", "test:e2e:ui": "cd powerflow-web-designer && npm run test:e2e:ui", "test:e2e:debug": "cd powerflow-web-designer && npm run test:e2e:debug", "test:e2e:report": "cd powerflow-web-designer && npm run test:e2e:report", "test:e2e:hierarchical": "cd powerflow-web-designer && npm run test:e2e:hierarchical"}}