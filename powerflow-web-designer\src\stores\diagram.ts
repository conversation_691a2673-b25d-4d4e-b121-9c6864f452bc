import { defineStore } from 'pinia';
import type { Diagram, DiagramLayer, DiagramGroup } from '../types/diagram';
import {
  createDiagram,
  addSymbolInstance,
  addConnection,
  addTextElement,
  removeTextElement,
  createDiagramLayer,
  createDiagramGroup
} from '../types/diagram';
import type { SymbolInstance } from '../types/symbolInstance';
import { createSymbolInstance } from '../types/symbolInstance';
import type { Connection, ConnectionRoutingOptions, ConnectionStyle } from '../types/connection';
import {
  createConnection,
  ConnectionType,
  ConnectionLineType
} from '../types/connection';
import type { SymbolDefinition, Position, Size } from '../types/symbol';
import type { TextElement, TextAlignment } from '../types/textElement';
import { createTextElement } from '../types/textElement';
import { symbolLibrary, getSymbolDefinition as getHardcodedSymbolDefinition } from '../utils/symbolLibrary';
import { getAllSymbols, getSymbolDefinition as getSymbolDefinitionAsync } from '../utils/symbolLoader';
import { AlignmentType, DistributionType, alignSymbols, distributeSymbols } from '../utils/alignment';
import { v4 as uuidv4 } from 'uuid';

interface DiagramState {
  currentDiagram: Diagram | null;
  diagrams: Record<string, Diagram>;
  symbolLibrary: SymbolDefinition[];
  symbolLibraryLoaded: boolean;
  modified: boolean;
  undoStack: Diagram[];
  redoStack: Diagram[];
  maxUndoStackSize: number;
  showAlignmentGuides: boolean;
}

export const useDiagramStore = defineStore('diagram', {
  state: (): DiagramState => ({
    currentDiagram: null,
    diagrams: {},
    symbolLibrary: symbolLibrary,
    symbolLibraryLoaded: false,
    modified: false,
    undoStack: [],
    redoStack: [],
    maxUndoStackSize: 20,
    showAlignmentGuides: true,
  }),

  actions: {
    // Load the complete symbol library (hardcoded + JSON)
    async loadSymbolLibrary() {
      try {
        const allSymbols = await getAllSymbols();
        this.symbolLibrary = allSymbols;
        this.symbolLibraryLoaded = true;
        console.log(`Loaded ${allSymbols.length} symbols total`);
      } catch (error) {
        console.error('Failed to load symbol library:', error);
        // Fallback to hardcoded symbols only
        this.symbolLibrary = symbolLibrary;
        this.symbolLibraryLoaded = true;
      }
    },

    // Get a symbol definition by ID (synchronous for loaded symbols)
    getSymbolDefinition(id: string): SymbolDefinition | undefined {
      return this.symbolLibrary.find(symbol => symbol.id === id);
    },

    // Save the current state to the undo stack
    saveToUndoStack() {
      if (!this.currentDiagram) return;

      // Create a deep copy of the current diagram
      const diagramCopy = JSON.parse(JSON.stringify(this.currentDiagram));

      // Add to undo stack
      this.undoStack.push(diagramCopy);

      // Limit the undo stack size
      if (this.undoStack.length > this.maxUndoStackSize) {
        this.undoStack.shift();
      }

      // Clear redo stack when a new action is performed
      this.redoStack = [];
    },

    // Undo the last action
    undo() {
      if (!this.currentDiagram || this.undoStack.length === 0) return false;

      // Save current state to redo stack
      const currentState = JSON.parse(JSON.stringify(this.currentDiagram));
      this.redoStack.push(currentState);

      // Restore the previous state
      const previousState = this.undoStack.pop();
      if (previousState) {
        this.currentDiagram = previousState;
        this.diagrams[previousState.id] = previousState;
        this.modified = true;
        return true;
      }

      return false;
    },

    // Redo the last undone action
    redo() {
      if (!this.currentDiagram || this.redoStack.length === 0) return false;

      // Save current state to undo stack
      const currentState = JSON.parse(JSON.stringify(this.currentDiagram));
      this.undoStack.push(currentState);

      // Restore the next state
      const nextState = this.redoStack.pop();
      if (nextState) {
        this.currentDiagram = nextState;
        this.diagrams[nextState.id] = nextState;
        this.modified = true;
        return true;
      }

      return false;
    },

    // Create a new diagram
    createDiagram(name: string, description: string = '') {
      // Clear undo/redo stacks
      this.undoStack = [];
      this.redoStack = [];

      const id = uuidv4();
      const diagram = createDiagram(id, name, description);
      this.diagrams[id] = diagram;
      this.currentDiagram = diagram;
      this.modified = false;
      return id;
    },

    // Load an existing diagram
    loadDiagram(id: string) {
      if (this.diagrams[id]) {
        this.currentDiagram = this.diagrams[id];
        this.modified = false;
        return true;
      }
      return false;
    },

    // Add a symbol instance to the current diagram
    addSymbolInstance(definitionId: string, x: number, y: number, layerId?: string) {
      if (!this.currentDiagram) return null;

      // Save current state to undo stack
      this.saveToUndoStack();

      const definition = this.getSymbolDefinition(definitionId);
      if (!definition) return null;

      const id = uuidv4();
      const instance = createSymbolInstance(
        id,
        definition,
        { x, y },
        0, // rotation
        1, // scale
        {} // bindings
      );

      this.currentDiagram = addSymbolInstance(this.currentDiagram, instance, layerId);
      this.modified = true;

      return id;
    },

    // Add a connection between two symbol instances
    addConnection(
      sourceSymbolId: string,
      sourcePointId: string,
      targetSymbolId: string,
      targetPointId: string,
      type: ConnectionType = ConnectionType.CABLE,
      lineType: ConnectionLineType = ConnectionLineType.SMART, // Changed default to SMART for better routing
      layerId?: string,
      style: Partial<ConnectionStyle> = {},
      label?: string,
      routingOptions?: Partial<ConnectionRoutingOptions>
    ) {
      if (!this.currentDiagram) return null;

      // Save current state to undo stack
      this.saveToUndoStack();

      const id = uuidv4();
      const connection = createConnection(
        id,
        type,
        { symbolInstanceId: sourceSymbolId, connectionPointId: sourcePointId },
        { symbolInstanceId: targetSymbolId, connectionPointId: targetPointId },
        lineType,
        [], // waypoints
        [], // controlPoints
        style,
        label,
        routingOptions
      );

      this.currentDiagram = addConnection(this.currentDiagram, connection, layerId);
      this.modified = true;

      return id;
    },

    // Add a text element to the current diagram
    addTextElement(content: string, x: number, y: number, layerId?: string, style?: Partial<TextElement['style']>) {
      if (!this.currentDiagram) return null;

      // Save current state to undo stack
      this.saveToUndoStack();

      const id = uuidv4();
      const textElement = createTextElement(
        id,
        content,
        { x, y },
        TextAlignment.LEFT,
        style,
        0, // rotation
        false // multiline
      );

      this.currentDiagram = addTextElement(this.currentDiagram, textElement, layerId);
      this.modified = true;

      return id;
    },

    // Update a text element
    updateTextElement(textElementId: string, updates: Partial<TextElement>) {
      if (!this.currentDiagram) return false;

      const textElement = this.currentDiagram.textElements[textElementId];
      if (!textElement) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Update the text element
      this.currentDiagram.textElements[textElementId] = {
        ...textElement,
        ...updates,
        style: updates.style ? { ...textElement.style, ...updates.style } : textElement.style,
      };

      this.modified = true;
      return true;
    },

    // Delete a text element
    deleteTextElement(textElementId: string) {
      if (!this.currentDiagram) return false;

      const textElement = this.currentDiagram.textElements[textElementId];
      if (!textElement) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      this.currentDiagram = removeTextElement(this.currentDiagram, textElementId);
      this.modified = true;

      return true;
    },

    // Select text element
    selectTextElement(textElementId: string, multiSelect: boolean = false) {
      if (!this.currentDiagram) return;

      if (!multiSelect) {
        // Clear other selections
        this.currentDiagram.selectedSymbolIds = [];
        this.currentDiagram.selectedConnectionIds = [];
        this.currentDiagram.selectedGroupIds = [];
        this.currentDiagram.selectedTextElementIds = [];
      }

      // Add to selection if not already selected
      if (!this.currentDiagram.selectedTextElementIds.includes(textElementId)) {
        this.currentDiagram.selectedTextElementIds.push(textElementId);
      }
    },

    // Deselect text element
    deselectTextElement(textElementId: string) {
      if (!this.currentDiagram) return;

      this.currentDiagram.selectedTextElementIds = this.currentDiagram.selectedTextElementIds.filter(
        id => id !== textElementId
      );
    },

    // Update diagram name
    setDiagramName(name: string) {
      if (!this.currentDiagram) return;

      // Save current state to undo stack
      this.saveToUndoStack();

      this.currentDiagram = {
        ...this.currentDiagram,
        name,
        modified: new Date(),
      };

      this.diagrams[this.currentDiagram.id] = this.currentDiagram;
      this.modified = true;
    },

    // Save the current diagram
    saveDiagram() {
      if (!this.currentDiagram) return false;

      // Update the modified timestamp
      this.currentDiagram.modified = new Date();

      // Save to diagrams object
      this.diagrams[this.currentDiagram.id] = this.currentDiagram;

      // Save to localStorage
      this.saveDiagramsToLocalStorage();

      this.modified = false;

      return true;
    },

    // Save diagrams to localStorage
    saveDiagramsToLocalStorage() {
      try {
        localStorage.setItem('powerflow-diagrams', JSON.stringify(this.diagrams));
        return true;
      } catch (error) {
        console.error('Failed to save diagrams to localStorage:', error);
        return false;
      }
    },

    // Load diagrams from localStorage
    loadDiagramsFromLocalStorage() {
      try {
        const storedDiagrams = localStorage.getItem('powerflow-diagrams');
        if (storedDiagrams) {
          const diagrams = JSON.parse(storedDiagrams);

          // Convert date strings to Date objects
          Object.values(diagrams).forEach((diagram: any) => {
            diagram.created = new Date(diagram.created);
            diagram.modified = new Date(diagram.modified);
          });

          this.diagrams = diagrams;
          return true;
        }
        return false;
      } catch (error) {
        console.error('Failed to load diagrams from localStorage:', error);
        return false;
      }
    },

    // Delete selected symbols, connections, and groups
    deleteSelected() {
      if (!this.currentDiagram) return false;

      // Check if there's anything selected
      if (
        this.currentDiagram.selectedSymbolIds.length === 0 &&
        this.currentDiagram.selectedConnectionIds.length === 0 &&
        this.currentDiagram.selectedTextElementIds.length === 0 &&
        this.currentDiagram.selectedGroupIds.length === 0
      ) {
        return false;
      }

      // Save current state to undo stack
      this.saveToUndoStack();

      // Delete selected groups
      if (this.currentDiagram.selectedGroupIds.length > 0) {
        // Get the selected group IDs
        const groupIds = [...this.currentDiagram.selectedGroupIds];

        // Remove the groups
        groupIds.forEach(id => {
          delete this.currentDiagram!.groups[id];
        });
      }

      // Delete selected symbols
      if (this.currentDiagram.selectedSymbolIds.length > 0) {
        // Get the selected symbol IDs
        const symbolIds = [...this.currentDiagram.selectedSymbolIds];

        // Remove the symbols from all layers
        this.currentDiagram.layers.forEach(layer => {
          layer.symbolInstanceIds = layer.symbolInstanceIds.filter(
            id => !symbolIds.includes(id)
          );
        });

        // Remove the symbols from all groups
        Object.values(this.currentDiagram.groups).forEach(group => {
          // Remove the symbols from the group
          group.symbolInstanceIds = group.symbolInstanceIds.filter(
            id => !symbolIds.includes(id)
          );

          // If the group is now empty, remove it
          if (group.symbolInstanceIds.length === 0) {
            delete this.currentDiagram!.groups[group.id];
          } else {
            // Recalculate group bounds
            this.recalculateGroupBounds(group.id);
          }
        });

        // Remove the symbols from the instances map
        symbolIds.forEach(id => {
          delete this.currentDiagram!.symbolInstances[id];
        });

        // Also delete any connections that use these symbols
        const connectionsToDelete: string[] = [];

        Object.entries(this.currentDiagram.connections).forEach(([id, connection]) => {
          if (
            symbolIds.includes(connection.source.symbolInstanceId) ||
            symbolIds.includes(connection.target.symbolInstanceId)
          ) {
            connectionsToDelete.push(id);
          }
        });

        // Remove the connections from all layers
        this.currentDiagram.layers.forEach(layer => {
          layer.connectionIds = layer.connectionIds.filter(
            id => !connectionsToDelete.includes(id)
          );
        });

        // Remove the connections from the connections map
        connectionsToDelete.forEach(id => {
          delete this.currentDiagram!.connections[id];
        });
      }

      // Delete selected connections
      if (this.currentDiagram.selectedConnectionIds.length > 0) {
        // Get the selected connection IDs
        const connectionIds = [...this.currentDiagram.selectedConnectionIds];

        // Remove the connections from all layers
        this.currentDiagram.layers.forEach(layer => {
          layer.connectionIds = layer.connectionIds.filter(
            id => !connectionIds.includes(id)
          );
        });

        // Remove the connections from the connections map
        connectionIds.forEach(id => {
          delete this.currentDiagram!.connections[id];
        });
      }

      // Delete selected text elements
      if (this.currentDiagram.selectedTextElementIds.length > 0) {
        // Get the selected text element IDs
        const textElementIds = [...this.currentDiagram.selectedTextElementIds];

        // Remove the text elements from all layers
        this.currentDiagram.layers.forEach(layer => {
          if (layer.textElementIds) {
            layer.textElementIds = layer.textElementIds.filter(
              id => !textElementIds.includes(id)
            );
          }
        });

        // Remove the text elements from the text elements map
        textElementIds.forEach(id => {
          delete this.currentDiagram!.textElements[id];
        });
      }

      // Clear selection
      this.currentDiagram.selectedSymbolIds = [];
      this.currentDiagram.selectedConnectionIds = [];
      this.currentDiagram.selectedTextElementIds = [];
      this.currentDiagram.selectedGroupIds = [];

      // Mark as modified
      this.modified = true;

      return true;
    },

    // Select all symbols and connections
    selectAll() {
      if (!this.currentDiagram) return false;

      // Get all visible symbol IDs
      const symbolIds: string[] = [];
      this.currentDiagram.layers.forEach(layer => {
        if (layer.visible) {
          symbolIds.push(...layer.symbolInstanceIds);
        }
      });

      // Get all visible connection IDs
      const connectionIds: string[] = [];
      this.currentDiagram.layers.forEach(layer => {
        if (layer.visible) {
          connectionIds.push(...layer.connectionIds);
        }
      });

      // Get all visible text element IDs
      const textElementIds: string[] = [];
      this.currentDiagram.layers.forEach(layer => {
        if (layer.visible && layer.textElementIds) {
          textElementIds.push(...layer.textElementIds);
        }
      });

      // Update selection
      this.currentDiagram.selectedSymbolIds = symbolIds;
      this.currentDiagram.selectedConnectionIds = connectionIds;
      this.currentDiagram.selectedTextElementIds = textElementIds;

      return true;
    },

    // Align selected symbols
    alignSymbols(alignmentType: AlignmentType) {
      if (!this.currentDiagram) return;

      // Get selected symbols
      const selectedSymbols = Object.values(this.currentDiagram.symbolInstances)
        .filter(symbol => this.currentDiagram!.selectedSymbolIds.includes(symbol.id));

      if (selectedSymbols.length < 2) return;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Calculate new positions
      const newPositions = alignSymbols(selectedSymbols, alignmentType);

      // Update symbol positions
      for (const { id, position } of newPositions) {
        this.currentDiagram.symbolInstances[id].position = position;
      }

      this.modified = true;
    },

    // Distribute selected symbols
    distributeSymbols(distributionType: DistributionType) {
      if (!this.currentDiagram) return;

      // Get selected symbols
      const selectedSymbols = Object.values(this.currentDiagram.symbolInstances)
        .filter(symbol => this.currentDiagram!.selectedSymbolIds.includes(symbol.id));

      if (selectedSymbols.length < 3) return;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Calculate new positions
      const newPositions = distributeSymbols(selectedSymbols, distributionType);

      // Update symbol positions
      for (const { id, position } of newPositions) {
        this.currentDiagram.symbolInstances[id].position = position;
      }

      this.modified = true;
    },

    // Set alignment guides visibility
    setAlignmentGuidesVisibility(visible: boolean) {
      this.showAlignmentGuides = visible;
    },

    // Update grid settings
    updateGridSettings(settings: Partial<DiagramGrid>) {
      if (!this.currentDiagram) return;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Update grid settings
      this.currentDiagram.grid = {
        ...this.currentDiagram.grid,
        ...settings,
      };

      this.modified = true;
    },

    // Update connection
    updateConnection(connection: Connection) {
      if (!this.currentDiagram) return;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Update the connection
      this.currentDiagram.connections[connection.id] = connection;

      this.modified = true;
    },

    // Add a new layer to the diagram
    addLayer(name: string) {
      if (!this.currentDiagram) return null;

      // Save current state to undo stack
      this.saveToUndoStack();

      const id = uuidv4();
      const layer = createDiagramLayer(id, name);

      this.currentDiagram.layers.push(layer);
      this.modified = true;

      return id;
    },

    // Delete a layer from the diagram
    deleteLayer(layerId: string) {
      if (!this.currentDiagram) return false;

      // Check if this is the only layer
      if (this.currentDiagram.layers.length <= 1) {
        return false;
      }

      // Save current state to undo stack
      this.saveToUndoStack();

      // Find the layer index
      const layerIndex = this.currentDiagram.layers.findIndex(
        layer => layer.id === layerId
      );

      if (layerIndex === -1) return false;

      // Get the layer
      const layer = this.currentDiagram.layers[layerIndex];

      // Move all symbols and connections to the first layer
      const firstLayerId = this.currentDiagram.layers[0].id;
      if (firstLayerId !== layerId) {
        const firstLayer = this.currentDiagram.layers[0];
        firstLayer.symbolInstanceIds.push(...layer.symbolInstanceIds);
        firstLayer.connectionIds.push(...layer.connectionIds);
      } else {
        // If deleting the first layer, move to the second layer
        const secondLayer = this.currentDiagram.layers[1];
        secondLayer.symbolInstanceIds.push(...layer.symbolInstanceIds);
        secondLayer.connectionIds.push(...layer.connectionIds);
      }

      // Remove the layer
      this.currentDiagram.layers.splice(layerIndex, 1);
      this.modified = true;

      return true;
    },

    // Rename a layer
    renameLayer(layerId: string, newName: string) {
      if (!this.currentDiagram) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Find the layer
      const layer = this.currentDiagram.layers.find(
        layer => layer.id === layerId
      );

      if (!layer) return false;

      // Update the name
      layer.name = newName;
      this.modified = true;

      return true;
    },

    // Set layer visibility
    setLayerVisibility(layerId: string, visible: boolean) {
      if (!this.currentDiagram) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Find the layer
      const layer = this.currentDiagram.layers.find(
        layer => layer.id === layerId
      );

      if (!layer) return false;

      // Update visibility
      layer.visible = visible;
      this.modified = true;

      return true;
    },

    // Set layer lock state
    setLayerLocked(layerId: string, locked: boolean) {
      if (!this.currentDiagram) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Find the layer
      const layer = this.currentDiagram.layers.find(
        layer => layer.id === layerId
      );

      if (!layer) return false;

      // Update lock state
      layer.locked = locked;
      this.modified = true;

      return true;
    },

    // Move a symbol to a different layer
    moveSymbolToLayer(symbolId: string, targetLayerId: string) {
      if (!this.currentDiagram) return false;

      // Check if the symbol exists
      if (!this.currentDiagram.symbolInstances[symbolId]) return false;

      // Check if the target layer exists
      const targetLayer = this.currentDiagram.layers.find(
        layer => layer.id === targetLayerId
      );

      if (!targetLayer) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Remove the symbol from its current layer
      this.currentDiagram.layers.forEach(layer => {
        layer.symbolInstanceIds = layer.symbolInstanceIds.filter(
          id => id !== symbolId
        );
      });

      // Add the symbol to the target layer
      targetLayer.symbolInstanceIds.push(symbolId);
      this.modified = true;

      return true;
    },

    // Move a connection to a different layer
    moveConnectionToLayer(connectionId: string, targetLayerId: string) {
      if (!this.currentDiagram) return false;

      // Check if the connection exists
      if (!this.currentDiagram.connections[connectionId]) return false;

      // Check if the target layer exists
      const targetLayer = this.currentDiagram.layers.find(
        layer => layer.id === targetLayerId
      );

      if (!targetLayer) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Remove the connection from its current layer
      this.currentDiagram.layers.forEach(layer => {
        layer.connectionIds = layer.connectionIds.filter(
          id => id !== connectionId
        );
      });

      // Add the connection to the target layer
      targetLayer.connectionIds.push(connectionId);
      this.modified = true;

      return true;
    },

    // Create a group from selected symbols
    createGroup() {
      if (!this.currentDiagram) return null;

      // Get selected symbols
      const selectedSymbolIds = [...this.currentDiagram.selectedSymbolIds];
      if (selectedSymbolIds.length < 2) return null;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Calculate group bounds
      const symbols = selectedSymbolIds.map(id => this.currentDiagram!.symbolInstances[id]);
      const positions = symbols.map(s => s.position);
      const minX = Math.min(...positions.map(p => p.x));
      const minY = Math.min(...positions.map(p => p.y));
      const maxX = Math.max(...positions.map(p => p.x + 100)); // Assuming 100x100 symbols
      const maxY = Math.max(...positions.map(p => p.y + 100)); // Assuming 100x100 symbols

      // Create a new group
      const id = uuidv4();
      const group = createDiagramGroup(
        id,
        `Group ${Object.keys(this.currentDiagram.groups).length + 1}`,
        selectedSymbolIds,
        { x: minX, y: minY },
        { width: maxX - minX, height: maxY - minY }
      );

      // Add the group to the diagram
      this.currentDiagram.groups[id] = group;

      // Update selection
      this.currentDiagram.selectedSymbolIds = [];
      this.currentDiagram.selectedGroupIds = [id];
      this.modified = true;

      return id;
    },

    // Ungroup a group
    ungroup(groupId: string) {
      if (!this.currentDiagram) return false;

      // Check if the group exists
      const group = this.currentDiagram.groups[groupId];
      if (!group) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Select all symbols in the group
      this.currentDiagram.selectedSymbolIds = [...group.symbolInstanceIds];
      this.currentDiagram.selectedGroupIds = [];

      // Remove the group
      delete this.currentDiagram.groups[groupId];
      this.modified = true;

      return true;
    },

    // Update group position
    updateGroupPosition(groupId: string, position: Position) {
      if (!this.currentDiagram) return false;

      // Check if the group exists
      const group = this.currentDiagram.groups[groupId];
      if (!group) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Calculate the delta
      const dx = position.x - group.position.x;
      const dy = position.y - group.position.y;

      // Update group position
      group.position = position;

      // Update positions of all symbols in the group
      group.symbolInstanceIds.forEach(symbolId => {
        const symbol = this.currentDiagram!.symbolInstances[symbolId];
        if (symbol) {
          symbol.position.x += dx;
          symbol.position.y += dy;
        }
      });

      this.modified = true;
      return true;
    },

    // Update group size
    updateGroupSize(groupId: string, size: Size) {
      if (!this.currentDiagram) return false;

      // Check if the group exists
      const group = this.currentDiagram.groups[groupId];
      if (!group) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Update group size
      group.size = size;
      this.modified = true;

      return true;
    },

    // Update group rotation
    updateGroupRotation(groupId: string, rotation: number) {
      if (!this.currentDiagram) return false;

      // Check if the group exists
      const group = this.currentDiagram.groups[groupId];
      if (!group) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Update group rotation
      group.rotation = rotation;

      // TODO: Update rotation of all symbols in the group
      // This would require more complex calculations

      this.modified = true;
      return true;
    },

    // Rotate selected symbols by 90 degrees
    rotateSelectedSymbols(clockwise: boolean = true) {
      if (!this.currentDiagram) return false;

      const selectedSymbolIds = this.currentDiagram.selectedSymbolIds;
      if (selectedSymbolIds.length === 0) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      const rotationDelta = clockwise ? 90 : -90;

      // Rotate each selected symbol
      selectedSymbolIds.forEach(symbolId => {
        const symbol = this.currentDiagram!.symbolInstances[symbolId];
        if (symbol) {
          // Update rotation (normalize to 0-360 range)
          symbol.rotation = (symbol.rotation + rotationDelta + 360) % 360;
        }
      });

      this.modified = true;
      return true;
    },

    // Rotate a single symbol by 90 degrees
    rotateSymbol(symbolId: string, clockwise: boolean = true) {
      if (!this.currentDiagram) return false;

      const symbol = this.currentDiagram.symbolInstances[symbolId];
      if (!symbol) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      const rotationDelta = clockwise ? 90 : -90;

      // Update rotation (normalize to 0-360 range)
      symbol.rotation = (symbol.rotation + rotationDelta + 360) % 360;

      this.modified = true;
      return true;
    },

    // Update connection properties with validation
    updateConnectionWithValidation(connection: Connection) {
      if (!this.currentDiagram) return false;

      // Check if the connection exists
      if (!this.currentDiagram.connections[connection.id]) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Update the connection
      this.currentDiagram.connections[connection.id] = {
        ...connection
      };

      this.modified = true;
      return true;
    },

    // Update symbol properties
    updateSymbolProperties(symbolId: string, properties: Record<string, any>) {
      if (!this.currentDiagram) return false;

      // Check if the symbol exists
      const symbol = this.currentDiagram.symbolInstances[symbolId];
      if (!symbol) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Update the symbol properties
      this.currentDiagram.symbolInstances[symbolId] = {
        ...symbol,
        properties: {
          ...properties
        }
      };

      this.modified = true;
      return true;
    },

    // Update symbol instance (more general method)
    updateSymbolInstance(symbolId: string, updates: Partial<SymbolInstance>) {
      if (!this.currentDiagram) return false;

      // Check if the symbol exists
      const symbol = this.currentDiagram.symbolInstances[symbolId];
      if (!symbol) return false;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Update the symbol instance
      this.currentDiagram.symbolInstances[symbolId] = {
        ...symbol,
        ...updates,
        // Merge properties if both exist
        properties: updates.properties
          ? { ...symbol.properties, ...updates.properties }
          : symbol.properties,
      };

      this.modified = true;
      return true;
    },

    // Add a symbol to a group
    addSymbolToGroup(symbolId: string, groupId: string) {
      if (!this.currentDiagram) return false;

      // Check if the symbol and group exist
      const symbol = this.currentDiagram.symbolInstances[symbolId];
      const group = this.currentDiagram.groups[groupId];
      if (!symbol || !group) return false;

      // Check if the symbol is already in the group
      if (group.symbolInstanceIds.includes(symbolId)) return true;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Add the symbol to the group
      group.symbolInstanceIds.push(symbolId);

      // Recalculate group bounds
      this.recalculateGroupBounds(groupId);

      this.modified = true;
      return true;
    },

    // Remove a symbol from a group
    removeSymbolFromGroup(symbolId: string, groupId: string) {
      if (!this.currentDiagram) return false;

      // Check if the group exists
      const group = this.currentDiagram.groups[groupId];
      if (!group) return false;

      // Check if the symbol is in the group
      if (!group.symbolInstanceIds.includes(symbolId)) return true;

      // Save current state to undo stack
      this.saveToUndoStack();

      // Remove the symbol from the group
      group.symbolInstanceIds = group.symbolInstanceIds.filter(id => id !== symbolId);

      // If the group is now empty, remove it
      if (group.symbolInstanceIds.length === 0) {
        delete this.currentDiagram.groups[groupId];
      } else {
        // Recalculate group bounds
        this.recalculateGroupBounds(groupId);
      }

      this.modified = true;
      return true;
    },

    // Recalculate group bounds
    recalculateGroupBounds(groupId: string) {
      if (!this.currentDiagram) return false;

      // Check if the group exists
      const group = this.currentDiagram.groups[groupId];
      if (!group) return false;

      // Get all symbols in the group
      const symbols = group.symbolInstanceIds.map(id => this.currentDiagram!.symbolInstances[id]);
      if (symbols.length === 0) return false;

      // Calculate group bounds
      const positions = symbols.map(s => s.position);
      const minX = Math.min(...positions.map(p => p.x));
      const minY = Math.min(...positions.map(p => p.y));
      const maxX = Math.max(...positions.map(p => p.x + 100)); // Assuming 100x100 symbols
      const maxY = Math.max(...positions.map(p => p.y + 100)); // Assuming 100x100 symbols

      // Update group position and size
      group.position = { x: minX, y: minY };
      group.size = { width: maxX - minX, height: maxY - minY };

      return true;
    },
  },

  getters: {
    // Get the current diagram name
    currentDiagramName: (state) => {
      return state.currentDiagram ? state.currentDiagram.name : 'No Diagram';
    },

    // Get the display name with modification indicator
    displayName: (state) => {
      if (!state.currentDiagram) return 'No Diagram';
      return state.modified ? `${state.currentDiagram.name} *` : state.currentDiagram.name;
    },

    // Get all symbol instances in the current diagram
    symbolInstances: (state) => {
      return state.currentDiagram ? state.currentDiagram.symbolInstances : {};
    },

    // Get all connections in the current diagram
    connections: (state) => {
      return state.currentDiagram ? state.currentDiagram.connections : {};
    },

    // Get all available diagrams
    availableDiagrams: (state) => {
      return Object.values(state.diagrams).map(diagram => ({
        id: diagram.id,
        name: diagram.name,
        description: diagram.description,
        created: diagram.created,
        modified: diagram.modified,
      }));
    },

    // Get alignment guides visibility
    alignmentGuidesVisible: (state) => state.showAlignmentGuides,
  },
});
