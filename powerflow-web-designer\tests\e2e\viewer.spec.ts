import { test, expect } from '@playwright/test';
import { 
  navigateToViewer, 
  navigateToEditor, 
  createNewDiagram, 
  addSymbolToDiagram 
} from './helpers';

test.describe('Viewer Functionality', () => {
  test('should load the viewer page correctly', async ({ page }) => {
    await navigateToViewer(page);
    
    // Check that the viewer page is loaded
    await expect(page.locator('.viewer-header')).toBeVisible();
    await expect(page.locator('.diagram-list')).toBeVisible();
  });

  test('should display available diagrams', async ({ page }) => {
    // First create a diagram in the editor
    await createNewDiagram(page, 'Viewer Test Diagram');
    
    // Add a symbol to make it interesting
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Save the diagram
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Navigate to the viewer
    await navigateToViewer(page);
    
    // Check that the diagram is in the list
    await expect(page.locator('.diagram-list')).toContainText('Viewer Test Diagram');
  });

  test('should open a diagram in view mode', async ({ page }) => {
    // First create a diagram in the editor
    await createNewDiagram(page, 'View Mode Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Save the diagram
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Navigate to the viewer
    await navigateToViewer(page);
    
    // Click on the diagram in the list
    await page.locator('.diagram-list').getByText('View Mode Test Diagram').click();
    
    // Check that the diagram is displayed in view mode
    await expect(page.locator('.diagram-canvas')).toBeVisible();
    await expect(page.locator('.symbol-instance')).toBeVisible();
    
    // Verify that editing controls are not available
    await expect(page.getByRole('button', { name: 'Symbols' })).not.toBeVisible();
  });

  test('should zoom in and out of the diagram', async ({ page }) => {
    // First create a diagram in the editor
    await createNewDiagram(page, 'Zoom Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Save the diagram
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Navigate to the viewer
    await navigateToViewer(page);
    
    // Click on the diagram in the list
    await page.locator('.diagram-list').getByText('Zoom Test Diagram').click();
    
    // Get the initial scale of the diagram
    const initialScale = await page.evaluate(() => {
      const canvas = document.querySelector('.diagram-canvas');
      if (canvas) {
        const transform = window.getComputedStyle(canvas).transform;
        const matrix = transform.match(/matrix\(([^)]+)\)/);
        if (matrix) {
          const values = matrix[1].split(',');
          return parseFloat(values[0]);
        }
      }
      return 1;
    });
    
    // Click the zoom in button
    await page.getByRole('button', { name: 'Zoom In' }).click();
    
    // Get the new scale
    const zoomedInScale = await page.evaluate(() => {
      const canvas = document.querySelector('.diagram-canvas');
      if (canvas) {
        const transform = window.getComputedStyle(canvas).transform;
        const matrix = transform.match(/matrix\(([^)]+)\)/);
        if (matrix) {
          const values = matrix[1].split(',');
          return parseFloat(values[0]);
        }
      }
      return 1;
    });
    
    // Check that the scale increased
    expect(zoomedInScale).toBeGreaterThan(initialScale);
    
    // Click the zoom out button
    await page.getByRole('button', { name: 'Zoom Out' }).click();
    
    // Get the new scale
    const zoomedOutScale = await page.evaluate(() => {
      const canvas = document.querySelector('.diagram-canvas');
      if (canvas) {
        const transform = window.getComputedStyle(canvas).transform;
        const matrix = transform.match(/matrix\(([^)]+)\)/);
        if (matrix) {
          const values = matrix[1].split(',');
          return parseFloat(values[0]);
        }
      }
      return 1;
    });
    
    // Check that the scale decreased
    expect(zoomedOutScale).toBeLessThan(zoomedInScale);
  });
});
