{"version": "2.0.0", "tasks": [{"label": "🚀 启动开发服务器", "type": "npm", "script": "dev", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "启动Vite开发服务器，支持热重载"}, {"label": "🔧 构建生产版本", "type": "npm", "script": "build", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$tsc", "$eslint-stylish"], "detail": "构建生产环境的优化版本"}, {"label": "👀 预览构建结果", "type": "npm", "script": "preview", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "预览构建后的生产版本"}, {"label": "🧪 运行单元测试", "type": "npm", "script": "test", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "运行Vitest单元测试"}, {"label": "🔍 监视模式测试", "type": "npm", "script": "test:watch", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "以监视模式运行测试，文件变化时自动重新运行", "isBackground": true}, {"label": "📊 测试覆盖率报告", "type": "npm", "script": "test:coverage", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "生成测试覆盖率报告"}, {"label": "🎭 运行E2E测试", "type": "npm", "script": "test:e2e", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "运行Playwright端到端测试"}, {"label": "🎭 E2E测试UI模式", "type": "npm", "script": "test:e2e:ui", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "以UI模式运行Playwright测试"}, {"label": "🐛 E2E调试模式", "type": "npm", "script": "test:e2e:debug", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "以调试模式运行Playwright测试"}, {"label": "📈 查看E2E测试报告", "type": "npm", "script": "test:e2e:report", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "查看Playwright测试报告"}, {"label": "🔧 代码检查", "type": "npm", "script": "lint", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$eslint-stylish"], "detail": "运行ESLint代码检查并自动修复"}, {"label": "💅 代码格式化", "type": "npm", "script": "format", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "使用Prettier格式化代码"}, {"label": "🧹 清理并重新安装依赖", "type": "shell", "command": "npm", "args": ["ci"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "清理node_modules并重新安装依赖"}, {"label": "🔄 重启开发服务器", "dependsOrder": "sequence", "dependsOn": ["🛑 停止所有任务", "🚀 启动开发服务器"], "group": "build", "detail": "停止当前开发服务器并重新启动"}, {"label": "🛑 停止所有任务", "type": "shell", "command": "echo", "args": ["停止所有后台任务..."], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": false}, "detail": "停止所有正在运行的后台任务"}]}