<template>
  <v-group
    :config="{
      x: instance.position.x,
      y: instance.position.y,
      rotation: instance.rotation,
      scaleX: instance.scale,
      scaleY: instance.scale,
      draggable: !isLocked && !readOnly && !isConnecting,
      id: instance.id,
    }"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
    @dragmove="handleDragMove"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- SVG for the symbol -->
    <template v-if="useSvgImage">
      <!-- Use v-image for complex SVG content -->
      <v-image
        :config="{
          image: svgImage,
          width: definition.dimensions.width,
          height: definition.dimensions.height,
        }"
      />
    </template>
    <template v-else>
      <!-- Fallback to v-path for simple path data -->
      <v-path
        :config="{
          data: svgPath || 'M0,0 H50 V50 H0 Z',
          fill: getFillColor(),
          stroke: getStrokeColor(),
          strokeWidth: getLineWidth(),
        }"
      />
    </template>

    <!-- Connection points (visible on hover or when selected) -->
    <v-circle
      v-for="point in connectionPoints"
      :key="point.id"
      :config="{
        x: point.position.x,
        y: point.position.y,
        radius: point.isActive ? 12 : (point.isHighlighted ? 10 : (point.isHovered ? 9 : 7)),
        fill: point.isActive ? '#1890ff' : (point.isHighlighted ? '#52c41a' : (point.isHovered ? '#e6f7ff' : '#fff')),
        stroke: point.isActive ? '#096dd9' : (point.isHighlighted ? '#389e0d' : (point.isHovered ? '#1890ff' : '#0096FF')),
        strokeWidth: point.isActive ? 4 : (point.isHighlighted ? 3 : (point.isHovered ? 3 : 2)),
        visible: isHovered || isSelected || point.isActive || point.isHighlighted || isConnecting || isDragConnectionActive,
        listening: true,
        shadowColor: point.isActive ? 'rgba(24, 144, 255, 0.8)' :
                    (point.isHighlighted ? 'rgba(82, 196, 26, 0.8)' :
                    (point.isHovered ? 'rgba(24, 144, 255, 0.5)' : 'transparent')),
        shadowBlur: point.isActive ? 12 : (point.isHighlighted ? 10 : (point.isHovered ? 8 : 0)),
        shadowOffset: { x: 0, y: 0 },
        shadowOpacity: 0.6,
        opacity: point.isActive || point.isHighlighted ? 1 : (point.isHovered ? 0.9 : 0.8),
      }"
      @mousedown="(e) => handleConnectionPointMouseDown(e, point.id)"
      @mouseup="(e) => handleConnectionPointMouseUp(e, point.id)"
      @mouseover="handleConnectionPointMouseOver(point.id)"
      @mouseout="handleConnectionPointMouseOut(point.id)"
    />

    <!-- Enhanced connection point highlighting for busbar symbols during drag operations -->
    <v-circle
      v-if="isBusbarSymbol(definition) && isDragConnectionActive"
      v-for="point in connectionPoints"
      :key="`highlight-${point.id}`"
      :config="{
        x: point.position.x,
        y: point.position.y,
        radius: 15,
        fill: 'rgba(82, 196, 26, 0.2)',
        stroke: '#52c41a',
        strokeWidth: 2,
        visible: true,
        listening: false,
        dash: [4, 4],
        opacity: 0.8,
      }"
    />

    <!-- Label (if any) -->
    <v-text
      v-if="label"
      :config="{
        x: 0,
        y: definition.dimensions.height + 10,
        text: label,
        fontSize: 12,
        fontFamily: 'Arial',
        fill: '#333',
        align: 'center',
        width: definition.dimensions.width,
      }"
    />

    <!-- Value displays -->
    <template v-if="hasValueDisplays">
      <v-group
        v-for="(display, index) in valueDisplays"
        :key="`value-display-${index}`"
        :config="{
          x: display.position.x,
          y: display.position.y,
        }"
      >
        <value-display
          :value="getDisplayValue(display.bindingId)"
          :format="display.format"
          :precision="display.precision"
          :unit="display.unit"
          :font-size="display.fontSize"
          :text-color="display.textColor"
          :show-background="display.showBackground"
          :background-color="display.backgroundColor"
          :background-opacity="display.backgroundOpacity"
          :width="display.width"
          :height="display.height"
        />
      </v-group>
    </template>

    <!-- Trend charts -->
    <template v-if="hasTrendCharts">
      <v-group
        v-for="(chart, index) in trendCharts"
        :key="`trend-chart-${index}`"
        :config="{
          x: chart.position.x,
          y: chart.position.y,
        }"
      >
        <trend-chart
          :data="getHistoricalData(chart.bindingId, chart.maxPoints)"
          :title="chart.title || chart.bindingId"
          :width="chart.width"
          :height="chart.height"
          :line-color="chart.lineColor"
          :fill-color="chart.fillColor"
          :background-color="chart.backgroundColor"
          :background-opacity="chart.backgroundOpacity"
          :text-color="chart.textColor"
          :show-title="chart.showTitle"
          :show-current-value="chart.showCurrentValue"
          :value-format="chart.valueFormat"
          :value-precision="chart.valuePrecision"
          :value-unit="chart.valueUnit"
          :min-value="chart.minValue"
          :max-value="chart.maxValue"
          :auto-scale="chart.autoScale"
        />
      </v-group>
    </template>
  </v-group>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { useSimulationStore } from '@/stores/simulation';
import { SymbolInstance as SymbolInstanceType } from '@/types/symbolInstance';
import { SymbolDefinition, ConnectionPoint, Position } from '@/types/symbol';
import ValueDisplay from './ValueDisplay.vue';
import TrendChart from './TrendChart.vue';
import {
  getBusbarConnectionPoints,
  getBusbarConnectionPointPosition,
  isBusbarSymbol
} from '@/utils/busbarUtils';

// Props
const props = defineProps<{
  instance: SymbolInstanceType;
  isSelected: boolean;
  isLocked: boolean;
  readOnly?: boolean;
  highlightedConnectionPointId?: string | null;
}>();

// Emits
const emit = defineEmits<{
  (e: 'select', id: string): void;
  (e: 'deselect', id: string): void;
  (e: 'move', id: string, x: number, y: number): void;
  (e: 'move-end', id: string): void;
  (e: 'move-multi-element', type: string, id: string, x: number, y: number, index?: number): void;
  (e: 'connection-start', symbolId: string, pointId: string): void;
  (e: 'connection-end', symbolId: string, pointId: string): void;
}>();

// Store
const diagramStore = useDiagramStore();
const simulationStore = useSimulationStore();

// State
const isHovered = ref(false);
const isDragging = ref(false);
const dragStartPosition = ref({ x: 0, y: 0 });
const svgImage = ref<HTMLImageElement | null>(null);
const hoveredConnectionPointId = ref<string | null>(null);
const activeConnectionPointId = ref<string | null>(null);
const isConnecting = ref(false); // 标记是否正在进行连接操作
const isDragConnectionActive = ref(false); // 标记是否有拖拽连接操作正在进行
const multiDragStartPositions = ref(new Map<string, { x: number; y: number }>());

// Value display interface
interface ValueDisplay {
  bindingId: string;
  position: Position;
  format?: string;
  precision?: number;
  unit?: string;
  fontSize?: number;
  textColor?: string;
  showBackground?: boolean;
  backgroundColor?: string;
  backgroundOpacity?: number;
  width?: number;
  height?: number;
}

// Trend chart interface
interface TrendChart {
  bindingId: string;
  position: Position;
  title?: string;
  width?: number;
  height?: number;
  lineColor?: string;
  fillColor?: string;
  backgroundColor?: string;
  backgroundOpacity?: number;
  textColor?: string;
  showTitle?: boolean;
  showCurrentValue?: boolean;
  valueFormat?: string;
  valuePrecision?: number;
  valueUnit?: string;
  maxPoints?: number;
  minValue?: number;
  maxValue?: number;
  autoScale?: boolean;
}

// Computed
const definition = computed<SymbolDefinition>(() => {
  const def = diagramStore.getSymbolDefinition(props.instance.definitionId);
  if (!def) {
    console.error(`Symbol definition not found: ${props.instance.definitionId}`);
    return {
      id: 'error',
      category: 'other' as any,
      name: 'Error',
      svg: '',
      dimensions: { width: 50, height: 50 },
      connectionPoints: [],
      bindingSlots: [],
      properties: {
        fillColor: '#ff0000',
        strokeColor: '#ff0000',
        lineWidth: 1,
      },
    };
  }
  return def;
});

// 检查SVG是否包含复杂元素，需要使用图像渲染
const useSvgImage = computed(() => {
  if (!definition.value || !definition.value.svg) return false;

  const svg = definition.value.svg;

  // 检查是否包含复杂元素
  return svg.includes('<rect') ||
         svg.includes('<circle') ||
         svg.includes('<line') ||
         svg.includes('<text') ||
         svg.includes('<polygon') ||
         (svg.includes('<path') && svg.includes('<svg'));
});

// 提取简单路径数据（当不使用图像时）
const svgPath = computed(() => {
  try {
    // 默认路径（简单矩形）
    const defaultPath = 'M0,0 H50 V50 H0 Z';

    // 确保定义存在并且有svg属性
    if (!definition.value || !definition.value.svg) {
      console.warn('Symbol definition or SVG is missing');
      return defaultPath;
    }

    // 如果使用图像渲染，则不需要路径数据
    if (useSvgImage.value) {
      return defaultPath;
    }

    // 从SVG字符串中提取路径数据
    const svg = definition.value.svg;

    // 尝试查找带有d属性的path元素
    const pathMatch = svg.match(/<path[^>]*d="([^"]*)"[^>]*>/);
    if (pathMatch && pathMatch[1] && pathMatch[1].trim()) {
      // 验证路径数据
      if (pathMatch[1].startsWith('M') || pathMatch[1].startsWith('m')) {
        return pathMatch[1];
      }
    }

    // 如果找不到有效路径，返回默认值
    return defaultPath;
  } catch (error) {
    console.error('Error parsing SVG path:', error);
    return 'M0,0 H50 V50 H0 Z';
  }
});

const connectionPoints = computed(() => {
  if (!definition.value) return [];

  // Use dynamic connection points for busbar symbols
  const points = isBusbarSymbol(definition.value)
    ? getBusbarConnectionPoints(props.instance, definition.value)
    : definition.value.connectionPoints;

  return points.map(point => ({
    id: point.id,
    position: {
      x: point.position.x,
      y: point.position.y
    },
    isHovered: point.id === hoveredConnectionPointId.value,
    isActive: point.id === activeConnectionPointId.value,
    isHighlighted: point.id === props.highlightedConnectionPointId
  }));
});

const label = computed(() => {
  // Get label from bindings if available
  const labelBinding = props.instance.bindings['label'];
  if (labelBinding) {
    return labelBinding.value;
  }
  return '';
});

// Check if the symbol has value displays configured
const hasValueDisplays = computed(() => {
  return props.instance.properties.valueDisplays &&
         Array.isArray(props.instance.properties.valueDisplays) &&
         props.instance.properties.valueDisplays.length > 0;
});

// Get the value displays configuration
const valueDisplays = computed<ValueDisplay[]>(() => {
  if (!hasValueDisplays.value) {
    return [];
  }
  return props.instance.properties.valueDisplays as ValueDisplay[];
});

// Check if the symbol has trend charts configured
const hasTrendCharts = computed(() => {
  return props.instance.properties.trendCharts &&
         Array.isArray(props.instance.properties.trendCharts) &&
         props.instance.properties.trendCharts.length > 0;
});

// Get the trend charts configuration
const trendCharts = computed<TrendChart[]>(() => {
  if (!hasTrendCharts.value) {
    return [];
  }
  return props.instance.properties.trendCharts as TrendChart[];
});

// Check if this symbol is part of a multi-selection
const isMultiSelected = computed(() => {
  if (!diagramStore.currentDiagram) return false;

  const selectedCount =
    diagramStore.currentDiagram.selectedSymbolIds.length +
    diagramStore.currentDiagram.selectedConnectionIds.length +
    diagramStore.currentDiagram.selectedGroupIds.length;

  return selectedCount >= 2 && props.isSelected;
});

// Methods
const getFillColor = () => {
  // Get fill color from instance properties or definition
  return props.instance.properties.fillColor ||
         definition.value.properties.fillColor ||
         '#ffffff';
};

const getStrokeColor = () => {
  // Get stroke color from instance properties or definition
  return props.instance.properties.strokeColor ||
         definition.value.properties.strokeColor ||
         '#000000';
};

const getLineWidth = () => {
  // Get line width from instance properties or definition
  return props.instance.properties.lineWidth ||
         definition.value.properties.lineWidth ||
         1;
};

const handleDragStart = (e: any) => {
  // 如果正在进行连接操作，不触发图元拖动
  if (isConnecting.value) {
    console.log('Ignoring drag start on symbol while connecting');
    e.cancelBubble = true;
    return;
  }

  isDragging.value = true;
  dragStartPosition.value = {
    x: props.instance.position.x,
    y: props.instance.position.y,
  };

  // Select the symbol if not already selected
  if (!props.isSelected) {
    emit('select', props.instance.id);
  }

  // If this symbol is part of a multi-selection, prepare for group dragging
  if (diagramStore.currentDiagram && isMultiSelected.value) {
    // Store initial positions of all selected elements for relative positioning
    multiDragStartPositions.value = new Map();

    // Store positions of selected symbols
    diagramStore.currentDiagram.selectedSymbolIds.forEach(symbolId => {
      const symbol = diagramStore.currentDiagram?.symbolInstances[symbolId];
      if (symbol) {
        multiDragStartPositions.value.set(`symbol-${symbolId}`, {
          x: symbol.position.x,
          y: symbol.position.y
        });
      }
    });

    // Store positions of selected connections (waypoints)
    diagramStore.currentDiagram.selectedConnectionIds.forEach(connectionId => {
      const connection = diagramStore.currentDiagram?.connections[connectionId];
      if (connection && connection.waypoints) {
        connection.waypoints.forEach((waypoint, index) => {
          multiDragStartPositions.value.set(`connection-${connectionId}-waypoint-${index}`, {
            x: waypoint.x,
            y: waypoint.y
          });
        });
      }
    });

    // Store positions of selected groups
    diagramStore.currentDiagram.selectedGroupIds.forEach(groupId => {
      const group = diagramStore.currentDiagram?.groups[groupId];
      if (group) {
        multiDragStartPositions.value.set(`group-${groupId}`, {
          x: group.position.x,
          y: group.position.y
        });
      }
    });
  }
};

const handleDragEnd = (e: any) => {
  isDragging.value = false;

  // Get the new position
  const newX = e.target.x();
  const newY = e.target.y();

  // Snap to grid if enabled
  const grid = diagramStore.currentDiagram?.grid;
  let finalX = newX;
  let finalY = newY;

  if (grid?.snapToGrid) {
    const gridSize = grid.size;
    finalX = Math.round(newX / gridSize) * gridSize;
    finalY = Math.round(newY / gridSize) * gridSize;
  }

  // Update the position of this symbol
  emit('move', props.instance.id, finalX, finalY);

  // If this was a multi-selection drag, finalize positions of all elements
  if (isMultiSelected.value && multiDragStartPositions.value.size > 0) {
    const deltaX = finalX - dragStartPosition.value.x;
    const deltaY = finalY - dragStartPosition.value.y;

    // Finalize positions of all selected elements
    multiDragStartPositions.value.forEach((startPos, elementKey) => {
      let newElementX = startPos.x + deltaX;
      let newElementY = startPos.y + deltaY;

      // Apply grid snapping to all elements if enabled
      if (grid?.snapToGrid) {
        const gridSize = grid.size;
        newElementX = Math.round(newElementX / gridSize) * gridSize;
        newElementY = Math.round(newElementY / gridSize) * gridSize;
      }

      // Parse element key to determine type and ID
      if (elementKey.startsWith('symbol-')) {
        const symbolId = elementKey.replace('symbol-', '');
        if (symbolId !== props.instance.id) {
          emit('move-multi-element', 'symbol', symbolId, newElementX, newElementY);
        }
      } else if (elementKey.startsWith('connection-') && elementKey.includes('-waypoint-')) {
        const parts = elementKey.split('-');
        const connectionId = parts[1];
        const waypointIndex = parseInt(parts[3]);
        emit('move-multi-element', 'connection-waypoint', connectionId, newElementX, newElementY, waypointIndex);
      } else if (elementKey.startsWith('group-')) {
        const groupId = elementKey.replace('group-', '');
        emit('move-multi-element', 'group', groupId, newElementX, newElementY);
      }
    });

    // Clear the multi-drag positions
    multiDragStartPositions.value.clear();
  }

  // Emit move-end event to clear alignment guides
  emit('move-end', props.instance.id);
};

const handleDragMove = (e: any) => {
  // 如果正在进行连接操作，取消拖动
  if (isConnecting.value) {
    console.log('Cancelling drag move on symbol while connecting');
    e.cancelBubble = true;
    return;
  }

  // Handle multi-selection group dragging
  if (isMultiSelected.value && multiDragStartPositions.value.size > 0) {
    const currentX = e.target.x();
    const currentY = e.target.y();

    // Calculate the offset from the original position
    const deltaX = currentX - dragStartPosition.value.x;
    const deltaY = currentY - dragStartPosition.value.y;

    // Move all selected elements by the same offset
    multiDragStartPositions.value.forEach((startPos, elementKey) => {
      const newX = startPos.x + deltaX;
      const newY = startPos.y + deltaY;

      // Parse element key to determine type and ID
      if (elementKey.startsWith('symbol-')) {
        const symbolId = elementKey.replace('symbol-', '');
        if (symbolId !== props.instance.id) { // Don't move the current symbol (it's handled by Konva)
          emit('move-multi-element', 'symbol', symbolId, newX, newY);
        }
      } else if (elementKey.startsWith('connection-') && elementKey.includes('-waypoint-')) {
        const parts = elementKey.split('-');
        const connectionId = parts[1];
        const waypointIndex = parseInt(parts[3]);
        emit('move-multi-element', 'connection-waypoint', connectionId, newX, newY, waypointIndex);
      } else if (elementKey.startsWith('group-')) {
        const groupId = elementKey.replace('group-', '');
        emit('move-multi-element', 'group', groupId, newX, newY);
      }
    });
  }
};

const handleClick = (e: any) => {
  e.cancelBubble = true; // Prevent event bubbling

  // Toggle selection
  if (props.isSelected) {
    emit('deselect', props.instance.id);
  } else {
    emit('select', props.instance.id);
  }
};

const handleMouseEnter = () => {
  isHovered.value = true;
};

const handleMouseLeave = () => {
  isHovered.value = false;
};

const handleConnectionPointMouseOver = (pointId: string) => {
  // 设置当前悬停的连接点
  hoveredConnectionPointId.value = pointId;

  // 对于母线符号，提供更强的视觉反馈
  if (isBusbarSymbol(definition.value)) {
    console.log('Busbar connection point hover:', pointId, 'on symbol:', props.instance.id);
  } else {
    console.log('Connection point hover:', pointId);
  }
};

const handleConnectionPointMouseOut = (pointId: string) => {
  // 清除悬停状态
  if (hoveredConnectionPointId.value === pointId) {
    hoveredConnectionPointId.value = null;
  }

  if (isBusbarSymbol(definition.value)) {
    console.log('Busbar connection point unhover:', pointId, 'on symbol:', props.instance.id);
  } else {
    console.log('Connection point unhover:', pointId);
  }
};

const handleConnectionPointMouseDown = (event: any, pointId: string) => {
  try {
    // 阻止事件冒泡，防止触发图元的拖动
    // 使用Konva的方式阻止事件冒泡
    if (event && event.cancelBubble) {
      event.cancelBubble = true;
    }

    // Prevent browser extension interference
    if (event && event.stopPropagation) {
      event.stopPropagation();
    }

    // 确保实例ID是有效的字符串
    const symbolId = String(props.instance.id);
    console.log('Connection point mousedown:', symbolId, pointId);

    // 设置活动连接点和连接状态
    activeConnectionPointId.value = pointId;
    isConnecting.value = true;

    // 在控制台中显示更多信息，以便调试
    console.log('Symbol instance ID type:', typeof symbolId);
    console.log('Symbol instance ID:', symbolId);
    console.log('Connection point:', pointId);
    console.log('Started connecting from point:', pointId);

    // 发出连接开始事件
    emit('connection-start', symbolId, pointId);
  } catch (error) {
    console.warn('Connection point mousedown error (likely browser extension interference):', error);
    // Reset state on error
    activeConnectionPointId.value = null;
    isConnecting.value = false;
  }
};

const handleConnectionPointMouseUp = (event: any, pointId: string) => {
  try {
    // 阻止事件冒泡，防止触发图元的其他事件
    // 使用Konva的方式阻止事件冒泡
    if (event && event.cancelBubble) {
      event.cancelBubble = true;
    }

    // Prevent browser extension interference
    if (event && event.stopPropagation) {
      event.stopPropagation();
    }

    // 确保实例ID是有效的字符串
    const symbolId = String(props.instance.id);
    console.log('Connection point mouseup:', symbolId, pointId);

    // 清除活动连接点和连接状态
    activeConnectionPointId.value = null;

    // 只有在正在连接时才发出连接结束事件
    if (isConnecting.value) {
      // 在控制台中显示更多信息，以便调试
      console.log('Symbol instance ID type:', typeof symbolId);
      console.log('Symbol instance ID:', symbolId);
      console.log('Connection point:', pointId);
      console.log('Finished connecting to point:', pointId);

      // 发出连接结束事件
      emit('connection-end', symbolId, pointId);

      // 重置连接状态
      isConnecting.value = false;
    }
  } catch (error) {
    console.warn('Connection point mouseup error (likely browser extension interference):', error);
    // Ensure cleanup happens even if error occurs
    activeConnectionPointId.value = null;
    isConnecting.value = false;
  }
};

// Get the value for a specific binding from simulation data or binding value
const getDisplayValue = (bindingId: string) => {
  // Check if we have simulation data for this symbol
  const currentState = simulationStore.getCurrentState;
  if (currentState && currentState.symbols[props.instance.id]) {
    const symbolState = currentState.symbols[props.instance.id];

    // Map binding IDs to simulation state properties
    switch (bindingId) {
      case 'voltage':
        return symbolState.voltage;
      case 'power':
        return symbolState.power;
      case 'status':
        return symbolState.status;
      case 'temperature':
        return symbolState.temperature;
      case 'efficiency':
        return symbolState.efficiency;
      default:
        // For other bindings, try to get from the binding value
        break;
    }
  }

  // If no simulation data or binding not found in simulation, use the binding value
  const binding = props.instance.bindings[bindingId];
  if (binding) {
    return binding.value;
  }

  // If no binding found, return null
  return null;
};

// Get historical data for a specific binding
const getHistoricalData = (bindingId: string, maxPoints: number = 100) => {
  // Map binding IDs to simulation state properties
  let parameter = '';
  switch (bindingId) {
    case 'voltage':
      parameter = 'voltage';
      break;
    case 'power':
      parameter = 'power';
      break;
    case 'temperature':
      parameter = 'temperature';
      break;
    case 'efficiency':
      parameter = 'efficiency';
      break;
    default:
      // For other bindings, use the binding ID
      parameter = bindingId;
      break;
  }

  // Get historical data from simulation store
  return simulationStore.getSymbolHistory(props.instance.id, parameter, maxPoints);
};

// 加载SVG图像
const loadSvgImage = () => {
  if (!useSvgImage.value || !definition.value || !definition.value.svg) {
    svgImage.value = null;
    return;
  }

  // 创建一个新的Image对象
  const img = new Image();

  // 设置SVG内容
  const svgContent = definition.value.svg
    .replace(/currentColor/g, getStrokeColor()) // 替换颜色
    .trim();

  // 创建完整的SVG文档
  const svgDoc = `
    <svg xmlns="http://www.w3.org/2000/svg" width="${definition.value.dimensions.width}" height="${definition.value.dimensions.height}">
      ${svgContent.replace(/<svg[^>]*>|<\/svg>/g, '')}
    </svg>
  `;

  // 将SVG转换为Data URL
  const svgBlob = new Blob([svgDoc], { type: 'image/svg+xml' });
  const url = URL.createObjectURL(svgBlob);

  // 设置图像加载事件
  img.onload = () => {
    svgImage.value = img;
    URL.revokeObjectURL(url); // 释放URL对象
  };

  img.onerror = (error) => {
    console.error('Error loading SVG image:', error);
    svgImage.value = null;
    URL.revokeObjectURL(url); // 释放URL对象
  };

  // 设置图像源
  img.src = url;
};

// 监听定义变化，重新加载SVG图像
watch(() => definition.value, () => {
  loadSvgImage();
}, { immediate: false });

// 监听颜色变化，重新加载SVG图像
watch([
  () => getFillColor(),
  () => getStrokeColor(),
  () => getLineWidth()
], () => {
  if (useSvgImage.value) {
    loadSvgImage();
  }
});

// 监听实例属性变化，重新加载SVG图像
watch(() => props.instance.properties, () => {
  if (useSvgImage.value) {
    loadSvgImage();
  }
}, { deep: true });

// 重置连接状态
const resetConnectionState = () => {
  console.log('Resetting connection state for symbol:', props.instance.id);
  hoveredConnectionPointId.value = null;
  activeConnectionPointId.value = null;
  isConnecting.value = false;
};

// 监听isConnecting状态变化，当连接结束时重置连接点状态
watch(isConnecting, (newValue, oldValue) => {
  if (oldValue && !newValue) {
    // 连接状态从true变为false，表示连接操作结束
    console.log('Connection operation ended, resetting connection point states');
    // 延迟一小段时间后重置，确保其他操作已完成
    setTimeout(() => {
      hoveredConnectionPointId.value = null;
      activeConnectionPointId.value = null;
    }, 100);
  }
});

// 监听全局连接操作结束事件
const handleConnectionOperationEnd = (event: CustomEvent) => {
  console.log('Received connection-operation-end event:', event.detail);
  // 重置连接状态
  resetConnectionState();
  isDragConnectionActive.value = false;
};

// 监听全局连接操作开始事件
const handleConnectionOperationStart = (event: CustomEvent) => {
  console.log('Received connection-operation-start event:', event.detail);
  isDragConnectionActive.value = true;
};

// 组件挂载时加载SVG图像并添加事件监听器
onMounted(() => {
  loadSvgImage();

  // 添加全局事件监听器
  document.addEventListener('connection-operation-start', handleConnectionOperationStart as EventListener);
  document.addEventListener('connection-operation-end', handleConnectionOperationEnd as EventListener);
});

// 组件卸载时重置连接状态并移除事件监听器
onUnmounted(() => {
  resetConnectionState();
  isDragConnectionActive.value = false;

  // 移除全局事件监听器
  document.removeEventListener('connection-operation-start', handleConnectionOperationStart as EventListener);
  document.removeEventListener('connection-operation-end', handleConnectionOperationEnd as EventListener);
});
</script>
