<template>
  <div class="busbar-properties-editor">
    <div class="section-title">母线属性</div>
    <a-form layout="vertical" size="small" class="compact-form">
      <!-- Busbar Orientation -->
      <a-form-item label="方向">
        <a-select
          v-model:value="busbarProperties.orientation"
          style="width: 100%"
          @change="handleOrientationChange"
        >
          <a-select-option value="horizontal">水平</a-select-option>
          <a-select-option value="vertical">垂直</a-select-option>
        </a-select>
      </a-form-item>

      <!-- Busbar Dimensions -->
      <div class="form-row">
        <a-row :gutter="8">
          <a-col :span="12">
            <a-form-item label="长度" class="form-row-item">
              <a-input-number
                v-model:value="busbarProperties.length"
                :min="50"
                :max="500"
                :step="10"
                style="width: 100%"
                @change="handleDimensionChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="宽度" class="form-row-item">
              <a-input-number
                v-model:value="busbarProperties.width"
                :min="10"
                :max="50"
                :step="5"
                style="width: 100%"
                @change="handleDimensionChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- Connection Points Configuration -->
      <a-form-item label="连接点数量">
        <a-input-number
          v-model:value="busbarProperties.connectionPointCount"
          :min="1"
          :max="10"
          :step="1"
          style="width: 100%"
          @change="handleConnectionPointsChange"
        />
      </a-form-item>

      <a-form-item label="连接点间距">
        <a-input-number
          v-model:value="busbarProperties.connectionPointSpacing"
          :min="20"
          :max="100"
          :step="5"
          style="width: 100%"
          :disabled="busbarProperties.autoDistributePoints"
          @change="handleConnectionPointsChange"
        />
      </a-form-item>

      <a-form-item>
        <a-checkbox
          v-model:checked="busbarProperties.autoDistributePoints"
          @change="handleAutoDistributeChange"
        >
          自动分布连接点
        </a-checkbox>
      </a-form-item>

      <!-- Preview Section -->
      <div class="preview-section">
        <div class="section-title">预览</div>
        <div class="busbar-preview" :class="busbarProperties.orientation">
          <div
            class="busbar-body"
            :style="busbarBodyStyle"
          ></div>
          <div
            v-for="(point, index) in previewConnectionPoints"
            :key="point.id"
            class="connection-point"
            :style="{
              left: point.position.x + 'px',
              top: point.position.y + 'px'
            }"
            :title="point.label"
          ></div>
        </div>
      </div>

      <!-- Apply Button -->
      <a-form-item>
        <a-button
          type="primary"
          block
          @click="applyChanges"
          :loading="applying"
        >
          应用更改
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { generateBusbarConnectionPoints } from '@/types/symbol';
import type { BusbarProperties } from '@/types/symbol';
import { BusbarOrientation } from '@/types/symbol';
import { generateBusbarPreviewSVG } from '@/utils/busbarUtils';

// Store
const diagramStore = useDiagramStore();

// Props
interface Props {
  symbolInstanceId?: string;
}

const props = defineProps<Props>();

// State
const applying = ref(false);

// Get selected symbol
const selectedSymbol = computed(() => {
  if (!props.symbolInstanceId || !diagramStore.currentDiagram) return null;
  return diagramStore.currentDiagram.symbolInstances[props.symbolInstanceId];
});

// Get symbol definition
const symbolDefinition = computed(() => {
  if (!selectedSymbol.value) return null;
  return diagramStore.getSymbolDefinition(selectedSymbol.value.definitionId);
});

// Initialize busbar properties
const busbarProperties = ref<BusbarProperties>({
  orientation: BusbarOrientation.HORIZONTAL,
  length: 200,
  width: 20,
  connectionPointCount: 3,
  connectionPointSpacing: 60,
  autoDistributePoints: true,
});

// Load current properties
watch(selectedSymbol, (symbol) => {
  if (symbol && symbol.properties?.busbar) {
    busbarProperties.value = { ...symbol.properties.busbar };
  } else if (symbolDefinition.value?.properties?.busbar) {
    busbarProperties.value = { ...symbolDefinition.value.properties.busbar };
  }
}, { immediate: true });

// Computed styles for preview
const busbarBodyStyle = computed(() => {
  const props = busbarProperties.value;
  if (props.orientation === BusbarOrientation.HORIZONTAL) {
    return {
      width: props.length + 'px',
      height: props.width + 'px',
      backgroundColor: '#ff0000',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
    };
  } else {
    return {
      width: props.width + 'px',
      height: props.length + 'px',
      backgroundColor: '#ff0000',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
    };
  }
});

// Generate preview connection points
const previewConnectionPoints = computed(() => {
  const dimensions = busbarProperties.value.orientation === BusbarOrientation.HORIZONTAL
    ? { width: busbarProperties.value.length, height: busbarProperties.value.width }
    : { width: busbarProperties.value.width, height: busbarProperties.value.length };

  const points = generateBusbarConnectionPoints(
    busbarProperties.value.orientation,
    dimensions,
    busbarProperties.value.connectionPointCount,
    busbarProperties.value.autoDistributePoints
  );

  // Adjust positions for preview (center the busbar in preview area)
  const previewWidth = 300;
  const previewHeight = 200;
  const offsetX = (previewWidth - dimensions.width) / 2;
  const offsetY = (previewHeight - dimensions.height) / 2;

  return points.map(point => ({
    ...point,
    position: {
      x: point.position.x + offsetX,
      y: point.position.y + offsetY,
    },
  }));
});

// Event handlers
const handleOrientationChange = () => {
  // Swap length and width when orientation changes
  const temp = busbarProperties.value.length;
  busbarProperties.value.length = busbarProperties.value.width;
  busbarProperties.value.width = temp;
};

const handleDimensionChange = () => {
  if (busbarProperties.value.autoDistributePoints) {
    updateConnectionPointSpacing();
  }
};

const handleConnectionPointsChange = () => {
  if (busbarProperties.value.autoDistributePoints) {
    updateConnectionPointSpacing();
  }
};

const handleAutoDistributeChange = () => {
  if (busbarProperties.value.autoDistributePoints) {
    updateConnectionPointSpacing();
  }
};

const updateConnectionPointSpacing = () => {
  const length = busbarProperties.value.length;
  const count = busbarProperties.value.connectionPointCount;
  busbarProperties.value.connectionPointSpacing = length / (count + 1);
};

const applyChanges = async () => {
  if (!selectedSymbol.value) return;

  applying.value = true;
  try {
    // Update symbol properties
    const updatedProperties = {
      ...selectedSymbol.value.properties,
      busbar: { ...busbarProperties.value },
    };

    // Update symbol dimensions
    const newDimensions = busbarProperties.value.orientation === BusbarOrientation.HORIZONTAL
      ? { width: busbarProperties.value.length, height: busbarProperties.value.width + 40 }
      : { width: busbarProperties.value.width + 40, height: busbarProperties.value.length };

    // Generate new connection points
    const newConnectionPoints = generateBusbarConnectionPoints(
      busbarProperties.value.orientation,
      newDimensions,
      busbarProperties.value.connectionPointCount,
      busbarProperties.value.autoDistributePoints
    );

    // Update the symbol instance
    diagramStore.updateSymbolInstance(selectedSymbol.value.id, {
      properties: updatedProperties,
    });

    // Mark diagram as modified
    diagramStore.modified = true;

    console.log('Busbar properties updated successfully');
  } catch (error) {
    console.error('Failed to update busbar properties:', error);
  } finally {
    applying.value = false;
  }
};
</script>

<style scoped>
.busbar-properties-editor {
  padding: 16px;
}

.section-title {
  font-weight: 600;
  margin-bottom: 12px;
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 4px;
}

.compact-form {
  margin-bottom: 0;
}

.form-row {
  margin-bottom: 12px;
}

.form-row-item {
  margin-bottom: 0;
}

.preview-section {
  margin-top: 16px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: #f9fafb;
}

.busbar-preview {
  position: relative;
  width: 300px;
  height: 200px;
  border: 1px dashed #d1d5db;
  margin: 12px auto;
  background-color: white;
}

.busbar-body {
  border-radius: 2px;
}

.connection-point {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #10b981;
  border: 2px solid white;
  transform: translate(-50%, -50%);
  cursor: pointer;
}

.connection-point:hover {
  background-color: #059669;
  transform: translate(-50%, -50%) scale(1.2);
}
</style>
