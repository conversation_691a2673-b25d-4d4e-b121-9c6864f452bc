<template>
  <div class="editor">
    <div class="editor-header">
      <div class="logo">
        <h2>{{ $locale.t('views.editor.title') }}</h2>
      </div>
      <main-toolbar
        :text-tool-active="textToolActive"
        @manage-diagrams="showDiagramManager"
        @save="saveDiagram"
        @undo="handleUndo"
        @redo="handleRedo"
        @cut="handleCut"
        @copy="handleCopy"
        @paste="handlePaste"
        @delete="handleDelete"
        @zoom-in="handleZoomIn"
        @zoom-out="handleZoomOut"
        @fit-content="handleFitContent"
        @reset-view="handleResetView"
        @rotate-clockwise="handleRotateClockwise"
        @rotate-counterclockwise="handleRotateCounterclockwise"
        @toggle-text-tool="handleToggleTextTool"
        @toggle-left-panel="toggleLeftPanel"
        @toggle-right-panel="toggleRightPanel"
        @show-grid-settings="showGridSettings"
        @view="$router.push('/viewer')"
        @home="$router.push('/')"
      >
        <template #keyboard-shortcuts>
          <keyboard-shortcuts
            @copy="handleCopy"
            @paste="handlePaste"
            @cut="handleCut"
            @delete="handleDelete"
            @undo="handleUndo"
            @redo="handleRedo"
            @save="saveDiagram"
            @select-all="handleSelectAll"
            @escape="handleEscape"
            @zoom-in="handleZoomIn"
            @zoom-out="handleZoomOut"
            @reset-view="handleResetView"
            @fit-content="handleFitContent"
            @rotate-clockwise="handleRotateClockwise"
            @rotate-counterclockwise="handleRotateCounterclockwise"
          />
        </template>
      </main-toolbar>

      <!-- 画面管理对话框 -->
      <diagram-manager
        v-model:visible="diagramManagerVisible"
        @edit="handleEditDiagram"
      />
      <div class="diagram-info">
        <a-typography-paragraph
          class="diagram-name"
          :editable="{
            tooltip: '点击编辑名称',
            onChange: handleDiagramNameChange,
            triggerType: ['text', 'icon']
          }"
          :content="diagramName"
        />
      </div>
    </div>

    <div class="editor-content">
      <div
        v-show="leftPanelVisible"
        class="left-panel"
      >
        <symbol-library-panel />
      </div>

      <div class="main-panel" :class="{
        'left-panel-hidden': !leftPanelVisible,
        'right-panel-hidden': !rightPanelVisible,
        'both-panels-hidden': !leftPanelVisible && !rightPanelVisible
      }">
        <canvas-drag-drop :stage-ref="stageRef">
          <diagram-renderer
        ref="diagramRenderer"
        :text-tool-active="textToolActive"
        @context-menu="handleContextMenu"
        @canvas-mounted="handleCanvasMounted"
        @edit-text-element="handleEditTextElement"
        @text-element-contextmenu="handleTextElementContextMenu"
        @place-text="handlePlaceText"
      />
        </canvas-drag-drop>
        <diagram-export ref="diagramExportRef" :stage-ref="stageRef" />
        <multi-selection-toolbar
          @select-all="handleSelectAll"
          @invert-selection="handleInvertSelection"
          @copy="handleCopy"
          @cut="handleCut"
          @delete="handleDelete"
          @bring-to-front="handleBringToFront"
          @send-to-back="handleSendToBack"
          @create-group="handleCreateGroup"
          @ungroup="handleUngroup"
          @align="handleAlign"
          @distribute="handleDistribute"
          @rotate-clockwise="handleRotateClockwise"
          @rotate-counterclockwise="handleRotateCounterclockwise"
          @clear-selection="handleClearSelection"
        />
        <context-menu
          ref="contextMenu"
          @edit="handleEdit"
          @copy="handleCopy"
          @cut="handleCut"
          @paste="handlePaste"
          @delete="handleDelete"
          @select-all="handleSelectAll"
          @fit-content="handleFitContent"
          @reset-view="handleResetView"
          @bring-to-front="handleBringToFront"
          @send-to-back="handleSendToBack"
          @line-type-change="handleLineTypeChange"
          @create-group="handleCreateGroup"
          @ungroup="handleUngroup"
        />
      </div>

      <div
        v-show="rightPanelVisible"
        class="right-panel"
      >
        <a-tabs default-active-key="properties">
          <a-tab-pane key="properties" tab="属性">
            <property-panel />
          </a-tab-pane>
          <a-tab-pane key="layers" tab="图层">
            <layer-panel />
          </a-tab-pane>
          <a-tab-pane key="layout" tab="布局">
            <power-layout-panel />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- Grid Settings Dialog -->
    <grid-settings-dialog
      :visible="gridSettingsVisible"
      @update:visible="gridSettingsVisible = $event"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { message } from 'ant-design-vue';
import { Position } from '@/types/symbol';
import { ConnectionLineType } from '@/types/connection';
import SymbolLibraryPanel from '@/components/panels/SymbolLibraryPanel.vue';
import PropertyPanel from '@/components/panels/PropertyPanel.vue';
import LayerPanel from '@/components/panels/LayerPanel.vue';
import PowerLayoutPanel from '@/components/panels/PowerLayoutPanel.vue';
import DiagramRenderer from '@/components/canvas/DiagramRenderer.vue';
import CanvasDragDrop from '@/components/canvas/CanvasDragDrop.vue';
import GridSettingsPanel from '@/components/controls/GridSettingsPanel.vue';
import KeyboardShortcuts from '@/components/controls/KeyboardShortcuts.vue';
import ContextMenu from '@/components/controls/ContextMenu.vue';
import DiagramExport from '@/components/controls/DiagramExport.vue';
import MainToolbar from '@/components/toolbar/MainToolbar.vue';
import MultiSelectionToolbar from '@/components/toolbar/MultiSelectionToolbar.vue';

import DiagramManager from '@/components/dialogs/DiagramManager.vue';
import GridSettingsDialog from '@/components/dialogs/GridSettingsDialog.vue';

// Store
const diagramStore = useDiagramStore();

// Refs
const diagramRenderer = ref(null);
const stageRef = ref(null);
const contextMenu = ref(null);
const diagramExportRef = ref(null);

// Reactive state
const diagramManagerVisible = ref(false);
const gridSettingsVisible = ref(false);
const leftPanelVisible = ref(true);
const rightPanelVisible = ref(true);
const textToolActive = ref(false);
const editingTextElementId = ref<string | null>(null);



// Export diagram
const handleExport = () => {
  if (diagramExportRef.value) {
    diagramExportRef.value.exportDiagram();
  }
};

// State
const clipboard = ref<{
  type: 'symbol' | 'connection' | 'textElement';
  data: any;
} | null>(null);

// Computed
const diagramName = computed(() => {
  return diagramStore.displayName;
});

// Handle diagram name change
const handleDiagramNameChange = (value: string) => {
  if (!diagramStore.currentDiagram) return;

  // Update the diagram name
  diagramStore.currentDiagram.name = value;
  diagramStore.modified = true;

  // Save the diagram
  diagramStore.saveDiagram();
  message.success('画面名称已更新');
};

// Methods
const createNewDiagram = () => {
  // Check if there are unsaved changes
  if (diagramStore.modified) {
    // Show confirmation dialog
    if (!confirm('You have unsaved changes. Create a new diagram anyway?')) {
      return;
    }
  }

  // Create a new diagram
  diagramStore.createDiagram('New Diagram');
};

const saveDiagram = () => {
  // Save the current diagram
  if (diagramStore.currentDiagram) {
    diagramStore.saveDiagram();
    message.success('画面已保存');
  }
};

const handleViewportChange = (position: Position, scale: number) => {
  if (!diagramStore.currentDiagram) return;

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    position,
    scale,
  };
};



const handleContextMenu = (event: MouseEvent, type: 'symbol' | 'connection' | 'canvas' | 'group' | 'text-element', id?: string) => {
  // Prevent default context menu
  event.preventDefault();

  // Show custom context menu
  if (contextMenu.value) {
    contextMenu.value.show(
      event.clientX,
      event.clientY,
      type,
      clipboard.value !== null
    );
  }
};

const handleEdit = () => {
  // Focus on the property panel
  // This is handled automatically by selection
};

const handleCopy = () => {
  if (!diagramStore.currentDiagram) return;

  // Check if there's a symbol selected
  if (diagramStore.currentDiagram.selectedSymbolIds.length > 0) {
    const symbolId = diagramStore.currentDiagram.selectedSymbolIds[0];
    const symbol = diagramStore.currentDiagram.symbolInstances[symbolId];

    if (symbol) {
      clipboard.value = {
        type: 'symbol',
        data: JSON.parse(JSON.stringify(symbol)),
      };
      message.success('Symbol copied to clipboard');
    }
  }
  // Check if there's a connection selected
  else if (diagramStore.currentDiagram.selectedConnectionIds.length > 0) {
    const connectionId = diagramStore.currentDiagram.selectedConnectionIds[0];
    const connection = diagramStore.currentDiagram.connections[connectionId];

    if (connection) {
      clipboard.value = {
        type: 'connection',
        data: JSON.parse(JSON.stringify(connection)),
      };
      message.success('Connection copied to clipboard');
    }
  }
  // Check if there's a text element selected
  else if (diagramStore.currentDiagram.selectedTextElementIds.length > 0) {
    const textElementId = diagramStore.currentDiagram.selectedTextElementIds[0];
    const textElement = diagramStore.currentDiagram.textElements[textElementId];

    if (textElement) {
      clipboard.value = {
        type: 'textElement',
        data: JSON.parse(JSON.stringify(textElement)),
      };
      message.success('Text element copied to clipboard');
    }
  }
};

const handleCut = () => {
  // Copy first
  handleCopy();

  // Then delete
  handleDelete();
};

const handlePaste = (x?: number, y?: number) => {
  if (!diagramStore.currentDiagram || !clipboard.value) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  if (clipboard.value.type === 'symbol') {
    const symbol = clipboard.value.data;

    // Generate a new ID
    const newId = crypto.randomUUID();

    // Create a new position if provided
    const newPosition = x !== undefined && y !== undefined
      ? { x, y }
      : { x: symbol.position.x + 20, y: symbol.position.y + 20 };

    // Create a new symbol instance
    const newSymbol = {
      ...symbol,
      id: newId,
      position: newPosition,
    };

    // Add to the diagram
    diagramStore.currentDiagram.symbolInstances[newId] = newSymbol;

    // Add to the current layer
    const currentLayer = diagramStore.currentDiagram.layers[0];
    currentLayer.symbolInstanceIds.push(newId);

    // Select the new symbol
    diagramStore.currentDiagram.selectedSymbolIds = [newId];
    diagramStore.currentDiagram.selectedConnectionIds = [];
    diagramStore.currentDiagram.selectedTextElementIds = [];

    // Mark as modified
    diagramStore.modified = true;

    message.success('Symbol pasted');
  }
  else if (clipboard.value.type === 'textElement') {
    const textElement = clipboard.value.data;

    // Generate a new ID
    const newId = crypto.randomUUID();

    // Create a new position if provided
    const newPosition = x !== undefined && y !== undefined
      ? { x, y }
      : { x: textElement.position.x + 20, y: textElement.position.y + 20 };

    // Create a new text element instance
    const newTextElement = {
      ...textElement,
      id: newId,
      position: newPosition,
    };

    // Ensure textElements object exists
    if (!diagramStore.currentDiagram.textElements) {
      diagramStore.currentDiagram.textElements = {};
    }

    // Add to the diagram
    diagramStore.currentDiagram.textElements[newId] = newTextElement;

    // Add to the current layer
    const currentLayer = diagramStore.currentDiagram.layers.find(layer => layer.visible && !layer.locked) || diagramStore.currentDiagram.layers[0];
    if (!currentLayer.textElementIds) {
      currentLayer.textElementIds = [];
    }
    currentLayer.textElementIds.push(newId);

    // Ensure selectedTextElementIds array exists
    if (!diagramStore.currentDiagram.selectedTextElementIds) {
      diagramStore.currentDiagram.selectedTextElementIds = [];
    }

    // Select the new text element
    diagramStore.currentDiagram.selectedSymbolIds = [];
    diagramStore.currentDiagram.selectedConnectionIds = [];
    diagramStore.currentDiagram.selectedTextElementIds = [newId];

    // Mark as modified
    diagramStore.modified = true;

    message.success('Text element pasted');
  }
  // Connection paste is more complex and not implemented here
};

const handleDelete = () => {
  if (!diagramStore.currentDiagram) return;

  // Delete selected elements
  if (diagramStore.deleteSelected()) {
    message.success('Selected elements deleted');
  }
};

const handleSelectAll = () => {
  if (!diagramStore.currentDiagram) return;

  // Select all elements
  diagramStore.selectAll();
};

const handleEscape = () => {
  if (!diagramStore.currentDiagram) return;

  // Clear selection
  diagramStore.currentDiagram.selectedSymbolIds = [];
  diagramStore.currentDiagram.selectedConnectionIds = [];
  diagramStore.currentDiagram.selectedTextElementIds = [];

  // Deactivate text tool if active
  if (textToolActive.value) {
    textToolActive.value = false;
  }

  // Clear text editing mode
  editingTextElementId.value = null;
};

const handleToggleTextTool = () => {
  textToolActive.value = !textToolActive.value;

  if (textToolActive.value) {
    // Clear any existing selection when activating text tool
    if (diagramStore.currentDiagram) {
      diagramStore.currentDiagram.selectedSymbolIds = [];
      diagramStore.currentDiagram.selectedConnectionIds = [];
      diagramStore.currentDiagram.selectedTextElementIds = [];
      diagramStore.currentDiagram.selectedGroupIds = [];
    }
    message.info('Text tool activated. Click on the canvas to place text.');
  } else {
    message.info('Text tool deactivated.');
  }
};

const handleEditTextElement = (id: string) => {
  editingTextElementId.value = id;
  // Focus on the property panel for text editing
  message.info('Text element selected for editing. Use the property panel to modify text properties.');
};

const handleTextElementContextMenu = (event: MouseEvent, id: string) => {
  // Handle text element context menu
  handleContextMenu(event, 'text-element', id);
};

const handlePlaceText = (position: { x: number; y: number }) => {
  if (!diagramStore.currentDiagram || !textToolActive.value) return;

  // Create a new text element
  const textElementId = `text-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const newTextElement = {
    id: textElementId,
    content: 'New Text',
    position: position,
    size: { width: 100, height: 30 },
    style: {
      fontSize: 14,
      fontFamily: 'Arial',
      color: '#000000',
      textAlign: 'left' as const,
      fontWeight: 'normal' as const,
      fontStyle: 'normal' as const,
      textDecoration: 'none' as const,
    },
    rotation: 0,
    visible: true,
    locked: false,
  };

  // Ensure textElements object exists
  if (!diagramStore.currentDiagram.textElements) {
    diagramStore.currentDiagram.textElements = {};
  }

  // Add the text element to the diagram
  diagramStore.currentDiagram.textElements[textElementId] = newTextElement;

  // Add to the current layer
  const currentLayer = diagramStore.currentDiagram.layers.find(layer => layer.visible && !layer.locked);
  if (currentLayer) {
    if (!currentLayer.textElementIds) {
      currentLayer.textElementIds = [];
    }
    currentLayer.textElementIds.push(textElementId);
  }

  // Ensure selectedTextElementIds array exists
  if (!diagramStore.currentDiagram.selectedTextElementIds) {
    diagramStore.currentDiagram.selectedTextElementIds = [];
  }

  // Select the new text element
  diagramStore.currentDiagram.selectedSymbolIds = [];
  diagramStore.currentDiagram.selectedConnectionIds = [];
  diagramStore.currentDiagram.selectedTextElementIds = [textElementId];
  diagramStore.currentDiagram.selectedGroupIds = [];

  // Mark diagram as modified
  diagramStore.modified = true;

  // Deactivate text tool after placing text
  textToolActive.value = false;

  // Set editing mode for the new text element
  editingTextElementId.value = textElementId;

  message.success('Text element created. Use the property panel to edit its content and style.');
};

const handleZoomIn = () => {
  if (!diagramStore.currentDiagram || !stageRef.value) return;

  const currentScale = diagramStore.currentDiagram.viewport.scale;
  const newScale = Math.min(currentScale * 1.2, 5);

  // Get the stage center point for zoom
  const stage = stageRef.value.getNode();
  const centerX = stage.width() / 2;
  const centerY = stage.height() / 2;

  // Calculate the new position to zoom towards center
  const mousePointTo = {
    x: (centerX - diagramStore.currentDiagram.viewport.position.x) / currentScale,
    y: (centerY - diagramStore.currentDiagram.viewport.position.y) / currentScale,
  };

  // Update the viewport with proper positioning
  diagramStore.currentDiagram.viewport = {
    position: {
      x: centerX / newScale - mousePointTo.x,
      y: centerY / newScale - mousePointTo.y,
    },
    scale: newScale,
  };
};

const handleZoomOut = () => {
  if (!diagramStore.currentDiagram || !stageRef.value) return;

  const currentScale = diagramStore.currentDiagram.viewport.scale;
  const newScale = Math.max(currentScale / 1.2, 0.1);

  // Get the stage center point for zoom
  const stage = stageRef.value.getNode();
  const centerX = stage.width() / 2;
  const centerY = stage.height() / 2;

  // Calculate the new position to zoom towards center
  const mousePointTo = {
    x: (centerX - diagramStore.currentDiagram.viewport.position.x) / currentScale,
    y: (centerY - diagramStore.currentDiagram.viewport.position.y) / currentScale,
  };

  // Update the viewport with proper positioning
  diagramStore.currentDiagram.viewport = {
    position: {
      x: centerX / newScale - mousePointTo.x,
      y: centerY / newScale - mousePointTo.y,
    },
    scale: newScale,
  };
};

const handleResetView = () => {
  if (!diagramStore.currentDiagram) return;

  // Reset view to default
  diagramStore.currentDiagram.viewport = {
    position: { x: 0, y: 0 },
    scale: 1,
  };
};

const handleFitContent = () => {
  if (!diagramStore.currentDiagram || !stageRef.value) return;

  const stage = stageRef.value.getNode();
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Calculate the bounds of all symbols and connections
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  // Check symbols
  const symbols = Object.values(diagramStore.currentDiagram.symbolInstances);
  const connections = Object.values(diagramStore.currentDiagram.connections);

  if (symbols.length === 0 && connections.length === 0) {
    handleResetView();
    return;
  }

  // Calculate bounds for symbols
  symbols.forEach(symbol => {
    const x = symbol.position.x;
    const y = symbol.position.y;
    // Assume symbol size for better bounds calculation
    const symbolWidth = 60; // Default symbol width
    const symbolHeight = 60; // Default symbol height

    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + symbolWidth);
    maxY = Math.max(maxY, y + symbolHeight);
  });

  // Calculate bounds for connections
  connections.forEach(connection => {
    if (connection.startPoint) {
      minX = Math.min(minX, connection.startPoint.x);
      minY = Math.min(minY, connection.startPoint.y);
      maxX = Math.max(maxX, connection.startPoint.x);
      maxY = Math.max(maxY, connection.startPoint.y);
    }
    if (connection.endPoint) {
      minX = Math.min(minX, connection.endPoint.x);
      minY = Math.min(minY, connection.endPoint.y);
      maxX = Math.max(maxX, connection.endPoint.x);
      maxY = Math.max(maxY, connection.endPoint.y);
    }
    // Include waypoints if they exist
    if (connection.waypoints) {
      connection.waypoints.forEach(point => {
        minX = Math.min(minX, point.x);
        minY = Math.min(minY, point.y);
        maxX = Math.max(maxX, point.x);
        maxY = Math.max(maxY, point.y);
      });
    }
  });

  // Add padding
  const padding = 100;
  minX -= padding;
  minY -= padding;
  maxX += padding;
  maxY += padding;

  // Calculate the content dimensions
  const contentWidth = maxX - minX;
  const contentHeight = maxY - minY;

  // Ensure minimum content size
  const minContentSize = 200;
  const finalContentWidth = Math.max(contentWidth, minContentSize);
  const finalContentHeight = Math.max(contentHeight, minContentSize);

  // Calculate the scale to fit (with maximum zoom limit)
  const scaleX = stageWidth / finalContentWidth;
  const scaleY = stageHeight / finalContentHeight;
  const scale = Math.min(scaleX, scaleY, 3); // Max zoom of 3x for fit content

  // Calculate the center position
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    position: {
      x: centerX - stageWidth / (2 * scale),
      y: centerY - stageHeight / (2 * scale),
    },
    scale: scale,
  };
};

// Rotation handlers
const handleRotateClockwise = () => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.rotateSelectedSymbols(true);
  message.success('Symbols rotated clockwise');
};

const handleRotateCounterclockwise = () => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.rotateSelectedSymbols(false);
  message.success('Symbols rotated counterclockwise');
};

// Panel toggle handlers
const toggleLeftPanel = () => {
  leftPanelVisible.value = !leftPanelVisible.value;
};

const toggleRightPanel = () => {
  rightPanelVisible.value = !rightPanelVisible.value;
};

// Grid settings handler
const showGridSettings = () => {
  console.log('Grid settings button clicked');
  console.log('Current gridSettingsVisible:', gridSettingsVisible.value);
  gridSettingsVisible.value = true;
  console.log('Updated gridSettingsVisible:', gridSettingsVisible.value);
};

const handleBringToFront = () => {
  if (!diagramStore.currentDiagram) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Get the selected symbol IDs
  const symbolIds = [...diagramStore.currentDiagram.selectedSymbolIds];

  // Move the symbols to the end of the layer's symbolInstanceIds array
  diagramStore.currentDiagram.layers.forEach(layer => {
    // Remove the symbols from the layer
    layer.symbolInstanceIds = layer.symbolInstanceIds.filter(
      id => !symbolIds.includes(id)
    );

    // Add them back at the end
    layer.symbolInstanceIds.push(...symbolIds);
  });

  // Mark as modified
  diagramStore.modified = true;
};

const handleSendToBack = () => {
  if (!diagramStore.currentDiagram) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Get the selected symbol IDs
  const symbolIds = [...diagramStore.currentDiagram.selectedSymbolIds];

  // Move the symbols to the beginning of the layer's symbolInstanceIds array
  diagramStore.currentDiagram.layers.forEach(layer => {
    // Remove the symbols from the layer
    layer.symbolInstanceIds = layer.symbolInstanceIds.filter(
      id => !symbolIds.includes(id)
    );

    // Add them back at the beginning
    layer.symbolInstanceIds.unshift(...symbolIds);
  });

  // Mark as modified
  diagramStore.modified = true;
};

const handleLineTypeChange = (type: ConnectionLineType) => {
  if (!diagramStore.currentDiagram) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Get the selected connection IDs
  const connectionIds = [...diagramStore.currentDiagram.selectedConnectionIds];

  // Update the line type
  connectionIds.forEach(id => {
    const connection = diagramStore.currentDiagram?.connections[id];
    if (connection) {
      connection.lineType = type;

      // Initialize waypoints or control points if needed
      if (type === ConnectionLineType.POLYLINE && !connection.waypoints) {
        connection.waypoints = [];
      } else if (type === ConnectionLineType.BEZIER && !connection.controlPoints) {
        connection.controlPoints = [];
      }
    }
  });

  // Mark as modified
  diagramStore.modified = true;
};

const handleCreateGroup = () => {
  const groupId = diagramStore.createGroup();
  if (groupId) {
    message.success('Group created');
  }
};

const handleUngroup = () => {
  if (!diagramStore.currentDiagram) return;

  // Get the selected group ID
  const groupId = diagramStore.currentDiagram.selectedGroupIds[0];
  if (groupId && diagramStore.ungroup(groupId)) {
    message.success('Group ungrouped');
  }
};

// Multi-selection toolbar handlers
const handleInvertSelection = () => {
  if (!diagramStore.currentDiagram) return;

  // Get all visible symbol IDs
  const allSymbolIds: string[] = [];
  diagramStore.currentDiagram.layers.forEach(layer => {
    if (layer.visible) {
      allSymbolIds.push(...layer.symbolInstanceIds);
    }
  });

  // Get all visible connection IDs
  const allConnectionIds: string[] = [];
  diagramStore.currentDiagram.layers.forEach(layer => {
    if (layer.visible) {
      allConnectionIds.push(...layer.connectionIds);
    }
  });

  // Invert symbol selection
  const currentlySelectedSymbols = new Set(diagramStore.currentDiagram.selectedSymbolIds);
  const newSelectedSymbols = allSymbolIds.filter(id => !currentlySelectedSymbols.has(id));

  // Invert connection selection
  const currentlySelectedConnections = new Set(diagramStore.currentDiagram.selectedConnectionIds);
  const newSelectedConnections = allConnectionIds.filter(id => !currentlySelectedConnections.has(id));

  // Update selection
  diagramStore.currentDiagram.selectedSymbolIds = newSelectedSymbols;
  diagramStore.currentDiagram.selectedConnectionIds = newSelectedConnections;
  diagramStore.currentDiagram.selectedGroupIds = []; // Clear group selection

  message.success('Selection inverted');
};

const handleAlign = (type: string) => {
  if (!diagramStore.currentDiagram) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Get selected symbols
  const selectedSymbols = diagramStore.currentDiagram.selectedSymbolIds
    .map(id => diagramStore.currentDiagram?.symbolInstances[id])
    .filter(Boolean);

  if (selectedSymbols.length < 2) return;

  // Calculate alignment based on type
  switch (type) {
    case 'left':
      const leftMost = Math.min(...selectedSymbols.map(s => s.position.x));
      selectedSymbols.forEach(symbol => {
        symbol.position.x = leftMost;
      });
      break;
    case 'center':
      const centerX = selectedSymbols.reduce((sum, s) => sum + s.position.x, 0) / selectedSymbols.length;
      selectedSymbols.forEach(symbol => {
        symbol.position.x = centerX;
      });
      break;
    case 'right':
      const rightMost = Math.max(...selectedSymbols.map(s => s.position.x));
      selectedSymbols.forEach(symbol => {
        symbol.position.x = rightMost;
      });
      break;
  }

  // Mark as modified
  diagramStore.modified = true;
  message.success(`Symbols aligned ${type}`);
};

const handleDistribute = (type: string) => {
  if (!diagramStore.currentDiagram) return;

  // Save current state to undo stack
  diagramStore.saveToUndoStack();

  // Get selected symbols
  const selectedSymbols = diagramStore.currentDiagram.selectedSymbolIds
    .map(id => diagramStore.currentDiagram?.symbolInstances[id])
    .filter(Boolean);

  if (selectedSymbols.length < 3) return;

  // Sort symbols and distribute based on type
  if (type === 'horizontal') {
    selectedSymbols.sort((a, b) => a.position.x - b.position.x);
    const leftMost = selectedSymbols[0].position.x;
    const rightMost = selectedSymbols[selectedSymbols.length - 1].position.x;
    const spacing = (rightMost - leftMost) / (selectedSymbols.length - 1);

    selectedSymbols.forEach((symbol, index) => {
      symbol.position.x = leftMost + (spacing * index);
    });
  } else if (type === 'vertical') {
    selectedSymbols.sort((a, b) => a.position.y - b.position.y);
    const topMost = selectedSymbols[0].position.y;
    const bottomMost = selectedSymbols[selectedSymbols.length - 1].position.y;
    const spacing = (bottomMost - topMost) / (selectedSymbols.length - 1);

    selectedSymbols.forEach((symbol, index) => {
      symbol.position.y = topMost + (spacing * index);
    });
  }

  // Mark as modified
  diagramStore.modified = true;
  message.success(`Symbols distributed ${type}ally`);
};

const handleClearSelection = () => {
  if (!diagramStore.currentDiagram) return;

  // Clear all selections
  diagramStore.currentDiagram.selectedSymbolIds = [];
  diagramStore.currentDiagram.selectedConnectionIds = [];
  diagramStore.currentDiagram.selectedGroupIds = [];
  diagramStore.currentDiagram.selectedConnectionId = undefined;
};

const handleUndo = () => {
  if (diagramStore.undo()) {
    message.info('Undo');
  }
};

const handleRedo = () => {
  if (diagramStore.redo()) {
    message.info('Redo');
  }
};

// 显示画面管理对话框
const showDiagramManager = () => {
  diagramManagerVisible.value = true;
};

// 处理编辑画面
const handleEditDiagram = (diagramId: string) => {
  diagramStore.loadDiagram(diagramId);
};

// 确保画面已加载
const ensureDiagramLoaded = () => {
  // 如果没有当前画面，则创建一个新画面
  if (!diagramStore.currentDiagram) {
    console.log('No current diagram, checking localStorage...');

    // 如果有保存的画面，加载最近修改的一个
    const diagrams = Object.values(diagramStore.diagrams);
    if (diagrams.length > 0) {
      console.log(`Found ${diagrams.length} diagrams in localStorage, loading most recent...`);

      // 按修改时间排序，取最近修改的
      const sortedDiagrams = diagrams.sort((a, b) =>
        new Date(b.modified).getTime() - new Date(a.modified).getTime()
      );

      // 加载第一个画面
      diagramStore.loadDiagram(sortedDiagrams[0].id);
      console.log(`Loaded diagram: ${sortedDiagrams[0].name} (${sortedDiagrams[0].id})`);
    } else {
      // 没有保存的画面，创建一个新画面
      console.log('No diagrams found in localStorage, creating a new one...');
      diagramStore.createDiagram('新画面');

      // 确保新画面已保存到 localStorage
      diagramStore.saveDiagramsToLocalStorage();
      console.log('New diagram created and saved to localStorage');
    }
  } else {
    console.log('Current diagram already loaded:', diagramStore.currentDiagram.name);
  }

  return !!diagramStore.currentDiagram;
};





// Handle canvas mounted event
const handleCanvasMounted = (canvas: any) => {
  console.log('Canvas mounted, initializing stage reference...');
  if (canvas?.stage) {
    stageRef.value = canvas.stage;
    console.log('Stage reference initialized from mounted canvas');
  }
};

// 监听 diagramRenderer 的变化
watch(diagramRenderer, async (newValue) => {
  if (newValue) {
    console.log('DiagramRenderer reference changed');
  }
}, { immediate: false });

// 监听 currentDiagram 的变化
watch(() => diagramStore.currentDiagram, (newDiagram) => {
  if (newDiagram) {
    console.log('Current diagram changed:', newDiagram.name);
    // Stage initialization is now handled by handleCanvasMounted
  }
}, { immediate: false });

// 监听 canvas 的变化
watch(() => diagramRenderer.value?.canvas, (newCanvas) => {
  if (newCanvas) {
    console.log('Canvas reference changed');
    // Stage initialization is now handled by handleCanvasMounted
  }
}, { immediate: false });

// Lifecycle hooks
onMounted(async () => {
  console.log('Editor component mounted');

  try {
    // 1. 从 localStorage 加载所有画面
    const loadSuccess = diagramStore.loadDiagramsFromLocalStorage();
    console.log('Diagrams loaded from localStorage:', loadSuccess);

    // 2. 确保当前画面已加载
    const diagramLoaded = ensureDiagramLoaded();

    if (!diagramLoaded) {
      console.error('Failed to load or create a diagram');
      return;
    }

    // 3. 等待一个tick确保画面数据已经完全设置
    await nextTick();
    console.log('Editor mounted. Current diagram:', diagramStore.currentDiagram?.name);
    console.log('Waiting for canvas to emit mounted event.');

  } catch (error) {
    console.error('Error during Editor component initialization:', error);
  }
});
</script>

<style scoped>
.editor {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-header {
  height: 36px;
  background-color: var(--header-background-color);
  border-bottom: 1px solid var(--header-border-color);
  display: flex;
  align-items: center;
  padding: 0 8px;
  color: white;
}

.logo {
  width: 200px;
}

.logo h2 {
  margin: 0;
  font-size: 14px;
  color: white;
  font-weight: 500;
}

.toolbar {
  flex: 1;
  display: flex;
  justify-content: center;
  background-color: var(--header-background-color);
}

.diagram-info {
  width: 200px;
  text-align: right;
}

.diagram-name {
  font-weight: 500;
  color: white !important;
  margin-bottom: 0 !important;
  text-align: right;
}

.diagram-name :deep(.ant-typography) {
  color: white !important;
}

.diagram-name :deep(.ant-typography-edit-content) {
  color: rgba(0, 0, 0, 0.85);
}

.diagram-name:hover {
  cursor: pointer;
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.left-panel {
  width: 200px;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
}

.main-panel {
  flex: 1;
  height: 100%;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.right-panel {
  width: 250px;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

/* Ensure tabs container takes full height */
.right-panel :deep(.ant-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.right-panel :deep(.ant-tabs-content-holder) {
  flex: 1;
  overflow: hidden;
}

.right-panel :deep(.ant-tabs-tabpane) {
  height: 100%;
  overflow: visible;
}
</style>
