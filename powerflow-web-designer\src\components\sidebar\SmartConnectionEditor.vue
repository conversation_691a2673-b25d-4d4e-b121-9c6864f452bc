<template>
  <div class="smart-connection-editor">
    <div class="section-title">Smart Connection Settings</div>
    
    <a-form layout="vertical">
      <!-- Routing Strategy -->
      <a-form-item label="Routing Strategy">
        <a-select
          v-model:value="routingStrategy"
          style="width: 100%"
          @change="updateRoutingOptions"
        >
          <a-select-option value="direct">Direct</a-select-option>
          <a-select-option value="orthogonal">Orthogonal</a-select-option>
          <a-select-option value="manhattan">Manhattan</a-select-option>
          <a-select-option value="metro">Metro</a-select-option>
        </a-select>
      </a-form-item>
      
      <!-- Obstacle Avoidance -->
      <a-form-item>
        <a-checkbox
          v-model:checked="avoidObstacles"
          @change="updateRoutingOptions"
        >
          Avoid Obstacles
        </a-checkbox>
      </a-form-item>
      
      <!-- Padding -->
      <a-form-item label="Obstacle Padding">
        <a-slider
          v-model:value="padding"
          :min="0"
          :max="30"
          :step="1"
          @change="updateRoutingOptions"
        />
      </a-form-item>
      
      <!-- Prefer Straight Lines -->
      <a-form-item>
        <a-checkbox
          v-model:checked="preferStraightLines"
          @change="updateRoutingOptions"
        >
          Prefer Straight Lines
        </a-checkbox>
      </a-form-item>
      
      <!-- Smoothing Factor -->
      <a-form-item label="Smoothing Factor">
        <a-slider
          v-model:value="smoothingFactor"
          :min="0"
          :max="1"
          :step="0.1"
          @change="updateRoutingOptions"
        />
      </a-form-item>
      
      <!-- Corner Radius -->
      <a-form-item label="Corner Radius">
        <a-slider
          v-model:value="cornerRadius"
          :min="0"
          :max="20"
          :step="1"
          @change="updateRoutingOptions"
        />
      </a-form-item>
      
      <!-- Jump Over Crossings -->
      <a-form-item>
        <a-checkbox
          v-model:checked="jumpOverCrossings"
          @change="updateRoutingOptions"
        >
          Jump Over Crossings
        </a-checkbox>
      </a-form-item>
      
      <!-- Snap to Grid -->
      <a-form-item>
        <a-checkbox
          v-model:checked="snapToGrid"
          @change="updateRoutingOptions"
        >
          Snap to Grid
        </a-checkbox>
      </a-form-item>
      
      <!-- Grid Size -->
      <a-form-item label="Grid Size">
        <a-slider
          v-model:value="gridSize"
          :min="5"
          :max="50"
          :step="5"
          @change="updateRoutingOptions"
        />
      </a-form-item>
      
      <!-- Optimize Path -->
      <a-form-item>
        <a-checkbox
          v-model:checked="optimizePath"
          @change="updateRoutingOptions"
        >
          Optimize Path
        </a-checkbox>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { Connection, ConnectionLineType, ConnectionRoutingOptions, defaultRoutingOptions } from '@/types/connection';

// Store
const diagramStore = useDiagramStore();

// Computed
const selectedConnection = computed<Connection | null>(() => {
  if (!diagramStore.currentDiagram || diagramStore.selectedConnectionIds.length === 0) {
    return null;
  }
  
  const connectionId = diagramStore.selectedConnectionIds[0];
  return diagramStore.currentDiagram.connections[connectionId] || null;
});

const isSmartConnection = computed(() => {
  return selectedConnection.value?.lineType === ConnectionLineType.SMART;
});

// Form state
const routingStrategy = ref<string>('direct');
const avoidObstacles = ref<boolean>(true);
const padding = ref<number>(10);
const preferStraightLines = ref<boolean>(true);
const smoothingFactor = ref<number>(0.5);
const cornerRadius = ref<number>(0);
const jumpOverCrossings = ref<boolean>(false);
const snapToGrid = ref<boolean>(true);
const gridSize = ref<number>(10);
const optimizePath = ref<boolean>(true);

// Watch for changes in selected connection
watch(selectedConnection, (connection) => {
  if (!connection || connection.lineType !== ConnectionLineType.SMART) {
    // Use default values if not a smart connection
    routingStrategy.value = defaultRoutingOptions.routingStrategy;
    avoidObstacles.value = defaultRoutingOptions.avoidObstacles;
    padding.value = defaultRoutingOptions.padding;
    preferStraightLines.value = defaultRoutingOptions.preferStraightLines;
    smoothingFactor.value = defaultRoutingOptions.smoothingFactor;
    cornerRadius.value = defaultRoutingOptions.cornerRadius;
    jumpOverCrossings.value = defaultRoutingOptions.jumpOverCrossings;
    snapToGrid.value = defaultRoutingOptions.snapToGrid;
    gridSize.value = defaultRoutingOptions.gridSize;
    optimizePath.value = defaultRoutingOptions.optimizePath;
    return;
  }
  
  // Get routing options from connection
  const routingOptions = connection.routingOptions || defaultRoutingOptions;
  
  // Set form values
  routingStrategy.value = routingOptions.routingStrategy;
  avoidObstacles.value = routingOptions.avoidObstacles;
  padding.value = routingOptions.padding;
  preferStraightLines.value = routingOptions.preferStraightLines;
  smoothingFactor.value = routingOptions.smoothingFactor;
  cornerRadius.value = routingOptions.cornerRadius;
  jumpOverCrossings.value = routingOptions.jumpOverCrossings;
  snapToGrid.value = routingOptions.snapToGrid;
  gridSize.value = routingOptions.gridSize;
  optimizePath.value = routingOptions.optimizePath;
}, { immediate: true });

// Update routing options
const updateRoutingOptions = () => {
  if (!selectedConnection.value || !isSmartConnection.value) return;
  
  const routingOptions: ConnectionRoutingOptions = {
    routingStrategy: routingStrategy.value,
    avoidObstacles: avoidObstacles.value,
    padding: padding.value,
    preferStraightLines: preferStraightLines.value,
    smoothingFactor: smoothingFactor.value,
    cornerRadius: cornerRadius.value,
    jumpOverCrossings: jumpOverCrossings.value,
    snapToGrid: snapToGrid.value,
    gridSize: gridSize.value,
    optimizePath: optimizePath.value,
  };
  
  // Update connection
  diagramStore.updateConnection({
    ...selectedConnection.value,
    routingOptions,
  });
};
</script>

<style scoped>
.smart-connection-editor {
  padding: 16px;
}

.section-title {
  font-weight: 500;
  margin-bottom: 16px;
  font-size: 16px;
}
</style>
