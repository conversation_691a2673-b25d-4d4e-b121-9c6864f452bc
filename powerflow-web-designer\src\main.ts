import { createApp } from 'vue';
import { createPinia } from 'pinia';
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/antd.css';
import './style.css';
import App from './App.vue';
import router from './router';
import VueKonva from 'vue-konva';
import { provideLocale } from './locales';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { useDiagramStore } from './stores/diagram';

const app = createApp(App);

// Use Ant Design Vue
app.use(Antd);

// Use our custom locale provider
app.config.globalProperties.$locale = provideLocale();

const pinia = createPinia();
app.use(pinia);
app.use(router);
app.use(VueKonva);

// Load symbol library after Pinia is initialized and before mounting
const diagramStore = useDiagramStore();
diagramStore.loadSymbolLibrary().then(() => {
  console.log('Symbol library loaded successfully');
  app.mount('#app');
}).catch((error) => {
  console.error('Failed to load symbol library:', error);
  // Mount anyway with fallback to hardcoded symbols
  app.mount('#app');
});
