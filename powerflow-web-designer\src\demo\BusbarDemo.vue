<template>
  <div class="busbar-demo">
    <div class="demo-header">
      <h2>增强母线图元功能演示</h2>
      <p>展示动态连接点、方向支持和属性编辑功能</p>
    </div>

    <div class="demo-content">
      <!-- 水平母线演示 -->
      <div class="demo-section">
        <h3>水平母线</h3>
        <div class="busbar-preview">
          <div 
            class="busbar horizontal"
            :style="horizontalBusbarStyle"
          >
            <div
              v-for="(point, index) in horizontalConnectionPoints"
              :key="point.id"
              class="connection-point"
              :style="{
                left: point.position.x + 'px',
                top: point.position.y + 'px'
              }"
              :title="point.label"
            ></div>
          </div>
        </div>
        
        <div class="controls">
          <a-form layout="inline" size="small">
            <a-form-item label="连接点数量">
              <a-input-number
                v-model:value="horizontalConfig.connectionPointCount"
                :min="1"
                :max="10"
                @change="updateHorizontalPoints"
              />
            </a-form-item>
            <a-form-item label="长度">
              <a-input-number
                v-model:value="horizontalConfig.length"
                :min="100"
                :max="400"
                :step="20"
                @change="updateHorizontalPoints"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 垂直母线演示 -->
      <div class="demo-section">
        <h3>垂直母线</h3>
        <div class="busbar-preview">
          <div 
            class="busbar vertical"
            :style="verticalBusbarStyle"
          >
            <div
              v-for="(point, index) in verticalConnectionPoints"
              :key="point.id"
              class="connection-point"
              :style="{
                left: point.position.x + 'px',
                top: point.position.y + 'px'
              }"
              :title="point.label"
            ></div>
          </div>
        </div>
        
        <div class="controls">
          <a-form layout="inline" size="small">
            <a-form-item label="连接点数量">
              <a-input-number
                v-model:value="verticalConfig.connectionPointCount"
                :min="1"
                :max="10"
                @change="updateVerticalPoints"
              />
            </a-form-item>
            <a-form-item label="长度">
              <a-input-number
                v-model:value="verticalConfig.length"
                :min="100"
                :max="400"
                :step="20"
                @change="updateVerticalPoints"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- SVG 生成演示 -->
      <div class="demo-section">
        <h3>SVG 生成</h3>
        <div class="svg-demo">
          <div class="svg-preview" v-html="generatedSVG"></div>
          <div class="svg-code">
            <a-typography-paragraph copyable>
              <pre>{{ generatedSVG }}</pre>
            </a-typography-paragraph>
          </div>
        </div>
      </div>

      <!-- 数据中心拓扑演示 -->
      <div class="demo-section">
        <h3>数据中心供电拓扑示例</h3>
        <div class="topology-demo">
          <div class="power-source">市电1</div>
          <div class="power-source">市电2</div>
          <div class="main-busbar horizontal-busbar">主母线</div>
          <div class="generator">柴发1</div>
          <div class="generator">柴发2</div>
          <div class="load">UPS1</div>
          <div class="load">UPS2</div>
          <div class="load">UPS3</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { generateBusbarConnectionPoints } from '@/types/symbol';
import { BusbarOrientation } from '@/types/symbol';
import { generateBusbarSVG } from '@/utils/busbarUtils';

// 水平母线配置
const horizontalConfig = ref({
  orientation: BusbarOrientation.HORIZONTAL,
  length: 200,
  width: 20,
  connectionPointCount: 3,
  connectionPointSpacing: 60,
  autoDistributePoints: true,
});

// 垂直母线配置
const verticalConfig = ref({
  orientation: BusbarOrientation.VERTICAL,
  length: 200,
  width: 20,
  connectionPointCount: 3,
  connectionPointSpacing: 60,
  autoDistributePoints: true,
});

// 计算水平母线连接点
const horizontalConnectionPoints = ref(
  generateBusbarConnectionPoints(
    BusbarOrientation.HORIZONTAL,
    { width: horizontalConfig.value.length, height: horizontalConfig.value.width + 40 },
    horizontalConfig.value.connectionPointCount,
    horizontalConfig.value.autoDistributePoints
  )
);

// 计算垂直母线连接点
const verticalConnectionPoints = ref(
  generateBusbarConnectionPoints(
    BusbarOrientation.VERTICAL,
    { width: verticalConfig.value.width + 40, height: verticalConfig.value.length },
    verticalConfig.value.connectionPointCount,
    verticalConfig.value.autoDistributePoints
  )
);

// 水平母线样式
const horizontalBusbarStyle = computed(() => ({
  width: horizontalConfig.value.length + 'px',
  height: (horizontalConfig.value.width + 40) + 'px',
  position: 'relative',
  margin: '20px auto',
}));

// 垂直母线样式
const verticalBusbarStyle = computed(() => ({
  width: (verticalConfig.value.width + 40) + 'px',
  height: verticalConfig.value.length + 'px',
  position: 'relative',
  margin: '20px auto',
}));

// 生成的SVG
const generatedSVG = computed(() => {
  return generateBusbarSVG(horizontalConfig.value);
});

// 更新水平母线连接点
const updateHorizontalPoints = () => {
  horizontalConnectionPoints.value = generateBusbarConnectionPoints(
    BusbarOrientation.HORIZONTAL,
    { width: horizontalConfig.value.length, height: horizontalConfig.value.width + 40 },
    horizontalConfig.value.connectionPointCount,
    horizontalConfig.value.autoDistributePoints
  );
};

// 更新垂直母线连接点
const updateVerticalPoints = () => {
  verticalConnectionPoints.value = generateBusbarConnectionPoints(
    BusbarOrientation.VERTICAL,
    { width: verticalConfig.value.width + 40, height: verticalConfig.value.length },
    verticalConfig.value.connectionPointCount,
    verticalConfig.value.autoDistributePoints
  );
};
</script>

<style scoped>
.busbar-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h2 {
  color: #1f2937;
  margin-bottom: 8px;
}

.demo-header p {
  color: #6b7280;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
}

.demo-section h3 {
  margin-bottom: 16px;
  color: #374151;
}

.busbar-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: white;
  border: 1px dashed #d1d5db;
  border-radius: 4px;
  margin-bottom: 16px;
}

.busbar {
  border: 2px solid #ff0000;
  background-color: #ff0000;
}

.busbar.horizontal {
  border-radius: 2px;
}

.busbar.vertical {
  border-radius: 2px;
}

.connection-point {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
  border: 2px solid white;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.connection-point:hover {
  background-color: #059669;
  transform: translate(-50%, -50%) scale(1.2);
}

.controls {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.svg-demo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.svg-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 20px;
  min-height: 150px;
}

.svg-code {
  background-color: #f3f4f6;
  border-radius: 4px;
  padding: 12px;
  overflow-x: auto;
}

.svg-code pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

.topology-demo {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 20px;
  background-color: white;
  border-radius: 4px;
}

.power-source,
.generator,
.load {
  padding: 12px;
  text-align: center;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}

.power-source {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #3b82f6;
}

.generator {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #f59e0b;
}

.load {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

.main-busbar {
  grid-column: 1 / -1;
  background-color: #fee2e2;
  color: #991b1b;
  border: 2px solid #ef4444;
  padding: 8px;
  text-align: center;
  font-weight: 600;
}
</style>
