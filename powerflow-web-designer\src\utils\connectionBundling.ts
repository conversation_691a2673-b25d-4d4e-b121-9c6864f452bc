/**
 * Connection bundling utilities for PowerFlow Web Designer
 * Implements algorithms for bundling parallel connections
 */

import type { Position } from '@/types/symbol';
import type { Connection } from '@/types/connection';
import { PathFindingOptions } from './pathFinding';

/**
 * Bundle connection interface
 */
export interface BundleInfo {
  bundleId: string;
  connections: string[];
  path: Position[];
  offset: number;
}

/**
 * Bundle connections that follow similar paths
 * @param connections Array of connections to bundle
 * @param options Path finding options
 * @returns Map of bundle IDs to bundle information
 */
export function bundleConnections(
  connections: Record<string, Connection>,
  options: PathFindingOptions
): Map<string, BundleInfo> {
  // Skip if bundling is disabled
  if (!options.bundleConnections) {
    return new Map();
  }

  // Create a map to store bundles
  const bundles = new Map<string, BundleInfo>();

  // Group connections by source and target
  const connectionGroups = groupConnectionsByEndpoints(connections);

  // Process each group
  let bundleIdCounter = 0;

  for (const group of connectionGroups) {
    if (group.length < 2) {
      // Skip groups with only one connection
      continue;
    }

    // Create a bundle for this group
    const bundleId = `bundle-${bundleIdCounter++}`;
    const connectionIds = group.map(conn => conn.id);

    // Calculate the main path for the bundle
    // For simplicity, use the path of the first connection
    const mainPath = group[0].waypoints || [];

    // Create the bundle info
    bundles.set(bundleId, {
      bundleId,
      connections: connectionIds,
      path: mainPath,
      offset: 0,
    });
  }

  return bundles;
}

/**
 * Group connections by their endpoints
 * @param connections Record of connections
 * @returns Array of connection groups
 */
function groupConnectionsByEndpoints(
  connections: Record<string, Connection>
): Connection[][] {
  // Create a map to store groups
  const groups: Map<string, Connection[]> = new Map();

  // Process each connection
  for (const id in connections) {
    const connection = connections[id];

    // Create a key for the group based on source and target
    const sourceId = connection.source.symbolInstanceId;
    const targetId = connection.target.symbolInstanceId;

    // Create a consistent key regardless of direction
    const key = [sourceId, targetId].sort().join('-');

    // Add to the group
    if (!groups.has(key)) {
      groups.set(key, []);
    }

    groups.get(key)!.push(connection);
  }

  // Convert map to array of groups
  return Array.from(groups.values());
}

/**
 * Calculate offsets for bundled connections
 * @param bundle Bundle information
 * @param bundleSpacing Spacing between bundled connections
 * @returns Map of connection IDs to offset values
 */
export function calculateBundleOffsets(
  bundle: BundleInfo,
  bundleSpacing: number = 5
): Map<string, number> {
  const offsets = new Map<string, number>();

  // Calculate the total width of the bundle
  const totalWidth = (bundle.connections.length - 1) * bundleSpacing;

  // Calculate the starting offset
  const startOffset = -totalWidth / 2;

  // Assign offsets to each connection
  for (let i = 0; i < bundle.connections.length; i++) {
    const connectionId = bundle.connections[i];
    const offset = startOffset + i * bundleSpacing;
    offsets.set(connectionId, offset);
  }

  return offsets;
}

/**
 * Apply bundle offsets to connection paths
 * @param connection Connection to apply offset to
 * @param offset Offset value
 * @param bundlePath Main path of the bundle
 * @returns Updated connection with offset applied
 */
export function applyBundleOffset(
  connection: Connection,
  offset: number,
  bundlePath: Position[]
): Connection {
  if (offset === 0 || !connection.waypoints || connection.waypoints.length < 2) {
    return connection;
  }

  // Create a copy of the waypoints
  const newWaypoints = [...connection.waypoints];

  // Apply offset to each segment
  for (let i = 1; i < newWaypoints.length; i++) {
    const prev = newWaypoints[i - 1];
    const current = newWaypoints[i];

    // Calculate the direction vector of the segment
    const dx = current.x - prev.x;
    const dy = current.y - prev.y;

    // Calculate the length of the segment
    const length = Math.sqrt(dx * dx + dy * dy);

    if (length > 0) {
      // Calculate the normal vector (perpendicular to the segment)
      const nx = -dy / length;
      const ny = dx / length;

      // Apply the offset
      current.x += nx * offset;
      current.y += ny * offset;
    }
  }

  // Return updated connection
  return {
    ...connection,
    waypoints: newWaypoints,
  };
}
