<template>
  <div class="power-flow-animation-editor">
    <h3>Power Flow Animation</h3>
    
    <div v-if="selectedConnection">
      <!-- Enable/Disable Animation -->
      <div class="form-group">
        <a-switch
          v-model:checked="animationEnabled"
          @change="updateAnimation"
        />
        <label class="switch-label">Enable Animation</label>
      </div>
      
      <template v-if="animationEnabled">
        <!-- Animation Type -->
        <div class="form-group">
          <label>Animation Type</label>
          <a-select
            v-model:value="animationType"
            style="width: 100%"
            @change="updateAnimation"
          >
            <a-select-option value="flow">Flow</a-select-option>
            <a-select-option value="pulse">Pulse</a-select-option>
            <a-select-option value="dash">Dash</a-select-option>
          </a-select>
        </div>
        
        <!-- Animation Direction -->
        <div class="form-group">
          <label>Direction</label>
          <a-select
            v-model:value="animationDirection"
            style="width: 100%"
            @change="updateAnimation"
          >
            <a-select-option value="forward">Forward</a-select-option>
            <a-select-option value="backward">Backward</a-select-option>
            <a-select-option value="bidirectional">Bidirectional</a-select-option>
          </a-select>
        </div>
        
        <!-- Animation Speed -->
        <div class="form-group">
          <label>Speed</label>
          <div class="slider-with-value">
            <a-slider
              v-model:value="animationSpeed"
              :min="1"
              :max="10"
              :step="1"
              style="flex: 1"
              @change="updateAnimation"
            />
            <span class="slider-value">{{ animationSpeed }}</span>
          </div>
        </div>
        
        <!-- Animation Color -->
        <div class="form-group">
          <label>Color</label>
          <div class="color-input-group">
            <a-input
              v-model:value="animationColor"
              type="color"
              style="width: 50px"
              @change="updateAnimation"
            />
            <a-button
              size="small"
              @click="resetAnimationColor"
            >
              Reset to Line Color
            </a-button>
          </div>
        </div>
        
        <!-- Animation Preview -->
        <div class="form-group">
          <label>Preview</label>
          <div class="animation-preview">
            <div class="preview-line">
              <div 
                class="preview-animation" 
                :class="[
                  `animation-${animationType}`,
                  `direction-${animationDirection}`,
                  `speed-${animationSpeed}`
                ]"
                :style="{ 
                  backgroundColor: animationColor || selectedConnection.style.strokeColor,
                  animationDuration: `${11 - animationSpeed}s`
                }"
              ></div>
            </div>
          </div>
        </div>
      </template>
      
      <div v-else class="animation-disabled-message">
        Enable animation to configure settings
      </div>
    </div>
    
    <div v-else class="no-selection">
      No connection selected
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { 
  Connection, 
  PowerFlowAnimationType,
  PowerFlowDirection,
  PowerFlowAnimation
} from '@/types/connection';

// Store
const diagramStore = useDiagramStore();

// Computed
const selectedConnection = computed<Connection | null>(() => {
  if (!diagramStore.currentDiagram || !diagramStore.currentDiagram.selectedConnectionId) {
    return null;
  }
  
  return diagramStore.currentDiagram.connections[diagramStore.currentDiagram.selectedConnectionId];
});

// Form state
const animationEnabled = ref<boolean>(false);
const animationType = ref<PowerFlowAnimationType>(PowerFlowAnimationType.FLOW);
const animationDirection = ref<PowerFlowDirection>(PowerFlowDirection.FORWARD);
const animationSpeed = ref<number>(5);
const animationColor = ref<string | undefined>(undefined);

// Initialize form values when selected connection changes
watch(selectedConnection, (connection) => {
  if (!connection) return;
  
  const animation = connection.style.animation;
  
  if (animation) {
    animationEnabled.value = animation.enabled;
    animationType.value = animation.type;
    animationDirection.value = animation.direction;
    animationSpeed.value = animation.speed;
    animationColor.value = animation.color;
  } else {
    // Default values
    animationEnabled.value = false;
    animationType.value = PowerFlowAnimationType.FLOW;
    animationDirection.value = PowerFlowDirection.FORWARD;
    animationSpeed.value = 5;
    animationColor.value = undefined;
  }
}, { immediate: true });

// Methods
const updateAnimation = () => {
  if (!selectedConnection.value) return;
  
  const animation: PowerFlowAnimation = {
    type: animationType.value,
    direction: animationDirection.value,
    speed: animationSpeed.value,
    color: animationColor.value,
    enabled: animationEnabled.value
  };
  
  // Update the connection style with the new animation
  diagramStore.updateConnection({
    ...selectedConnection.value,
    style: {
      ...selectedConnection.value.style,
      animation
    }
  });
};

const resetAnimationColor = () => {
  animationColor.value = undefined;
  updateAnimation();
};
</script>

<style scoped>
.power-flow-animation-editor {
  padding: 16px;
}

.form-group {
  margin-bottom: 16px;
}

label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
}

.switch-label {
  display: inline-block;
  margin-left: 8px;
  vertical-align: middle;
}

.slider-with-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.slider-value {
  width: 20px;
  text-align: right;
}

.color-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.animation-preview {
  height: 60px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.preview-line {
  width: 100%;
  height: 2px;
  background-color: #ddd;
  position: relative;
  overflow: hidden;
}

.preview-animation {
  position: absolute;
  height: 100%;
}

/* Flow animation */
.animation-flow {
  width: 20px;
  animation: flow-animation linear infinite;
}

@keyframes flow-animation {
  0% { left: -20px; }
  100% { left: 100%; }
}

/* Pulse animation */
.animation-pulse {
  width: 4px;
  animation: pulse-animation linear infinite;
}

@keyframes pulse-animation {
  0% { 
    left: -4px;
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }
  100% { 
    left: 100%;
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

/* Dash animation */
.animation-dash {
  width: 100%;
  background: repeating-linear-gradient(
    to right,
    currentColor,
    currentColor 10px,
    transparent 10px,
    transparent 20px
  );
  animation: dash-animation linear infinite;
}

@keyframes dash-animation {
  0% { background-position: 0 0; }
  100% { background-position: 40px 0; }
}

/* Direction modifiers */
.direction-backward {
  animation-direction: reverse;
}

.direction-bidirectional {
  animation-direction: alternate;
}

.no-selection, .animation-disabled-message {
  color: #888;
  font-style: italic;
  margin-top: 16px;
}
</style>
