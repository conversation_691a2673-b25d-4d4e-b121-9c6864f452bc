<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3>属性</h3>
    </div>

    <div class="panel-content">
      <a-empty v-if="!hasSelection" description="未选择元素" />

      <template v-else-if="selectedGroup">
        <!-- Common Properties (Always Visible) -->
        <div class="common-properties">
          <div class="section-title">基本属性</div>
          <a-form layout="vertical" size="small" class="compact-form">
            <!-- Read-only properties as text -->
            <div class="readonly-properties">
              <div class="readonly-item">
                <span class="readonly-label">标识符:</span>
                <span class="readonly-value">{{ selectedGroup.id }}</span>
              </div>
              <div class="readonly-item">
                <span class="readonly-label">位置:</span>
                <span class="readonly-value">X: {{ selectedGroup.position.x }} Y: {{ selectedGroup.position.y }}</span>
              </div>
            </div>

            <a-form-item label="名称">
              <a-input
                v-model:value="selectedGroup.name"
                @change="handleGroupPropertyChange"
              />
            </a-form-item>

            <!-- Size (Width, Height) in one row -->
            <div class="form-row">
              <a-form-item label="尺寸" class="form-row-item">
                <a-row :gutter="8">
                  <a-col :span="12">
                    <a-input-number
                      v-model:value="selectedGroup.size.width"
                      :min="10"
                      :max="10000"
                      placeholder="宽度"
                      style="width: 100%"
                      @change="handleGroupPropertyChange"
                    />
                  </a-col>
                  <a-col :span="12">
                    <a-input-number
                      v-model:value="selectedGroup.size.height"
                      :min="10"
                      :max="10000"
                      placeholder="高度"
                      style="width: 100%"
                      @change="handleGroupPropertyChange"
                    />
                  </a-col>
                </a-row>
              </a-form-item>
            </div>

            <!-- Rotation and Lock in one row -->
            <div class="form-row">
              <a-row :gutter="8">
                <a-col :span="16">
                  <a-form-item label="旋转角度" class="form-row-item">
                    <a-input-number
                      v-model:value="selectedGroup.rotation"
                      :min="0"
                      :max="360"
                      style="width: 100%"
                      @change="handleGroupPropertyChange"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="锁定" class="form-row-item">
                    <a-switch
                      v-model:checked="selectedGroup.locked"
                      @change="handleGroupPropertyChange"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </div>

        <!-- Specific Properties -->
        <div class="specific-properties">
          <div class="section-title">组成员</div>
          <a-form layout="vertical" size="small">
            <a-form-item label="符号">
              <a-tag v-for="symbolId in selectedGroup.symbolInstanceIds" :key="symbolId">
                {{ symbolId }}
              </a-tag>
            </a-form-item>
          </a-form>
        </div>
      </template>

      <template v-else-if="selectedSymbol">
        <!-- Common Properties (Always Visible) -->
        <div class="common-properties">
          <div class="section-title">基本属性</div>
          <a-form layout="vertical" size="small" class="compact-form">
            <!-- Read-only properties as text -->
            <div class="readonly-properties">
              <div class="readonly-item">
                <span class="readonly-label">标识符:</span>
                <span class="readonly-value">{{ selectedSymbol.id }}</span>
              </div>
              <div class="readonly-item">
                <span class="readonly-label">类型:</span>
                <span class="readonly-value">{{ getLocalizedSymbolName(symbolDefinition) }}</span>
              </div>
              <div class="readonly-item">
                <span class="readonly-label">位置:</span>
                <span class="readonly-value">X: {{ selectedSymbol.position.x }} Y: {{ selectedSymbol.position.y }}</span>
              </div>
            </div>

            <!-- Rotation and Scale in one row -->
            <div class="form-row">
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-form-item label="旋转角度" class="form-row-item">
                    <a-input-number
                      v-model:value="selectedSymbol.rotation"
                      :step="15"
                      style="width: 100%"
                      @change="handleSymbolPropertyChange"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="缩放比例" class="form-row-item">
                    <a-input-number
                      v-model:value="selectedSymbol.scale"
                      :step="0.1"
                      :min="0.1"
                      :max="10"
                      style="width: 100%"
                      @change="handleSymbolPropertyChange"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </div>

        <!-- Specific Properties -->
        <div class="specific-properties">
          <!-- Style properties -->
          <div class="section-title">样式</div>
          <a-form layout="vertical" size="small" class="compact-form">
            <!-- Colors in one row -->
            <div class="form-row">
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-form-item label="填充颜色" class="form-row-item">
                    <a-input
                      v-model:value="symbolProperties.fillColor"
                      type="color"
                      style="width: 100%"
                      @change="handleSymbolStyleChange('fillColor')"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="边框颜色" class="form-row-item">
                    <a-input
                      v-model:value="symbolProperties.strokeColor"
                      type="color"
                      style="width: 100%"
                      @change="handleSymbolStyleChange('strokeColor')"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <a-form-item label="线宽">
              <a-input-number
                v-model:value="symbolProperties.lineWidth"
                :step="0.5"
                :min="0.5"
                :max="10"
                style="width: 100%"
                @change="handleSymbolStyleChange('lineWidth')"
              />
            </a-form-item>
          </a-form>

          <!-- Binding slots (hidden for busbar symbols) -->
          <template v-if="!isBusbarSymbol">
            <div class="section-title">绑定</div>
            <a-form layout="vertical" size="small" class="compact-form">
              <template v-for="slot in symbolBindingSlots" :key="slot.id">
                <a-form-item :label="slot.name">
                  <!-- Boolean binding -->
                  <a-switch
                    v-if="slot.dataType === 'boolean'"
                    :checked="getBindingValue(slot.id)"
                    @change="(value) => handleBindingChange(slot.id, value)"
                  />

                  <!-- Number binding -->
                  <a-input-number
                    v-else-if="slot.dataType === 'number'"
                    :value="getBindingValue(slot.id)"
                    style="width: 100%"
                    @change="(value) => handleBindingChange(slot.id, value)"
                  />

                  <!-- Enum binding -->
                  <a-select
                    v-else-if="slot.dataType === 'enum'"
                    :value="getBindingValue(slot.id)"
                    style="width: 100%"
                    size="small"
                    @change="(value) => handleBindingChange(slot.id, value)"
                  >
                    <a-select-option
                      v-for="value in slot.enumValues"
                      :key="value"
                      :value="value"
                    >
                      {{ value }}
                    </a-select-option>
                  </a-select>

                  <!-- String binding -->
                  <a-input
                    v-else
                    :value="getBindingValue(slot.id)"
                    @change="(e) => handleBindingChange(slot.id, e.target.value)"
                  />
                </a-form-item>
              </template>
            </a-form>

            <!-- Value Displays -->
            <value-display-editor />

            <!-- Trend Charts -->
            <trend-chart-editor />
          </template>

          <!-- Busbar Properties (for busbar symbols) -->
          <busbar-properties-editor
            v-if="isBusbarSymbol"
            :symbol-instance-id="selectedSymbol.id"
          />
        </div>
      </template>

      <template v-else-if="selectedConnection">
        <!-- Common Properties (Always Visible) -->
        <div class="common-properties">
          <div class="section-title">基本属性</div>
          <a-form layout="vertical" size="small" class="compact-form">
            <!-- Read-only properties as text -->
            <div class="readonly-properties">
              <div class="readonly-item">
                <span class="readonly-label">标识符:</span>
                <span class="readonly-value">{{ selectedConnection.id }}</span>
              </div>
            </div>

            <!-- Type and Label in one row -->
            <div class="form-row">
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-form-item label="连接类型" class="form-row-item">
                    <a-select
                      v-model:value="selectedConnection.type"
                      style="width: 100%"
                      size="small"
                      @change="handleConnectionPropertyChange"
                    >
                      <a-select-option value="cable">电缆</a-select-option>
                      <a-select-option value="busbar">母线</a-select-option>
                      <a-select-option value="control">控制</a-select-option>
                      <a-select-option value="signal">信号</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="标签" class="form-row-item">
                    <a-input
                      v-model:value="selectedConnection.label"
                      @change="handleConnectionPropertyChange"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </div>

        <!-- Specific Properties -->
        <div class="specific-properties">
          <!-- Connection Style Editor -->
          <connection-style-editor />

          <!-- Connection Labels Panel -->
          <a-divider style="margin: 8px 0" />
          <connection-labels-panel :connection="selectedConnection" />
        </div>
      </template>

      <template v-else-if="selectedTextElement">
        <!-- Common Properties (Always Visible) -->
        <div class="common-properties">
          <div class="section-title">基本属性</div>
          <a-form layout="vertical" size="small" class="compact-form">
            <!-- Read-only properties as text -->
            <div class="readonly-properties">
              <div class="readonly-item">
                <span class="readonly-label">标识符:</span>
                <span class="readonly-value">{{ selectedTextElement.id }}</span>
              </div>
              <div class="readonly-item">
                <span class="readonly-label">位置:</span>
                <span class="readonly-value">X: {{ selectedTextElement.position.x }} Y: {{ selectedTextElement.position.y }}</span>
              </div>
            </div>

            <!-- Size (Width, Height) in one row -->
            <div class="form-row">
              <a-form-item label="尺寸" class="form-row-item">
                <a-row :gutter="8">
                  <a-col :span="12">
                    <a-input-number
                      v-model:value="selectedTextElement.size.width"
                      :min="20"
                      :max="1000"
                      :step="1"
                      placeholder="宽度"
                      style="width: 100%"
                      @change="handleTextElementPropertyChange"
                    />
                  </a-col>
                  <a-col :span="12">
                    <a-input-number
                      v-model:value="selectedTextElement.size.height"
                      :min="20"
                      :max="1000"
                      :step="1"
                      placeholder="高度"
                      style="width: 100%"
                      @change="handleTextElementPropertyChange"
                    />
                  </a-col>
                </a-row>
              </a-form-item>
            </div>

            <a-form-item label="旋转角度">
              <a-input-number
                v-model:value="selectedTextElement.rotation"
                :step="15"
                :min="0"
                :max="360"
                style="width: 100%"
                @change="handleTextElementPropertyChange"
              />
            </a-form-item>
          </a-form>
        </div>

        <!-- Specific Properties -->
        <div class="specific-properties">
          <div class="section-title">文本内容</div>
          <a-form layout="vertical" size="small" class="compact-form">
            <a-form-item label="内容">
              <a-textarea
                v-model:value="selectedTextElement.content"
                :rows="3"
                placeholder="输入文本内容"
                @change="handleTextElementPropertyChange"
              />
            </a-form-item>
          </a-form>

          <!-- Text Style -->
          <div class="section-title">文本样式</div>
          <a-form layout="vertical" size="small" class="compact-form">
            <!-- Font size and color in one row -->
            <div class="form-row">
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-form-item label="字体大小" class="form-row-item">
                    <a-input-number
                      v-model:value="selectedTextElement.style.fontSize"
                      :min="8"
                      :max="72"
                      :step="1"
                      style="width: 100%"
                      @change="handleTextElementPropertyChange"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="文本颜色" class="form-row-item">
                    <a-input
                      v-model:value="selectedTextElement.style.color"
                      type="color"
                      style="width: 100%"
                      @change="handleTextElementPropertyChange"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <a-form-item label="字体">
              <a-select
                v-model:value="selectedTextElement.style.fontFamily"
                style="width: 100%"
                @change="handleTextElementPropertyChange"
              >
                <a-select-option value="Arial">Arial</a-select-option>
                <a-select-option value="Helvetica">Helvetica</a-select-option>
                <a-select-option value="Times New Roman">Times New Roman</a-select-option>
                <a-select-option value="Courier New">Courier New</a-select-option>
                <a-select-option value="SimSun">宋体</a-select-option>
                <a-select-option value="SimHei">黑体</a-select-option>
                <a-select-option value="Microsoft YaHei">微软雅黑</a-select-option>
              </a-select>
            </a-form-item>

            <!-- Alignment and weight in one row -->
            <div class="form-row">
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-form-item label="对齐方式" class="form-row-item">
                    <a-select
                      v-model:value="selectedTextElement.style.textAlign"
                      style="width: 100%"
                      @change="handleTextElementPropertyChange"
                    >
                      <a-select-option value="left">左对齐</a-select-option>
                      <a-select-option value="center">居中</a-select-option>
                      <a-select-option value="right">右对齐</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="字体粗细" class="form-row-item">
                    <a-select
                      v-model:value="selectedTextElement.style.fontWeight"
                      style="width: 100%"
                      @change="handleTextElementPropertyChange"
                    >
                      <a-select-option value="normal">正常</a-select-option>
                      <a-select-option value="bold">粗体</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <!-- Style and decoration in one row -->
            <div class="form-row">
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-form-item label="字体样式" class="form-row-item">
                    <a-select
                      v-model:value="selectedTextElement.style.fontStyle"
                      style="width: 100%"
                      @change="handleTextElementPropertyChange"
                    >
                      <a-select-option value="normal">正常</a-select-option>
                      <a-select-option value="italic">斜体</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="文本装饰" class="form-row-item">
                    <a-select
                      v-model:value="selectedTextElement.style.textDecoration"
                      style="width: 100%"
                      @change="handleTextElementPropertyChange"
                    >
                      <a-select-option value="none">无</a-select-option>
                      <a-select-option value="underline">下划线</a-select-option>
                      <a-select-option value="line-through">删除线</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';

import { BindingDataType, BindingSlot } from '@/types/symbol';
import ConnectionStyleEditor from '@/components/sidebar/ConnectionStyleEditor.vue';
import ConnectionLabelsPanel from './ConnectionLabelsPanel.vue';
import ValueDisplayEditor from './ValueDisplayEditor.vue';
import TrendChartEditor from './TrendChartEditor.vue';
import BusbarPropertiesEditor from './BusbarPropertiesEditor.vue';

// Store
const diagramStore = useDiagramStore();

// Computed
const hasSelection = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  if (!diagramStore.currentDiagram.selectedSymbolIds ||
      !diagramStore.currentDiagram.selectedConnectionIds ||
      !diagramStore.currentDiagram.selectedTextElementIds ||
      !diagramStore.currentDiagram.selectedGroupIds) return false;

  return (
    diagramStore.currentDiagram.selectedSymbolIds.length > 0 ||
    diagramStore.currentDiagram.selectedConnectionIds.length > 0 ||
    diagramStore.currentDiagram.selectedTextElementIds.length > 0 ||
    diagramStore.currentDiagram.selectedGroupIds.length > 0
  );
});

const selectedSymbol = computed(() => {
  if (!diagramStore.currentDiagram) return null;
  if (!diagramStore.currentDiagram.selectedSymbolIds) return null;
  if (diagramStore.currentDiagram.selectedSymbolIds.length === 0) return null;

  const selectedId = diagramStore.currentDiagram.selectedSymbolIds[0];
  if (!selectedId) return null;

  return diagramStore.currentDiagram.symbolInstances[selectedId];
});

const selectedConnection = computed(() => {
  if (!diagramStore.currentDiagram) return null;
  if (!diagramStore.currentDiagram.selectedConnectionIds) return null;
  if (diagramStore.currentDiagram.selectedConnectionIds.length === 0) return null;

  const selectedId = diagramStore.currentDiagram.selectedConnectionIds[0];
  if (!selectedId) return null;

  return diagramStore.currentDiagram.connections[selectedId];
});

const selectedTextElement = computed(() => {
  if (!diagramStore.currentDiagram) return null;
  if (!diagramStore.currentDiagram.selectedTextElementIds) return null;
  if (diagramStore.currentDiagram.selectedTextElementIds.length === 0) return null;

  const selectedId = diagramStore.currentDiagram.selectedTextElementIds[0];
  if (!selectedId) return null;

  return diagramStore.currentDiagram.textElements[selectedId];
});

const selectedGroup = computed(() => {
  if (!diagramStore.currentDiagram) return null;
  if (!diagramStore.currentDiagram.selectedGroupIds) return null;
  if (diagramStore.currentDiagram.selectedGroupIds.length === 0) return null;

  const selectedId = diagramStore.currentDiagram.selectedGroupIds[0];
  if (!selectedId) return null;

  return diagramStore.currentDiagram.groups[selectedId];
});

const symbolDefinition = computed(() => {
  if (!selectedSymbol.value) return null;

  return diagramStore.getSymbolDefinition(selectedSymbol.value.definitionId);
});

// Check if the selected symbol is a busbar
const isBusbarSymbol = computed(() => {
  if (!symbolDefinition.value) return false;
  return symbolDefinition.value.category === 'busbar' ||
         symbolDefinition.value.id.includes('busbar');
});

const symbolBindingSlots = computed(() => {
  if (!symbolDefinition.value) return [];

  return symbolDefinition.value.bindingSlots;
});

const symbolProperties = computed(() => {
  if (!selectedSymbol.value) {
    return {
      fillColor: '#ffffff',
      strokeColor: '#000000',
      lineWidth: 1,
    };
  }

  // Merge default properties from definition with instance properties
  const defaultProps = symbolDefinition.value?.properties || {
    fillColor: '#ffffff',
    strokeColor: '#000000',
    lineWidth: 1,
  };

  return {
    ...defaultProps,
    ...selectedSymbol.value.properties,
  };
});

const lineDashType = computed({
  get() {
    if (!selectedConnection.value) return 'solid';

    const dash = selectedConnection.value.style.lineDash;
    if (!dash || dash.length === 0) return 'solid';
    if (dash[0] === 5 && dash[1] === 5) return 'dashed';
    if (dash[0] === 2 && dash[1] === 2) return 'dotted';

    return 'solid';
  },
  set(value: string) {
    if (!selectedConnection.value) return;

    if (value === 'solid') {
      selectedConnection.value.style.lineDash = undefined;
    } else if (value === 'dashed') {
      selectedConnection.value.style.lineDash = [5, 5];
    } else if (value === 'dotted') {
      selectedConnection.value.style.lineDash = [2, 2];
    }

    diagramStore.modified = true;
  }
});

// Methods
const getBindingValue = (slotId: string) => {
  if (!selectedSymbol.value) return null;

  const binding = selectedSymbol.value.bindings[slotId];
  if (!binding) {
    // Find default value from binding slot
    const slot = symbolBindingSlots.value.find(s => s.id === slotId);
    return slot?.defaultValue;
  }

  return binding.value;
};

const handleSymbolPropertyChange = () => {
  if (!selectedSymbol.value) return;

  // Mark the diagram as modified
  diagramStore.modified = true;
};

const handleSymbolStyleChange = (property: string) => {
  if (!selectedSymbol.value) return;

  // Ensure properties object exists
  if (!selectedSymbol.value.properties) {
    selectedSymbol.value.properties = {};
  }

  // Update the property
  selectedSymbol.value.properties[property] = symbolProperties.value[property];

  // Mark the diagram as modified
  diagramStore.modified = true;
};

const handleBindingChange = (slotId: string, value: any) => {
  if (!selectedSymbol.value) return;

  // Ensure bindings object exists
  if (!selectedSymbol.value.bindings) {
    selectedSymbol.value.bindings = {};
  }

  // Create or update the binding
  selectedSymbol.value.bindings[slotId] = {
    slotId,
    value,
  };

  // Mark the diagram as modified
  diagramStore.modified = true;
};

const handleConnectionPropertyChange = () => {
  if (!selectedConnection.value) return;

  // Mark the diagram as modified
  diagramStore.modified = true;
};

const handleConnectionStyleChange = (property: string) => {
  if (!selectedConnection.value) return;

  // Mark the diagram as modified
  diagramStore.modified = true;
};

const handleLineDashChange = () => {
  // This is handled by the computed property setter
};

const handleTextElementPropertyChange = () => {
  if (!selectedTextElement.value) return;

  // Ensure style object exists
  if (!selectedTextElement.value.style) {
    selectedTextElement.value.style = {
      fontSize: 14,
      fontFamily: 'Arial',
      color: '#000000',
      textAlign: 'left',
      fontWeight: 'normal',
      fontStyle: 'normal',
      textDecoration: 'none',
    };
  }

  // Ensure size object exists
  if (!selectedTextElement.value.size) {
    selectedTextElement.value.size = { width: 100, height: 30 };
  }

  // Mark the diagram as modified
  diagramStore.modified = true;
};

const handleGroupPropertyChange = () => {
  if (!selectedGroup.value) return;

  // Mark the diagram as modified
  diagramStore.modified = true;
};

const getLocalizedSymbolName = (symbol: any) => {
  if (!symbol) return '';

  // 根据符号ID返回中文名称
  const symbolNames: Record<string, string> = {
    'circuit-breaker': '断路器',
    'disconnector-switch': '隔离开关',
    'transformer': '变压器',
    'busbar': '母线',
    'busbar-horizontal': '水平母线',
    'busbar-vertical': '垂直母线',
    'generator': '发电机',
    'diesel-generator': '柴油发电机',
    'load': '负载',
    'capacitor': '电容器',
    'reactor': '电抗器',
    'current-transformer': '电流互感器',
    'voltage-transformer': '电压互感器',
    'relay': '继电器',
    'earth-switch': '接地开关',
    'fuse': '熔断器',
    'load-break-switch': '负荷开关',
    'autotransformer': '自耦变压器',
    'three-winding-transformer': '三绕组变压器',
    'bus-coupler': '母联',
    'synchronous-generator': '同步发电机',
    'asynchronous-generator': '异步发电机',
    'wind-generator': '风力发电机',
    'solar-panel': '太阳能电池板',
    'industrial-load': '工业负载',
    'residential-load': '民用负载',
    'capacitor-bank': '电容器组',
    'shunt-reactor': '并联电抗器',
    'series-reactor': '串联电抗器',
    'power-meter': '功率计',
    'over-current-relay': '过流继电器',
    'distance-relay': '距离继电器',
    'differential-relay': '差动继电器',
  };

  return symbolNames[symbol.id] || symbol.name;
};
</script>

<style scoped>
.property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-left: 1px solid #e8e8e8;
  overflow: hidden;
}

.panel-header {
  padding: 5px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #f5f5f5;
}

.panel-header h3 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 500;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 5px;
  min-height: 0;
}

/* Common Properties Section */
.common-properties {
  background-color: #fafafa;
  border-radius: 4px;
  margin-bottom: 8px;
  padding: 8px;
}

/* Specific Properties Section */
.specific-properties {
  padding: 4px 0;
}

/* Read-only Properties */
.readonly-properties {
  margin-bottom: 12px;
}

.readonly-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 2px 0;
}

.readonly-label {
  font-size: 11px;
  color: #666;
  min-width: 50px;
  margin-right: 8px;
}

.readonly-value {
  font-size: 11px;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

.section-title {
  font-weight: 500;
  margin: 8px 0 6px;
  padding-bottom: 2px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
  color: var(--primary-color);
}

.section-title:first-child {
  margin-top: 0;
}

/* Compact Form Styles */
.compact-form {
  margin: 0;
}

.form-row {
  margin-bottom: 8px;
}

.form-row-item {
  margin-bottom: 0 !important;
}

/* 紧凑型表单样式 */
:deep(.ant-form-item) {
  margin-bottom: 8px;
}

:deep(.ant-form-item-label > label) {
  font-size: 12px;
  height: 18px;
  line-height: 18px;
}

:deep(.ant-form-item-control-input) {
  min-height: 24px;
}



/* Row and Column Spacing */
:deep(.ant-row) {
  margin-left: -4px;
  margin-right: -4px;
}

:deep(.ant-col) {
  padding-left: 4px;
  padding-right: 4px;
}

/* Input and Control Sizing */
:deep(.ant-input-number) {
  font-size: 12px;
}

:deep(.ant-select) {
  font-size: 12px;
}

:deep(.ant-input) {
  font-size: 12px;
}

:deep(.ant-switch) {
  min-width: 28px;
  height: 16px;
  line-height: 16px;
}

:deep(.ant-switch-inner) {
  font-size: 10px;
}

/* Color input specific styling */
:deep(.ant-input[type="color"]) {
  padding: 1px;
  height: 24px;
}
</style>
