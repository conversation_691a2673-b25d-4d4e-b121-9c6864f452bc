<template>
  <div class="connection-labels-panel">
    <div class="panel-section">
      <div class="section-header">
        <h3>Labels</h3>
        <a-button type="primary" size="small" @click="showAddLabelEditor">
          <template #icon><plus-outlined /></template>
          Add Label
        </a-button>
      </div>

      <div class="labels-list">
        <a-empty v-if="!connection.labels || connection.labels.length === 0" description="No labels" />

        <a-list v-else>
          <a-list-item v-for="label in connection.labels" :key="label.id">
            <a-list-item-meta>
              <template #title>{{ label.text }}</template>
              <template #description>
                Position: {{ getLabelPositionText(label.position) }}
                {{ label.positionPercentage ? `(${label.positionPercentage}%)` : '' }}
              </template>
            </a-list-item-meta>

            <template #actions>
              <a-button type="text" @click="editLabel(label)">
                <template #icon><edit-outlined /></template>
              </a-button>
              <a-button type="text" danger @click="deleteLabel(label.id)">
                <template #icon><delete-outlined /></template>
              </a-button>
            </template>
          </a-list-item>
        </a-list>
      </div>
    </div>

    <div class="panel-section">
      <div class="section-header">
        <h3>Annotations</h3>
        <a-button type="primary" size="small" @click="showAddAnnotationEditor">
          <template #icon><plus-outlined /></template>
          Add Annotation
        </a-button>
      </div>

      <div class="annotations-list">
        <a-empty v-if="!connection.annotations || connection.annotations.length === 0" description="No annotations" />

        <a-list v-else>
          <a-list-item v-for="annotation in connection.annotations" :key="annotation.id">
            <a-list-item-meta>
              <template #title>{{ getAnnotationTitle(annotation) }}</template>
              <template #description>
                Position: {{ getLabelPositionText(annotation.position) }}
                {{ annotation.positionPercentage ? `(${annotation.positionPercentage}%)` : '' }}
              </template>
            </a-list-item-meta>

            <template #actions>
              <a-button type="text" @click="editAnnotation(annotation)">
                <template #icon><edit-outlined /></template>
              </a-button>
              <a-button type="text" danger @click="deleteAnnotation(annotation.id)">
                <template #icon><delete-outlined /></template>
              </a-button>
            </template>
          </a-list-item>
        </a-list>
      </div>
    </div>

    <!-- Label Editor Modal -->
    <a-modal
      v-model:visible="showLabelEditor"
      :title="editingLabel ? 'Edit Label' : 'Add Label'"
      :footer="null"
      width="500px"
      :destroyOnClose="true"
    >
      <connection-label-editor
        :label="editingLabel"
        :connection-id="connection.id"
        @save="saveLabel"
        @close="closeLabelEditor"
      />
    </a-modal>

    <!-- Annotation Editor Modal -->
    <a-modal
      v-model:visible="showAnnotationEditor"
      :title="editingAnnotation ? 'Edit Annotation' : 'Add Annotation'"
      :footer="null"
      width="500px"
      :destroyOnClose="true"
    >
      <connection-annotation-editor
        :annotation="editingAnnotation"
        :connection-id="connection.id"
        @save="saveAnnotation"
        @close="closeAnnotationEditor"
      />
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import {
  Connection,
  ConnectionLabel,
  ConnectionAnnotation,
  LabelPosition,
  AnnotationType
} from '@/types/connection';
import { useDiagramStore } from '@/stores/diagram';
import ConnectionLabelEditor from './ConnectionLabelEditor.vue';
import ConnectionAnnotationEditor from './ConnectionAnnotationEditor.vue';

// Props
const props = defineProps<{
  connection: Connection;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const showLabelEditor = ref(false);
const showAnnotationEditor = ref(false);
const editingLabel = ref<ConnectionLabel | undefined>(undefined);
const editingAnnotation = ref<ConnectionAnnotation | undefined>(undefined);

// Methods
const getLabelPositionText = (position: LabelPosition): string => {
  switch (position) {
    case LabelPosition.START:
      return 'Start';
    case LabelPosition.MIDDLE:
      return 'Middle';
    case LabelPosition.END:
      return 'End';
    case LabelPosition.CUSTOM:
      return 'Custom';
    default:
      return 'Unknown';
  }
};

const getAnnotationTitle = (annotation: ConnectionAnnotation): string => {
  switch (annotation.type) {
    case AnnotationType.TEXT:
      return `Text: ${annotation.content}`;
    case AnnotationType.ICON:
      return `Icon: ${annotation.content}`;
    case AnnotationType.ARROW:
      return `Arrow: ${annotation.content}`;
    case AnnotationType.CUSTOM:
      return `Custom: ${annotation.content}`;
    default:
      return annotation.content;
  }
};

// Label methods
const showAddLabelEditor = () => {
  editingLabel.value = undefined;
  showLabelEditor.value = true;
};

const editLabel = (label: ConnectionLabel) => {
  editingLabel.value = label;
  showLabelEditor.value = true;
};

const closeLabelEditor = () => {
  showLabelEditor.value = false;
  editingLabel.value = undefined;
};

const saveLabel = (label: ConnectionLabel) => {
  const updatedConnection = { ...props.connection };

  if (!updatedConnection.labels) {
    updatedConnection.labels = [];
  }

  // Check if we're editing an existing label
  const existingLabelIndex = updatedConnection.labels.findIndex(l => l.id === label.id);

  if (existingLabelIndex >= 0) {
    // Update existing label
    updatedConnection.labels[existingLabelIndex] = label;
  } else {
    // Add new label
    updatedConnection.labels.push(label);
  }

  // Update the connection in the store
  diagramStore.updateConnectionWithValidation(updatedConnection);

  // Close the editor
  closeLabelEditor();
};

const deleteLabel = (labelId: string) => {
  const updatedConnection = { ...props.connection };

  if (!updatedConnection.labels) return;

  // Filter out the label to delete
  updatedConnection.labels = updatedConnection.labels.filter(l => l.id !== labelId);

  // Update the connection in the store
  diagramStore.updateConnectionWithValidation(updatedConnection);
};

// Annotation methods
const showAddAnnotationEditor = () => {
  editingAnnotation.value = undefined;
  showAnnotationEditor.value = true;
};

const editAnnotation = (annotation: ConnectionAnnotation) => {
  editingAnnotation.value = annotation;
  showAnnotationEditor.value = true;
};

const closeAnnotationEditor = () => {
  showAnnotationEditor.value = false;
  editingAnnotation.value = undefined;
};

const saveAnnotation = (annotation: ConnectionAnnotation) => {
  const updatedConnection = { ...props.connection };

  if (!updatedConnection.annotations) {
    updatedConnection.annotations = [];
  }

  // Check if we're editing an existing annotation
  const existingAnnotationIndex = updatedConnection.annotations.findIndex(a => a.id === annotation.id);

  if (existingAnnotationIndex >= 0) {
    // Update existing annotation
    updatedConnection.annotations[existingAnnotationIndex] = annotation;
  } else {
    // Add new annotation
    updatedConnection.annotations.push(annotation);
  }

  // Update the connection in the store
  diagramStore.updateConnectionWithValidation(updatedConnection);

  // Close the editor
  closeAnnotationEditor();
};

const deleteAnnotation = (annotationId: string) => {
  const updatedConnection = { ...props.connection };

  if (!updatedConnection.annotations) return;

  // Filter out the annotation to delete
  updatedConnection.annotations = updatedConnection.annotations.filter(a => a.id !== annotationId);

  // Update the connection in the store
  diagramStore.updateConnectionWithValidation(updatedConnection);
};
</script>

<style scoped>
.connection-labels-panel {
  padding: 16px;
}

.panel-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.labels-list,
.annotations-list {
  margin-bottom: 16px;
}
</style>
