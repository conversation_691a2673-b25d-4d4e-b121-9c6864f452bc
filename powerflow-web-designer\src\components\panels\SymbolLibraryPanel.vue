<template>
  <div class="symbol-library-panel">
    <div class="panel-header">
      <a-input-search
        v-model:value="searchText"
        placeholder="搜索符号"
        style="width: 100%"
        @change="handleSearch"
      />
    </div>

    <div class="panel-content">
      <a-collapse v-model:activeKey="activeCategories">
        <a-collapse-panel
          v-for="category in filteredCategories"
          :key="category.id"
          :header="getCategoryName(category.id)"
        >
          <div class="symbol-grid">
            <div
              v-for="symbol in getSymbolsByCategory(category.id)"
              :key="symbol.id"
              class="symbol-item"
              draggable="true"
              @dragstart="handleDragStart($event, symbol)"
            >
              <div class="symbol-preview" v-html="sanitizeSvg(symbol.svg)"></div>
              <div class="symbol-name">{{ getLocalizedSymbolName(symbol) }}</div>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { symbolCategories } from '@/utils/symbolLibrary';
import { SymbolCategory } from '@/types/symbol';

// Store
const diagramStore = useDiagramStore();

// State
const searchText = ref('');
const activeCategories = ref<string[]>(Object.values(SymbolCategory));

// Computed
const filteredCategories = computed(() => {
  if (!searchText.value) {
    return symbolCategories;
  }

  const searchLower = searchText.value.toLowerCase();

  // Filter categories that have matching symbols
  return symbolCategories.filter(category => {
    const symbols = getSymbolsByCategory(category.id);
    return symbols.length > 0;
  });
});

// Methods
const getSymbolsByCategory = (categoryId: string) => {
  const symbols = diagramStore.symbolLibrary.filter(symbol => symbol.category === categoryId);

  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase();
    return symbols.filter(symbol =>
      symbol.name.toLowerCase().includes(searchLower)
    );
  }

  return symbols;
};

const handleSearch = () => {
  // If search text is not empty, expand all categories
  if (searchText.value) {
    activeCategories.value = Object.values(SymbolCategory);
  }
};

const handleDragStart = (event: DragEvent, symbol: any) => {
  console.log('Drag start event for symbol:', symbol.id);

  if (event.dataTransfer) {
    // 设置允许的拖放效果
    event.dataTransfer.effectAllowed = 'copy';

    // 设置拖放数据
    const dragData = JSON.stringify({
      type: 'symbol',
      symbolId: symbol.id,
    });

    // 设置多种数据格式，确保兼容性
    event.dataTransfer.setData('application/json', dragData);
    event.dataTransfer.setData('text/plain', dragData);

    console.log('Drag data set:', dragData);

    // 创建拖放图像
    try {
      const dragImage = document.createElement('div');
      dragImage.innerHTML = sanitizeSvg(symbol.svg);
      dragImage.style.position = 'absolute';
      dragImage.style.top = '-1000px';
      dragImage.style.left = '0';
      dragImage.style.width = '50px';
      dragImage.style.height = '50px';
      dragImage.style.backgroundColor = 'white';
      dragImage.style.border = '1px solid #ccc';
      dragImage.style.borderRadius = '4px';
      dragImage.style.padding = '5px';
      dragImage.style.zIndex = '9999';
      document.body.appendChild(dragImage);

      // 设置拖放图像
      event.dataTransfer.setDragImage(dragImage, 25, 25);

      // 清理拖放图像
      setTimeout(() => {
        try {
          document.body.removeChild(dragImage);
        } catch (error) {
          console.warn('Error removing drag image:', error);
        }
      }, 100);
    } catch (error) {
      console.error('Error setting drag image:', error);
    }
  } else {
    console.warn('No dataTransfer object in dragStart event');
  }
};

const sanitizeSvg = (svg: string) => {
  // Basic sanitization to prevent XSS
  // In a production app, use a proper sanitization library
  return svg
    .replace(/javascript:/gi, '')
    .replace(/on\w+=/gi, '')
    .replace(/onerror/gi, '')
    .replace(/data:/gi, '');
};

const getLocalizedSymbolName = (symbol: any) => {
  // 根据符号ID返回中文名称 - 包含GB标准符号
  const symbolNames: Record<string, string> = {
    // 原有符号
    'circuit-breaker': '断路器',
    'disconnector': '隔离开关',
    'transformer': '变压器',
    'busbar': '母线',
    'generator': '发电机',
    'load': '负载',
    'current-transformer': '电流互感器',
    'voltage-transformer': '电压互感器',
    'fuse': '熔断器',
    'ground': '接地',

    // GB标准变压器
    'dry-type-transformer': '干式变压器',
    'oil-immersed-transformer': '油浸式变压器',
    'auto-transformer': '自耦变压器',

    // GB标准开关设备
    'load-switch': '负荷开关',
    'earthing-switch': '接地开关',
    'motor-operated-switch': '电动操作开关',
    'sf6-circuit-breaker': 'SF6断路器',

    // GB标准电机
    'induction-motor': '异步电动机',
    'synchronous-motor': '同步电动机',
    'dc-motor': '直流电动机',

    // GB标准测量设备
    'ammeter': '电流表',
    'voltmeter': '电压表',
    'power-factor-meter': '功率因数表',
    'energy-meter': '电能表',
    'frequency-meter': '频率表',

    // GB标准保护设备
    'lightning-arrester': '避雷器',
    'surge-protector': '浪涌保护器',
    'overcurrent-relay': '过流继电器',
    'differential-relay': '差动继电器',

    // GB标准输电设备
    'overhead-line': '架空线路',
    'underground-cable': '地下电缆',
    'transmission-tower': '输电塔',

    // GB标准电容器
    'power-capacitor': '电力电容器',
    'capacitor-bank': '电容器组',
    'coupling-capacitor': '耦合电容器',

    // GB标准电抗器
    'current-limiting-reactor': '限流电抗器',
    'shunt-reactor': '并联电抗器',
    'smoothing-reactor': '平波电抗器',

    // GB标准继电器
    'distance-relay': '距离继电器',
    'frequency-relay': '频率继电器',
    'voltage-relay': '电压继电器',
  };

  return symbolNames[symbol.id] || symbol.name;
};

const getCategoryName = (categoryId: string) => {
  // 返回分类的中文名称 - 包含GB标准分类
  const categoryNames: Record<string, string> = {
    'datacenter': '数据中心',
    'switchgear': '开关设备',
    'transformer': '变压器',
    'busbar': '母线',
    'measurement': '测量设备',
    'protection': '保护设备',
    'distribution': '配电设备',
    'generator': '发电机',
    'load': '负载',
    'motor': '电动机',
    'transmission': '输电设备',
    'capacitor': '电容器',
    'reactor': '电抗器',
    'relay': '继电器',
    'other': '其他',
  };

  return categoryNames[categoryId] || categoryId;
};
</script>

<style scoped>
.symbol-library-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
}

.panel-header {
  padding: 5px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #f5f5f5;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.symbol-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2px;
  padding: 2px;
}

.symbol-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2px;
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  cursor: grab;
  transition: all 0.3s;
  position: relative;
  margin: 1px;
  aspect-ratio: 1 / 1;
}

.symbol-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.symbol-preview {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.symbol-preview :deep(svg) {
  width: 100%;
  height: 100%;
  color: #333;
}

.symbol-name {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120%;
  z-index: 10;
  border-radius: 2px;
  pointer-events: none;
}

.symbol-item:hover .symbol-name {
  display: block;
}
</style>
