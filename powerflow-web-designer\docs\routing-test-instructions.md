# Unified Routing System Test Instructions

## Test Setup

1. **Open PowerFlow Web Designer**: Navigate to http://localhost:5173
2. **Open Browser Console**: Press F12 and go to Console tab
3. **Clear Console**: Click clear button to start fresh

## Test 1: Initial Connection Creation

### Steps:
1. **Add two symbols** to the diagram (e.g., Generator and Load)
2. **Position them non-aligned** (not vertically or horizontally aligned)
3. **Create a connection** between them using connection points
4. **Check console output** for:
   ```
   Using mandatory orthogonal routing for connection [ID]: {
     mandatoryOrthogonal: true,
     distance: [number],
     isCloseProximity: [boolean],
     needsObstacleAvoidance: [boolean],
     bendMinimization: true,
     pathSegments: [number],
     bends: [number]
   }
   ```

### Expected Results:
- ✅ Connection shows L-shaped path (1 bend)
- ✅ No diagonal lines visible
- ✅ Console shows `mandatoryOrthogonal: true`
- ✅ Console shows `bendMinimization: true`

## Test 2: Symbol Movement Consistency

### Steps:
1. **Select one of the connected symbols**
2. **Drag it to a new position** (keep non-aligned)
3. **Check console output** for:
   ```
   Recalculating unified smart path for connection [ID] due to position change
   ```
4. **Verify the connection** updates to new L-shaped path

### Expected Results:
- ✅ Console shows recalculation message
- ✅ Connection immediately updates to new orthogonal path
- ✅ New path has minimal bends (0 or 1 bend)
- ✅ No diagonal segments appear during or after movement

## Test 3: Aligned Symbols (Direct Connection)

### Steps:
1. **Move symbols** so they are vertically or horizontally aligned
2. **Observe the connection** update
3. **Check console** for bend count

### Expected Results:
- ✅ Connection shows direct line (0 bends)
- ✅ Console shows `bends: 0`
- ✅ Line is perfectly horizontal or vertical

## Test 4: Close Proximity (Z-shaped)

### Steps:
1. **Move symbols very close** together (< 100px apart)
2. **Observe the connection** routing
3. **Check console** for `isCloseProximity: true`

### Expected Results:
- ✅ Connection shows Z-shaped path (2 bends)
- ✅ Console shows `isCloseProximity: true`
- ✅ Console shows `bends: 2`
- ✅ Path avoids symbol overlap

## Test 5: Connection Preview Consistency

### Steps:
1. **Start creating a new connection** (click on connection point)
2. **Move mouse around** to see preview line
3. **Observe preview line shape**
4. **Complete connection** and compare final result

### Expected Results:
- ✅ Preview line shows orthogonal L-shape
- ✅ Final connection matches preview shape
- ✅ No diagonal preview lines

## Test 6: Multiple Connections

### Steps:
1. **Add more symbols** to the diagram
2. **Create multiple connections** between different symbols
3. **Move symbols around** and observe all connections
4. **Check console** for consistent logging

### Expected Results:
- ✅ All connections use orthogonal routing
- ✅ All connections update consistently when symbols move
- ✅ Console shows unified routing for all connections
- ✅ No diagonal lines in any connection

## Verification Checklist

### ✅ **Mandatory Orthogonal Routing**
- [ ] No diagonal lines in any connection
- [ ] All connections use only horizontal and vertical segments
- [ ] Console shows `mandatoryOrthogonal: true` for all connections

### ✅ **Bend Minimization**
- [ ] Aligned symbols: 0 bends (direct connection)
- [ ] Non-aligned symbols: 1 bend (L-shaped)
- [ ] Close proximity: 2 bends (Z-shaped)
- [ ] Console shows correct bend count

### ✅ **Consistency**
- [ ] Initial creation and symbol movement produce identical results
- [ ] Same positions always produce same routing
- [ ] Console shows recalculation messages during movement

### ✅ **Visual Quality**
- [ ] Clean connection point termination
- [ ] No visual gaps or overlaps
- [ ] Professional electrical schematic appearance

## Troubleshooting

### If diagonal lines appear:
1. Check console for error messages
2. Verify `mandatoryOrthogonal: true` in logs
3. Check if legacy routing code is still active

### If inconsistent routing:
1. Verify console shows recalculation messages
2. Check if multiple routing mechanisms are conflicting
3. Ensure unified routing function is being called

### If preview doesn't match final:
1. Check `getOrthogonalPreviewPoints()` function
2. Verify connection creator uses same routing options
3. Ensure preview and final use same bend logic

## Success Criteria

The unified routing system is working correctly if:

1. **All connections are orthogonal** (no diagonal lines)
2. **Bend minimization is applied** (optimal bend count)
3. **Routing is consistent** (same positions = same routing)
4. **Console logging confirms** unified system usage
5. **Preview matches final** connection routing

## Performance Notes

- Routing calculations should be fast (< 50ms)
- No flickering during symbol movement
- Smooth connection updates
- Minimal console error messages

This test suite verifies that the unified routing system successfully consolidates all connection routing mechanisms into a single, consistent system that applies mandatory orthogonal routing with bend minimization across all scenarios.
