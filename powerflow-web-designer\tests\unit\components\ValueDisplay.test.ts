import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import ValueDisplay from '../../../powerflow-web-designer/src/components/canvas/ValueDisplay.vue';

// Mock Konva components
vi.mock('vue-konva', () => ({
  VGroup: {
    name: 'VGroup',
    template: '<div class="v-group"><slot /></div>',
    props: ['config']
  },
  VRect: {
    name: 'VRect',
    template: '<div class="v-rect"></div>',
    props: ['config']
  },
  VText: {
    name: 'VText',
    template: '<div class="v-text"></div>',
    props: ['config']
  }
}));

describe('ValueDisplay', () => {
  it('renders correctly with default props', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 42
      }
    });

    // Check that the component renders
    expect(wrapper.exists()).toBe(true);

    // Check that it renders a VGroup
    expect(wrapper.findComponent({ name: 'VGroup' }).exists()).toBe(true);

    // Check that it renders a VRect for the background
    expect(wrapper.findComponent({ name: 'VRect' }).exists()).toBe(true);

    // Check that it renders a VText for the value
    expect(wrapper.findAllComponents({ name: 'VText' }).length).toBe(1);
  });

  it('renders with unit when provided', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 42,
        unit: 'kW'
      }
    });

    // Check that it renders two VText components (value and unit)
    expect(wrapper.findAllComponents({ name: 'VText' }).length).toBe(2);
  });

  it('formats integer values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 42,
        format: 'integer'
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('42');
  });

  it('formats decimal values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 42.123,
        format: 'decimal',
        precision: 2
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('42.12');
  });

  it('formats percentage values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 0.42,
        format: 'percentage',
        precision: 0
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('42%');
  });

  it('formats scientific values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 42000,
        format: 'scientific',
        precision: 2
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('4.20e+4');
  });

  it('formats binary values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 1,
        format: 'binary'
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('1');
  });

  it('formats hex values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 255,
        format: 'hex'
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('0xFF');
  });

  it('formats currency values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 42.5,
        format: 'currency',
        precision: 2
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('$42.50');
  });

  it('handles null values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: null
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('N/A');
  });

  it('handles string values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: 'Test'
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('Test');
  });

  it('handles NaN values correctly', () => {
    const wrapper = mount(ValueDisplay, {
      props: {
        value: NaN
      }
    });

    // Check the formatted value
    const formattedValue = wrapper.vm.formattedValue;
    expect(formattedValue).toBe('N/A');
  });
});
