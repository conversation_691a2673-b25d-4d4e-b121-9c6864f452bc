/**
 * Path finding utilities for PowerFlow Web Designer
 * Implements algorithms for finding optimal paths between connection points
 */

import type { Position } from '@/types/symbol';
import type { SymbolInstance } from '@/types/symbolInstance';

// Grid cell for path finding
interface GridCell {
  x: number;
  y: number;
  f: number; // Total cost (g + h)
  g: number; // Cost from start
  h: number; // Heuristic (estimated cost to goal)
  walkable: boolean;
  parent?: GridCell;
}

// Routing strategy for path finding
export enum RoutingStrategy {
  DIRECT = 'direct',         // Direct path with minimal waypoints
  ORTHOGONAL = 'orthogonal', // Orthogonal path with horizontal and vertical segments
  MANHATTAN = 'manhattan',   // Manhattan path (horizontal then vertical)
  METRO = 'metro'            // Metro-style path with 45-degree angles
}

// Path finding options
export interface PathFindingOptions {
  gridSize: number;           // Size of each grid cell
  padding: number;            // Padding around obstacles
  preferStraightLines: boolean; // Whether to prefer straight lines
  maxIterations: number;      // Maximum number of iterations to prevent infinite loops
  avoidObstacles: boolean;    // Whether to avoid obstacles
  smoothingFactor: number;    // Factor for path smoothing (0-1)
  routingStrategy: RoutingStrategy; // Routing strategy
  cornerRadius: number;       // Radius for rounded corners (0 for sharp corners)
  jumpOverCrossings: boolean; // Whether to create jumps at crossings
  snapToGrid: boolean;        // Whether to snap waypoints to grid
  optimizePath: boolean;      // Whether to optimize the path by removing unnecessary waypoints
  heuristicWeight: number;    // Weight factor for heuristic (0-1) where 0 is pure Manhattan and 1 is pure Euclidean
  pathTension: number;        // Tension factor for path smoothing (0-1)
  useHierarchicalPathfinding: boolean; // Whether to use hierarchical pathfinding for better performance
  bundleConnections: boolean; // Whether to bundle parallel connections
  bundleThreshold: number;    // Distance threshold for bundling connections
  proximityThreshold?: number; // Distance threshold for switching to orthogonal routing (default: 150)
  minOrthogonalDistance?: number; // Minimum distance for direct connection (default: 30)
  interferenceThreshold?: number; // Distance threshold for detecting line interference with obstacles (default: 50)
}

// Default path finding options
export const defaultPathFindingOptions: PathFindingOptions = {
  gridSize: 10,
  padding: 10,
  preferStraightLines: true,
  maxIterations: 1000,
  avoidObstacles: true,
  smoothingFactor: 0.5,
  routingStrategy: RoutingStrategy.DIRECT,
  cornerRadius: 0,
  jumpOverCrossings: false,
  snapToGrid: true,
  optimizePath: true,
  heuristicWeight: 0.5,
  pathTension: 0.5,
  useHierarchicalPathfinding: true,
  bundleConnections: false,
  bundleThreshold: 20,
  proximityThreshold: 150,
  minOrthogonalDistance: 30,
  interferenceThreshold: 50,
};

/**
 * Find an optimal path between two points, avoiding obstacles
 * @param start Starting position
 * @param end Ending position
 * @param obstacles Array of symbol instances to avoid
 * @param options Path finding options
 * @returns Array of positions representing the path
 */
export function findPath(
  start: Position,
  end: Position,
  obstacles: SymbolInstance[],
  options: Partial<PathFindingOptions> = {}
): Position[] {
  // Merge options with defaults
  const mergedOptions: PathFindingOptions = {
    ...defaultPathFindingOptions,
    ...options,
  };

  // MANDATORY ORTHOGONAL ROUTING: All strategies now return orthogonal paths
  // This ensures consistency with the unified routing system
  let path: Position[];

  switch (mergedOptions.routingStrategy) {
    case RoutingStrategy.DIRECT:
    case RoutingStrategy.ORTHOGONAL:
    case RoutingStrategy.MANHATTAN:
    case RoutingStrategy.METRO:
    default:
      // All routing strategies now use orthogonal pathfinding
      path = findOrthogonalPath(start, end, obstacles, mergedOptions);
      break;
  }

  // Ensure the returned path is always orthogonal
  return ensureOrthogonalPath(path);
}

/**
 * Find a direct path between two points, avoiding obstacles
 * @param start Starting position
 * @param end Ending position
 * @param obstacles Array of symbol instances to avoid
 * @param options Path finding options
 * @returns Array of positions representing the path
 */
function findDirectPath(
  start: Position,
  end: Position,
  obstacles: SymbolInstance[],
  options: PathFindingOptions
): Position[] {
  // If not avoiding obstacles or no obstacles, return direct path
  if (!options.avoidObstacles || obstacles.length === 0) {
    return createDirectPath(start, end, options);
  }

  // Create grid for path finding
  const grid = createGrid(start, end, obstacles, options);

  // Find path using A* algorithm
  const path = findPathAStar(
    gridPositionFromPosition(start, options.gridSize),
    gridPositionFromPosition(end, options.gridSize),
    grid,
    options
  );

  // Convert grid positions back to actual positions
  const positionPath = path.map(cell => ({
    x: cell.x * options.gridSize,
    y: cell.y * options.gridSize,
  }));

  // Smooth the path
  const smoothedPath = smoothPath(positionPath, options);

  // Optimize the path if requested
  return options.optimizePath ? optimizePath(smoothedPath) : smoothedPath;
}

/**
 * Find an orthogonal path between two points, avoiding obstacles
 * @param start Starting position
 * @param end Ending position
 * @param obstacles Array of symbol instances to avoid
 * @param options Path finding options
 * @returns Array of positions representing the path
 */
function findOrthogonalPath(
  start: Position,
  end: Position,
  obstacles: SymbolInstance[],
  options: PathFindingOptions
): Position[] {
  // If not avoiding obstacles or no obstacles, create a simple orthogonal path
  if (!options.avoidObstacles || obstacles.length === 0) {
    return createOrthogonalPath(start, end, options);
  }

  // Create grid for path finding
  const grid = createGrid(start, end, obstacles, options);

  // Modify the heuristic to prefer orthogonal paths
  const orthogonalOptions = {
    ...options,
    preferStraightLines: true
  };

  // Find path using A* algorithm
  const path = findPathAStar(
    gridPositionFromPosition(start, options.gridSize),
    gridPositionFromPosition(end, options.gridSize),
    grid,
    orthogonalOptions
  );

  // Convert grid positions back to actual positions
  const positionPath = path.map(cell => ({
    x: cell.x * options.gridSize,
    y: cell.y * options.gridSize,
  }));

  // Ensure the path is orthogonal
  const orthogonalPath = ensureOrthogonalPath(positionPath);

  // Optimize the path if requested
  return options.optimizePath ? optimizeOrthogonalPath(orthogonalPath) : orthogonalPath;
}

/**
 * Find a Manhattan path between two points, avoiding obstacles
 * @param start Starting position
 * @param end Ending position
 * @param obstacles Array of symbol instances to avoid
 * @param options Path finding options
 * @returns Array of positions representing the path
 */
function findManhattanPath(
  start: Position,
  end: Position,
  obstacles: SymbolInstance[],
  options: PathFindingOptions
): Position[] {
  // If not avoiding obstacles or no obstacles, create a simple Manhattan path
  if (!options.avoidObstacles || obstacles.length === 0) {
    return createManhattanPath(start, end, options);
  }

  // Create a Manhattan path with two segments (horizontal then vertical)
  const midPoint = { x: end.x, y: start.y };

  // Check if the horizontal segment intersects with any obstacles
  const horizontalBlocked = isPathBlocked(start, midPoint, obstacles, options.padding);

  // If horizontal segment is blocked, try vertical first
  if (horizontalBlocked) {
    const altMidPoint = { x: start.x, y: end.y };

    // Check if the vertical segment is also blocked
    const verticalBlocked = isPathBlocked(start, altMidPoint, obstacles, options.padding);

    if (verticalBlocked) {
      // Both direct paths are blocked, use A* to find a path
      return findOrthogonalPath(start, end, obstacles, options);
    } else {
      // Use vertical-first Manhattan path
      return [start, altMidPoint, end];
    }
  } else {
    // Use horizontal-first Manhattan path
    return [start, midPoint, end];
  }
}

/**
 * Find a Metro-style path between two points, avoiding obstacles
 * @param start Starting position
 * @param end Ending position
 * @param obstacles Array of symbol instances to avoid
 * @param options Path finding options
 * @returns Array of positions representing the path
 */
function findMetroPath(
  start: Position,
  end: Position,
  obstacles: SymbolInstance[],
  options: PathFindingOptions
): Position[] {
  // If not avoiding obstacles or no obstacles, create a simple Metro path
  if (!options.avoidObstacles || obstacles.length === 0) {
    return createMetroPath(start, end, options);
  }

  // First try to find an orthogonal path
  const orthogonalPath = findOrthogonalPath(start, end, obstacles, options);

  // Convert the orthogonal path to a metro path with 45-degree angles
  return convertToMetroPath(orthogonalPath, options);
}

/**
 * Create a direct path between two points
 * @param start Starting position
 * @param end Ending position
 * @param options Path finding options
 * @returns Array of positions representing the path
 */
function createDirectPath(
  start: Position,
  end: Position,
  options: PathFindingOptions
): Position[] {
  // If prefer straight lines, create orthogonal path
  if (options.preferStraightLines) {
    return createOrthogonalPath(start, end);
  }

  // Otherwise, return direct line
  return [start, end];
}

/**
 * Create an orthogonal path between two points with right angles
 * @param start Starting position
 * @param end Ending position
 * @param options Path finding options
 * @returns Array of positions representing the path
 */
function createOrthogonalPath(
  start: Position,
  end: Position,
  options?: PathFindingOptions
): Position[] {
  // Create a path with a single corner
  const corner: Position = { x: end.x, y: start.y };

  // Check if the corner is the same as start or end
  if ((corner.x === start.x && corner.y === start.y) ||
      (corner.x === end.x && corner.y === end.y)) {
    return [start, end];
  }

  // Return the path
  return [start, corner, end];
}

/**
 * Create a Manhattan path between two points
 * @param start Starting position
 * @param end Ending position
 * @param options Path finding options
 * @returns Array of positions representing the path
 */
function createManhattanPath(
  start: Position,
  end: Position,
  options: PathFindingOptions
): Position[] {
  // Create a path with a single corner (horizontal first)
  const corner: Position = { x: end.x, y: start.y };

  // Check if the corner is the same as start or end
  if ((corner.x === start.x && corner.y === start.y) ||
      (corner.x === end.x && corner.y === end.y)) {
    return [start, end];
  }

  // Return the path
  return [start, corner, end];
}

/**
 * Create a Metro-style path between two points
 * @param start Starting position
 * @param end Ending position
 * @param options Path finding options
 * @returns Array of positions representing the path
 */
function createMetroPath(
  start: Position,
  end: Position,
  options: PathFindingOptions
): Position[] {
  // Calculate the midpoint
  const midX = (start.x + end.x) / 2;
  const midY = (start.y + end.y) / 2;

  // Create a path with a midpoint
  return [start, { x: midX, y: midY }, end];
}

/**
 * Find a path using the A* algorithm with orthogonal movement
 * @param start Starting grid position
 * @param end Ending grid position
 * @param grid Grid for path finding
 * @param options Path finding options
 * @returns Array of grid cells representing the path
 */
function findPathAStarOrthogonal(
  start: { x: number, y: number },
  end: { x: number, y: number },
  grid: GridCell[][],
  options: PathFindingOptions
): GridCell[] {
  // Similar to findPathAStar but with orthogonal movement only
  const openList: GridCell[] = [];
  const closedList: Set<string> = new Set();

  // Add start cell to open list
  const startCell = grid[start.y][start.x];
  startCell.g = 0;
  startCell.h = manhattanDistance(start, end);
  startCell.f = startCell.g + startCell.h;
  openList.push(startCell);

  // Iteration counter to prevent infinite loops
  let iterations = 0;

  while (openList.length > 0 && iterations < options.maxIterations) {
    iterations++;

    // Find the cell with the lowest f score
    let lowestIndex = 0;
    for (let i = 1; i < openList.length; i++) {
      if (openList[i].f < openList[lowestIndex].f) {
        lowestIndex = i;
      }
    }

    const currentCell = openList[lowestIndex];

    // Check if we've reached the end
    if (currentCell.x === end.x && currentCell.y === end.y) {
      // Reconstruct path
      const path: GridCell[] = [];
      let temp = currentCell;
      path.push(temp);

      while (temp.parent) {
        path.push(temp.parent);
        temp = temp.parent;
      }

      return path.reverse();
    }

    // Move current cell from open to closed list
    openList.splice(lowestIndex, 1);
    closedList.add(`${currentCell.x},${currentCell.y}`);

    // Get orthogonal neighbors only
    const neighbors = getOrthogonalNeighbors(currentCell, grid);

    for (const neighbor of neighbors) {
      // Skip if in closed list or not walkable
      if (closedList.has(`${neighbor.x},${neighbor.y}`) || !neighbor.walkable) {
        continue;
      }

      // Calculate g score (always 10 for orthogonal movement)
      const gScore = currentCell.g + 10;

      // Check if neighbor is in open list
      const inOpenList = openList.some(cell => cell.x === neighbor.x && cell.y === neighbor.y);

      if (!inOpenList || gScore < neighbor.g) {
        // Update neighbor values
        neighbor.g = gScore;
        neighbor.h = manhattanDistance({ x: neighbor.x, y: neighbor.y }, end);
        neighbor.f = neighbor.g + neighbor.h;
        neighbor.parent = currentCell;

        // Add to open list if not already there
        if (!inOpenList) {
          openList.push(neighbor);
        }
      }
    }
  }

  // No path found
  return [grid[start.y][start.x]];
}

/**
 * Convert a position to a grid position
 * @param position Position to convert
 * @param gridSize Size of each grid cell
 * @returns Grid position
 */
function gridPositionFromPosition(position: Position, gridSize: number): { x: number, y: number } {
  return {
    x: Math.floor(position.x / gridSize),
    y: Math.floor(position.y / gridSize),
  };
}

/**
 * Create a grid for path finding
 * @param start Starting position
 * @param end Ending position
 * @param obstacles Array of symbol instances to avoid
 * @param options Path finding options
 * @returns Grid for path finding
 */
function createGrid(
  start: Position,
  end: Position,
  obstacles: SymbolInstance[],
  options: PathFindingOptions
): GridCell[][] {
  // Determine grid boundaries
  const minX = Math.min(start.x, end.x) - 200;
  const minY = Math.min(start.y, end.y) - 200;
  const maxX = Math.max(start.x, end.x) + 200;
  const maxY = Math.max(start.y, end.y) + 200;

  // Calculate grid dimensions
  const gridWidth = Math.ceil((maxX - minX) / options.gridSize);
  const gridHeight = Math.ceil((maxY - minY) / options.gridSize);

  // Create empty grid
  const grid: GridCell[][] = [];
  for (let y = 0; y < gridHeight; y++) {
    grid[y] = [];
    for (let x = 0; x < gridWidth; x++) {
      grid[y][x] = {
        x,
        y,
        f: 0,
        g: 0,
        h: 0,
        walkable: true,
      };
    }
  }

  // Mark obstacle cells as unwalkable
  for (const obstacle of obstacles) {
    markObstacle(grid, obstacle, minX, minY, options);
  }

  return grid;
}

/**
 * Mark cells occupied by an obstacle as unwalkable
 * @param grid Grid to mark
 * @param obstacle Obstacle to mark
 * @param minX Minimum X coordinate of the grid
 * @param minY Minimum Y coordinate of the grid
 * @param options Path finding options
 */
function markObstacle(
  grid: GridCell[][],
  obstacle: SymbolInstance,
  minX: number,
  minY: number,
  options: PathFindingOptions
): void {
  // Get obstacle bounds
  const x = obstacle.position.x;
  const y = obstacle.position.y;
  const width = obstacle.dimensions?.width || 100;
  const height = obstacle.dimensions?.height || 100;

  // Add padding
  const padding = options.padding;
  const left = x - padding;
  const top = y - padding;
  const right = x + width + padding;
  const bottom = y + height + padding;

  // For simple rectangular obstacles
  if (!obstacle.rotation || obstacle.rotation === 0) {
    // Convert to grid coordinates
    const gridLeft = Math.floor((left - minX) / options.gridSize);
    const gridTop = Math.floor((top - minY) / options.gridSize);
    const gridRight = Math.ceil((right - minX) / options.gridSize);
    const gridBottom = Math.ceil((bottom - minY) / options.gridSize);

    // Mark cells as unwalkable
    for (let y = gridTop; y < gridBottom; y++) {
      for (let x = gridLeft; x < gridRight; x++) {
        if (grid[y] && grid[y][x]) {
          grid[y][x].walkable = false;
        }
      }
    }
  } else {
    // For rotated obstacles, use a more precise approach
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const rotation = obstacle.rotation * Math.PI / 180; // Convert to radians

    // Calculate the corners of the rotated rectangle
    const corners = [
      rotatePoint({ x: left, y: top }, { x: centerX, y: centerY }, rotation),
      rotatePoint({ x: right, y: top }, { x: centerX, y: centerY }, rotation),
      rotatePoint({ x: right, y: bottom }, { x: centerX, y: centerY }, rotation),
      rotatePoint({ x: left, y: bottom }, { x: centerX, y: centerY }, rotation),
    ];

    // Find the bounding box of the rotated rectangle
    let minRotX = Math.min(...corners.map(p => p.x));
    let minRotY = Math.min(...corners.map(p => p.y));
    let maxRotX = Math.max(...corners.map(p => p.x));
    let maxRotY = Math.max(...corners.map(p => p.y));

    // Convert to grid coordinates
    const gridRotX1 = Math.floor((minRotX - minX) / options.gridSize);
    const gridRotY1 = Math.floor((minRotY - minY) / options.gridSize);
    const gridRotX2 = Math.ceil((maxRotX - minX) / options.gridSize);
    const gridRotY2 = Math.ceil((maxRotY - minY) / options.gridSize);

    // Check each cell if it's inside the rotated rectangle
    for (let y = gridRotY1; y < gridRotY2; y++) {
      for (let x = gridRotX1; x < gridRotX2; x++) {
        if (grid[y] && grid[y][x]) {
          // Convert grid cell to world coordinates (center of the cell)
          const cellX = minX + (x + 0.5) * options.gridSize;
          const cellY = minY + (y + 0.5) * options.gridSize;

          // Check if the point is inside the rotated rectangle
          if (isPointInRotatedRectangle(
            { x: cellX, y: cellY },
            { x: centerX, y: centerY },
            width + padding * 2,
            height + padding * 2,
            rotation
          )) {
            grid[y][x].walkable = false;
          }
        }
      }
    }
  }
}

/**
 * Find a path using the A* algorithm
 * @param start Starting grid position
 * @param end Ending grid position
 * @param grid Grid for path finding
 * @param options Path finding options
 * @returns Array of grid cells representing the path
 */
function findPathAStar(
  start: { x: number, y: number },
  end: { x: number, y: number },
  grid: GridCell[][],
  options: PathFindingOptions
): GridCell[] {
  // Initialize open and closed lists
  const openList: GridCell[] = [];
  const closedList: Set<string> = new Set();

  // Add start cell to open list
  const startCell = grid[start.y][start.x];
  startCell.g = 0;

  // Use weighted heuristic based on heuristicWeight
  startCell.h = weightedDistance(start, end, options.heuristicWeight);
  startCell.f = startCell.g + startCell.h;
  openList.push(startCell);

  // Iteration counter to prevent infinite loops
  let iterations = 0;

  // While there are cells in the open list
  while (openList.length > 0 && iterations < options.maxIterations) {
    iterations++;

    // Find cell with lowest f value
    let currentIndex = 0;
    for (let i = 1; i < openList.length; i++) {
      if (openList[i].f < openList[currentIndex].f) {
        currentIndex = i;
      }
    }

    // Get current cell
    const currentCell = openList[currentIndex];

    // If reached the end, reconstruct and return the path
    if (currentCell.x === end.x && currentCell.y === end.y) {
      return reconstructPath(currentCell);
    }

    // Remove current cell from open list and add to closed list
    openList.splice(currentIndex, 1);
    closedList.add(`${currentCell.x},${currentCell.y}`);

    // Check neighbors
    const neighbors = getNeighbors(currentCell, grid, options.preferStraightLines);

    for (const neighbor of neighbors) {
      // Skip if in closed list or not walkable
      if (closedList.has(`${neighbor.x},${neighbor.y}`) || !neighbor.walkable) {
        continue;
      }

      // Calculate g score
      const gScore = currentCell.g + (
        (neighbor.x === currentCell.x || neighbor.y === currentCell.y) ? 10 : 14
      );

      // Check if neighbor is in open list
      const inOpenList = openList.some(cell => cell.x === neighbor.x && cell.y === neighbor.y);

      if (!inOpenList || gScore < neighbor.g) {
        // Update neighbor values
        neighbor.g = gScore;

        // Use weighted heuristic based on heuristicWeight
        neighbor.h = weightedDistance({ x: neighbor.x, y: neighbor.y }, end, options.heuristicWeight);

        // Apply path tension to influence path straightness
        if (currentCell.parent) {
          // Calculate change in direction
          const dx1 = currentCell.x - currentCell.parent.x;
          const dy1 = currentCell.y - currentCell.parent.y;
          const dx2 = neighbor.x - currentCell.x;
          const dy2 = neighbor.y - currentCell.y;

          // Add penalty for direction changes
          if (dx1 !== dx2 || dy1 !== dy2) {
            // Higher tension means higher penalty for turns
            neighbor.h += 10 * options.pathTension;
          }
        }

        neighbor.f = neighbor.g + neighbor.h;
        neighbor.parent = currentCell;

        // Add to open list if not already there
        if (!inOpenList) {
          openList.push(neighbor);
        }
      }
    }
  }

  // If no path found, return empty array
  return [];
}

/**
 * Get neighboring cells
 * @param cell Current cell
 * @param grid Grid for path finding
 * @param preferStraightLines Whether to prefer straight lines
 * @returns Array of neighboring cells
 */
function getNeighbors(cell: GridCell, grid: GridCell[][], preferStraightLines: boolean): GridCell[] {
  const neighbors: GridCell[] = [];
  const { x, y } = cell;

  // Define directions (4 or 8 directions)
  const directions = preferStraightLines
    ? [
        { x: 0, y: -1 }, // Up
        { x: 1, y: 0 },  // Right
        { x: 0, y: 1 },  // Down
        { x: -1, y: 0 }, // Left
      ]
    : [
        { x: 0, y: -1 }, // Up
        { x: 1, y: -1 }, // Up-Right
        { x: 1, y: 0 },  // Right
        { x: 1, y: 1 },  // Down-Right
        { x: 0, y: 1 },  // Down
        { x: -1, y: 1 }, // Down-Left
        { x: -1, y: 0 }, // Left
        { x: -1, y: -1 },// Up-Left
      ];

  // Check each direction
  for (const dir of directions) {
    const newY = y + dir.y;
    const newX = x + dir.x;

    // Check if within grid bounds
    if (newY >= 0 && newY < grid.length && newX >= 0 && newX < grid[0].length) {
      neighbors.push(grid[newY][newX]);
    }
  }

  return neighbors;
}

/**
 * Get orthogonal neighboring cells (only up, right, down, left)
 * @param cell Current cell
 * @param grid Grid for path finding
 * @returns Array of neighboring cells
 */
function getOrthogonalNeighbors(cell: GridCell, grid: GridCell[][]): GridCell[] {
  const neighbors: GridCell[] = [];
  const { x, y } = cell;

  // Define directions (4 directions only)
  const directions = [
    { x: 0, y: -1 }, // Up
    { x: 1, y: 0 },  // Right
    { x: 0, y: 1 },  // Down
    { x: -1, y: 0 }, // Left
  ];

  // Check each direction
  for (const dir of directions) {
    const newY = y + dir.y;
    const newX = x + dir.x;

    // Check if within grid bounds
    if (newY >= 0 && newY < grid.length && newX >= 0 && newX < grid[0].length) {
      neighbors.push(grid[newY][newX]);
    }
  }

  return neighbors;
}

/**
 * Check if a path between two points is blocked by any obstacles
 * @param start Starting position
 * @param end Ending position
 * @param obstacles Array of symbol instances to avoid
 * @param padding Padding around obstacles
 * @returns Whether the path is blocked
 */
function isPathBlocked(
  start: Position,
  end: Position,
  obstacles: SymbolInstance[],
  padding: number
): boolean {
  // For each obstacle, check if the line intersects with it
  for (const obstacle of obstacles) {
    // Get the obstacle bounds with padding
    const bounds = {
      x: obstacle.position.x - padding,
      y: obstacle.position.y - padding,
      width: obstacle.dimensions.width + padding * 2,
      height: obstacle.dimensions.height + padding * 2,
    };

    // Check if the line intersects with the obstacle bounds
    if (lineIntersectsRectangle(start, end, bounds)) {
      return true;
    }
  }

  return false;
}

/**
 * Calculate Manhattan distance between two points
 * @param a First point
 * @param b Second point
 * @returns Manhattan distance
 */
function manhattanDistance(a: { x: number, y: number }, b: { x: number, y: number }): number {
  return Math.abs(a.x - b.x) + Math.abs(a.y - b.y);
}

/**
 * Calculate Euclidean distance between two points
 * @param a First point
 * @param b Second point
 * @returns Euclidean distance
 */
function euclideanDistance(a: { x: number, y: number }, b: { x: number, y: number }): number {
  return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));
}

/**
 * Calculate Chebyshev distance between two points (allows diagonal movement at same cost as orthogonal)
 * @param a First point
 * @param b Second point
 * @returns Chebyshev distance
 */
function chebyshevDistance(a: { x: number, y: number }, b: { x: number, y: number }): number {
  return Math.max(Math.abs(a.x - b.x), Math.abs(a.y - b.y));
}

/**
 * Calculate a weighted combination of Manhattan and Euclidean distances
 * This creates more natural paths by balancing between grid-aligned and direct paths
 * @param a First point
 * @param b Second point
 * @param weight Weight factor (0-1) where 0 is pure Manhattan and 1 is pure Euclidean
 * @returns Weighted distance
 */
function weightedDistance(a: { x: number, y: number }, b: { x: number, y: number }, weight: number): number {
  const manhattan = manhattanDistance(a, b);
  const euclidean = euclideanDistance(a, b);
  return manhattan * (1 - weight) + euclidean * weight;
}

/**
 * Reconstruct path from end to start
 * @param endCell End cell
 * @returns Array of cells representing the path
 */
function reconstructPath(endCell: GridCell): GridCell[] {
  const path: GridCell[] = [];
  let currentCell: GridCell | undefined = endCell;

  while (currentCell) {
    path.unshift(currentCell);
    currentCell = currentCell.parent;
  }

  return path;
}

/**
 * Rotate a point around a center point
 * @param point Point to rotate
 * @param center Center point to rotate around
 * @param angle Angle in radians
 * @returns Rotated point
 */
function rotatePoint(point: Position, center: Position, angle: number): Position {
  const cos = Math.cos(angle);
  const sin = Math.sin(angle);

  // Translate point to origin
  const x = point.x - center.x;
  const y = point.y - center.y;

  // Rotate point
  const xNew = x * cos - y * sin;
  const yNew = x * sin + y * cos;

  // Translate point back
  return {
    x: xNew + center.x,
    y: yNew + center.y,
  };
}

/**
 * Check if a point is inside a rotated rectangle
 * @param point Point to check
 * @param center Center of the rectangle
 * @param width Width of the rectangle
 * @param height Height of the rectangle
 * @param angle Angle of rotation in radians
 * @returns True if the point is inside the rotated rectangle
 */
function isPointInRotatedRectangle(
  point: Position,
  center: Position,
  width: number,
  height: number,
  angle: number
): boolean {
  // Rotate the point in the opposite direction
  const rotatedPoint = rotatePoint(point, center, -angle);

  // Check if the rotated point is inside the non-rotated rectangle
  return (
    rotatedPoint.x >= center.x - width / 2 &&
    rotatedPoint.x <= center.x + width / 2 &&
    rotatedPoint.y >= center.y - height / 2 &&
    rotatedPoint.y <= center.y + height / 2
  );
}

/**
 * Check if a line intersects with a rectangle
 * @param start Starting position of the line
 * @param end Ending position of the line
 * @param rect Rectangle to check
 * @returns Whether the line intersects with the rectangle
 */
function lineIntersectsRectangle(
  start: Position,
  end: Position,
  rect: { x: number, y: number, width: number, height: number }
): boolean {
  // Check if either endpoint is inside the rectangle
  if (
    (start.x >= rect.x && start.x <= rect.x + rect.width &&
     start.y >= rect.y && start.y <= rect.y + rect.height) ||
    (end.x >= rect.x && end.x <= rect.x + rect.width &&
     end.y >= rect.y && end.y <= rect.y + rect.height)
  ) {
    return true;
  }

  // Check if the line intersects with any of the rectangle's edges
  const rectEdges = [
    { start: { x: rect.x, y: rect.y }, end: { x: rect.x + rect.width, y: rect.y } }, // Top
    { start: { x: rect.x + rect.width, y: rect.y }, end: { x: rect.x + rect.width, y: rect.y + rect.height } }, // Right
    { start: { x: rect.x + rect.width, y: rect.y + rect.height }, end: { x: rect.x, y: rect.y + rect.height } }, // Bottom
    { start: { x: rect.x, y: rect.y + rect.height }, end: { x: rect.x, y: rect.y } }, // Left
  ];

  for (const edge of rectEdges) {
    if (linesIntersect(start, end, edge.start, edge.end)) {
      return true;
    }
  }

  return false;
}

/**
 * Check if two line segments intersect
 * @param a1 First point of first line
 * @param a2 Second point of first line
 * @param b1 First point of second line
 * @param b2 Second point of second line
 * @returns Whether the lines intersect
 */
function linesIntersect(a1: Position, a2: Position, b1: Position, b2: Position): boolean {
  // Calculate the direction of the lines
  const uA = ((b2.x - b1.x) * (a1.y - b1.y) - (b2.y - b1.y) * (a1.x - b1.x)) /
             ((b2.y - b1.y) * (a2.x - a1.x) - (b2.x - b1.x) * (a2.y - a1.y));

  const uB = ((a2.x - a1.x) * (a1.y - b1.y) - (a2.y - a1.y) * (a1.x - b1.x)) /
             ((b2.y - b1.y) * (a2.x - a1.x) - (b2.x - b1.x) * (a2.y - a1.y));

  // If uA and uB are between 0-1, lines are colliding
  return (uA >= 0 && uA <= 1 && uB >= 0 && uB <= 1);
}

/**
 * Ensure a path is orthogonal (only horizontal and vertical segments)
 * Enhanced version with bend minimization for unified routing system
 * @param path Path to ensure is orthogonal
 * @returns Orthogonal path with minimal bends
 */
function ensureOrthogonalPath(path: Position[]): Position[] {
  if (!Array.isArray(path) || path.length < 2) {
    return path;
  }

  const orthogonalPath: Position[] = [path[0]]; // Start with the first point

  for (let i = 1; i < path.length; i++) {
    const current = path[i];
    const previous = orthogonalPath[orthogonalPath.length - 1];

    const dx = current.x - previous.x;
    const dy = current.y - previous.y;

    // Check if the segment is already orthogonal
    if (Math.abs(dx) < 5) {
      // Vertically aligned - keep as is
      orthogonalPath.push(current);
    } else if (Math.abs(dy) < 5) {
      // Horizontally aligned - keep as is
      orthogonalPath.push(current);
    } else {
      // Diagonal segment - convert to orthogonal L-shape
      // Choose direction based on larger distance for better visual flow
      if (Math.abs(dx) > Math.abs(dy)) {
        // Horizontal first, then vertical
        orthogonalPath.push({ x: current.x, y: previous.y });
        orthogonalPath.push(current);
      } else {
        // Vertical first, then horizontal
        orthogonalPath.push({ x: previous.x, y: current.y });
        orthogonalPath.push(current);
      }
    }
  }

  return orthogonalPath;
}

/**
 * Optimize an orthogonal path by removing unnecessary waypoints
 * @param path Path to optimize
 * @returns Optimized path
 */
function optimizeOrthogonalPath(path: Position[]): Position[] {
  if (path.length <= 2) {
    return path;
  }

  const optimizedPath: Position[] = [path[0]];

  for (let i = 1; i < path.length - 1; i++) {
    const prev = path[i - 1];
    const current = path[i];
    const next = path[i + 1];

    // Skip points that form a straight line
    if (
      (prev.x === current.x && current.x === next.x) ||
      (prev.y === current.y && current.y === next.y)
    ) {
      continue;
    }

    optimizedPath.push(current);
  }

  optimizedPath.push(path[path.length - 1]);

  return optimizedPath;
}

/**
 * Convert an orthogonal path to a metro-style path with 45-degree angles
 * @param path Orthogonal path to convert
 * @param options Path finding options
 * @returns Metro-style path
 */
function convertToMetroPath(path: Position[], options: PathFindingOptions): Position[] {
  if (path.length <= 2) {
    return path;
  }

  const metroPath: Position[] = [path[0]];

  for (let i = 1; i < path.length - 1; i++) {
    const prev = path[i - 1];
    const current = path[i];
    const next = path[i + 1];

    // If this is a corner point, replace it with two 45-degree points
    if (prev.x !== next.x && prev.y !== next.y) {
      // Calculate the corner size (distance for 45-degree segments)
      const cornerSize = Math.min(
        Math.abs(current.x - prev.x),
        Math.abs(current.y - prev.y),
        Math.abs(next.x - current.x),
        Math.abs(next.y - current.y)
      ) / 2;

      // Add the first 45-degree point
      if (prev.x === current.x) {
        // Vertical segment, then horizontal
        metroPath.push({
          x: current.x,
          y: current.y - Math.sign(current.y - next.y) * cornerSize
        });

        metroPath.push({
          x: current.x + Math.sign(next.x - current.x) * cornerSize,
          y: current.y
        });
      } else {
        // Horizontal segment, then vertical
        metroPath.push({
          x: current.x - Math.sign(current.x - next.x) * cornerSize,
          y: current.y
        });

        metroPath.push({
          x: current.x,
          y: current.y + Math.sign(next.y - current.y) * cornerSize
        });
      }
    } else {
      // Not a corner, keep the point
      metroPath.push(current);
    }
  }

  metroPath.push(path[path.length - 1]);

  return metroPath;
}

/**
 * Optimize a path by removing unnecessary waypoints
 * @param path Path to optimize
 * @returns Optimized path
 */
function optimizePath(path: Position[]): Position[] {
  if (path.length <= 2) {
    return path;
  }

  // Simple path optimization by removing redundant points
  const optimizedPath: Position[] = [path[0]];

  for (let i = 1; i < path.length - 1; i++) {
    const prev = path[i - 1];
    const current = path[i];
    const next = path[i + 1];

    // Skip points that are in a straight line
    if (
      (prev.x === current.x && current.x === next.x) ||
      (prev.y === current.y && current.y === next.y) ||
      (Math.abs(prev.x - current.x) === Math.abs(prev.y - current.y) &&
       Math.abs(current.x - next.x) === Math.abs(current.y - next.y) &&
       Math.sign(prev.x - current.x) === Math.sign(current.x - next.x) &&
       Math.sign(prev.y - current.y) === Math.sign(current.y - next.y))
    ) {
      continue;
    }

    optimizedPath.push(current);
  }

  optimizedPath.push(path[path.length - 1]);

  return optimizedPath;
}

/**
 * Smooth a path to make it more natural
 * @param path Path to smooth
 * @param options Path finding options
 * @returns Smoothed path
 */
function smoothPath(path: Position[], options: PathFindingOptions): Position[] {
  if (path.length <= 2) {
    return path;
  }

  // First, remove redundant points
  const simplifiedPath: Position[] = [path[0]];

  for (let i = 1; i < path.length - 1; i++) {
    const prev = path[i - 1];
    const current = path[i];
    const next = path[i + 1];

    // Skip points that are in a straight line
    if (
      (prev.x === current.x && current.x === next.x) ||
      (prev.y === current.y && current.y === next.y)
    ) {
      continue;
    }

    simplifiedPath.push(current);
  }

  simplifiedPath.push(path[path.length - 1]);

  // If corner radius is 0 or path is too short, return simplified path
  if (options.cornerRadius <= 0 || simplifiedPath.length <= 2) {
    return simplifiedPath;
  }

  // Apply corner rounding
  return roundCorners(simplifiedPath, options.cornerRadius, options.pathTension);
}

/**
 * Round the corners of a path
 * @param path Path to round corners
 * @param radius Corner radius
 * @param tension Path tension (0-1)
 * @returns Path with rounded corners
 */
function roundCorners(path: Position[], radius: number, tension: number = 0.5): Position[] {
  if (path.length <= 2) {
    return path;
  }

  const roundedPath: Position[] = [];

  // Add first point
  roundedPath.push(path[0]);

  // Process each corner
  for (let i = 1; i < path.length - 1; i++) {
    const prev = path[i - 1];
    const current = path[i];
    const next = path[i + 1];

    // Calculate segment lengths
    const prevSegmentLength = Math.sqrt(
      Math.pow(current.x - prev.x, 2) + Math.pow(current.y - prev.y, 2)
    );

    const nextSegmentLength = Math.sqrt(
      Math.pow(next.x - current.x, 2) + Math.pow(next.y - current.y, 2)
    );

    // Limit radius to half of the shorter segment
    const maxRadius = Math.min(prevSegmentLength, nextSegmentLength) / 2;
    const effectiveRadius = Math.min(radius, maxRadius);

    if (effectiveRadius <= 1) {
      // If radius is too small, just add the corner point
      roundedPath.push(current);
      continue;
    }

    // Calculate direction vectors
    const prevDir = {
      x: (current.x - prev.x) / prevSegmentLength,
      y: (current.y - prev.y) / prevSegmentLength
    };

    const nextDir = {
      x: (next.x - current.x) / nextSegmentLength,
      y: (next.y - current.y) / nextSegmentLength
    };

    // Calculate corner points
    const cornerStart = {
      x: current.x - prevDir.x * effectiveRadius,
      y: current.y - prevDir.y * effectiveRadius
    };

    const cornerEnd = {
      x: current.x + nextDir.x * effectiveRadius,
      y: current.y + nextDir.y * effectiveRadius
    };

    // Add corner start point
    roundedPath.push(cornerStart);

    // Add control point for bezier curve
    const controlPoint = {
      x: current.x,
      y: current.y
    };

    // Add corner end point with control point
    roundedPath.push({
      x: cornerEnd.x,
      y: cornerEnd.y,
      controlPoint: controlPoint
    } as any);
  }

  // Add last point
  roundedPath.push(path[path.length - 1]);

  return roundedPath;
}


