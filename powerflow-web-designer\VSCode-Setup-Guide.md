# PowerFlow Web Designer - VSCode 配置完整指南

## 📋 概述

本指南为 PowerFlow Web Designer 项目提供了完整的 VSCode 开发环境配置，包括运行调试配置、任务管理、代码质量工具和开发工作流优化。

## 🚀 快速启动

### 一键启动（推荐）
```bash
# Windows 用户 - 双击运行
start-dev.bat

# PowerShell 用户
.\scripts\dev-start.ps1

# 或使用 VSCode
按 F5 → 选择 "🚀 启动开发服务器 (Dev Server)"
```

## 📁 配置文件详解

### `.vscode/launch.json` - 运行和调试配置

包含 11 个预配置的启动选项，分为 4 个类别：

#### 🚀 开发环境
- **启动开发服务器** - Vite 开发服务器 (默认端口 5173)
- **构建项目** - 生产环境构建
- **预览构建结果** - 预览生产版本

#### 🧪 测试环境  
- **运行单元测试** - Vitest 单元测试
- **监视模式测试** - 自动重新运行测试
- **E2E测试** - Playwright 端到端测试
- **E2E测试UI** - Playwright UI 模式
- **E2E调试模式** - Playwright 调试模式
- **测试覆盖率** - 生成覆盖率报告

#### 🔧 开发工具
- **代码检查** - ESLint 自动修复
- **代码格式化** - Prettier 格式化

#### 🔄 组合配置
- **完整开发环境** - 同时启动开发服务器和测试监视

### `.vscode/tasks.json` - 任务配置

提供 13 个预定义任务：
- 🚀 启动开发服务器 (默认构建任务)
- 🔧 构建生产版本
- 👀 预览构建结果
- 🧪 运行单元测试
- 🔍 监视模式测试
- 📊 测试覆盖率报告
- 🎭 运行E2E测试
- 🎭 E2E测试UI模式
- 🐛 E2E调试模式
- 📈 查看E2E测试报告
- 🔧 代码检查
- 💅 代码格式化
- 🧹 清理并重新安装依赖

### `.vscode/settings.json` - 工作区设置

#### 编辑器优化
```json
{
  "editor.tabSize": 2,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  }
}
```

#### 文件类型关联
- `*.vue` → Vue 语言支持
- `*.ts` → TypeScript 支持
- `*.js` → JavaScript 支持

#### 性能优化
- 排除 `node_modules`、`dist`、`coverage` 等目录
- 优化文件监视和搜索
- TypeScript 增量编译

### `.vscode/extensions.json` - 推荐扩展

#### 必备扩展
- **Vue.volar** - Vue 3 官方语言支持
- **esbenp.prettier-vscode** - 代码格式化
- **dbaeumer.vscode-eslint** - 代码质量检查

#### 测试工具
- **ms-playwright.playwright** - Playwright 测试支持
- **vitest.explorer** - Vitest 测试资源管理器

#### 开发工具
- **eamodio.gitlens** - Git 增强功能
- **christian-kohler.path-intellisense** - 路径智能提示
- **formulahendry.auto-rename-tag** - 自动重命名标签

## 🔧 使用方法

### 启动开发服务器

#### 方法 1: VSCode 运行配置
1. 按 `F5` 或点击侧边栏"运行和调试"
2. 选择"🚀 启动开发服务器 (Dev Server)"
3. 点击绿色播放按钮

#### 方法 2: 任务面板
1. `Ctrl+Shift+P` → `Tasks: Run Task`
2. 选择"🚀 启动开发服务器"

#### 方法 3: 快捷脚本
- Windows: 双击 `start-dev.bat`
- PowerShell: `.\scripts\dev-start.ps1`

### 运行测试

#### 单元测试
```bash
# 一次性运行
F5 → "🧪 运行单元测试"

# 监视模式（推荐）
F5 → "🔍 监视模式测试"
```

#### E2E 测试
```bash
# 标准模式
F5 → "🎭 E2E测试"

# UI 模式（可视化）
F5 → "🎭 E2E测试UI"

# 调试模式
F5 → "🐛 E2E调试模式"
```

### 代码质量检查

#### 自动检查（推荐）
- 保存时自动运行 ESLint 和 Prettier
- 导入语句自动整理
- TypeScript 实时类型检查

#### 手动检查
```bash
# 代码检查
F5 → "🔧 代码检查"

# 代码格式化  
F5 → "💅 代码格式化"
```

## 🎯 开发工作流

### 日常开发
1. **启动环境**: `F5` → "🚀 启动开发服务器"
2. **编写代码**: 自动格式化和类型检查
3. **运行测试**: `F5` → "🔍 监视模式测试"
4. **提交前检查**: 运行完整测试套件

### 调试流程
1. 在代码中设置断点
2. 启动对应的调试配置
3. 在浏览器或测试中触发断点
4. 使用 VSCode 调试工具分析问题

## 🚨 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :5173

# 解决方案：Vite 会自动选择其他端口
```

#### 2. 依赖问题
```bash
# 清理重装
.\scripts\dev-start.ps1 -Clean

# 或手动清理
rm -rf node_modules package-lock.json
npm install
```

#### 3. TypeScript 错误
```bash
# 重启 TS 服务
Ctrl+Shift+P → "TypeScript: Restart TS Server"
```

#### 4. ESLint 不工作
- 确保安装了 ESLint 扩展
- 检查 `.eslintrc` 配置文件
- 重启 VSCode

### 性能优化

#### 大型项目优化
```json
// 在 settings.json 中添加
{
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true
  }
}
```

## 📊 功能特性

### ✅ 已实现功能
- 🚀 一键启动开发环境
- 🧪 完整测试套件支持
- 🔧 自动代码质量检查
- 🎯 智能调试配置
- 📝 代码自动格式化
- 🔄 热重载开发体验
- 📊 测试覆盖率报告
- 🎭 可视化 E2E 测试

### 🔮 扩展功能
- 环境变量配置支持
- 多浏览器测试
- 性能监控集成
- Git 工作流优化
- 代码审查工具

## 📚 相关资源

- [VSCode 官方文档](https://code.visualstudio.com/docs)
- [Vite 配置指南](https://vitejs.dev/config/)
- [Vue 3 开发指南](https://vuejs.org/guide/)
- [Vitest 测试框架](https://vitest.dev/)
- [Playwright E2E 测试](https://playwright.dev/)

## 🤝 支持与反馈

如有问题或建议，请：
1. 查看 `.vscode/README.md` 详细文档
2. 检查故障排除部分
3. 创建 Issue 报告问题
4. 提交 PR 改进配置
