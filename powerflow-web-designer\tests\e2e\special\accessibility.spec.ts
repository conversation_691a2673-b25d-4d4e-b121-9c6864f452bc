import { test, expect } from '@playwright/test';
import { navigateToHome, navigateToEditor, navigateToViewer, runAccessibilityScan } from '../helpers';

/**
 * Level 4: Special Accessibility Tests
 * These tests verify that the application meets accessibility standards.
 */

test.describe('Accessibility', () => {
  test.beforeEach(async ({ page }) => {
    // Inject axe-core for accessibility testing
    await page.addScriptTag({
      url: 'https://cdnjs.cloudflare.com/ajax/libs/axe-core/4.7.0/axe.min.js'
    });
  });

  test('homepage should be accessible', async ({ page }) => {
    await navigateToHome(page);
    
    // Run accessibility scan
    const accessibilityScanResults = await runAccessibilityScan(page);
    
    // Log any violations
    if (accessibilityScanResults.violations.length > 0) {
      console.log('Accessibility violations found on homepage:');
      console.log(JSON.stringify(accessibilityScanResults.violations, null, 2));
    }
    
    // Check that there are no critical accessibility violations
    const criticalViolations = accessibilityScanResults.violations.filter(
      violation => violation.impact === 'critical'
    );
    
    expect(criticalViolations.length).toBe(0);
  });

  test('editor should be accessible', async ({ page }) => {
    await navigateToEditor(page);
    
    // Run accessibility scan
    const accessibilityScanResults = await runAccessibilityScan(page);
    
    // Log any violations
    if (accessibilityScanResults.violations.length > 0) {
      console.log('Accessibility violations found on editor page:');
      console.log(JSON.stringify(accessibilityScanResults.violations, null, 2));
    }
    
    // Check that there are no critical accessibility violations
    const criticalViolations = accessibilityScanResults.violations.filter(
      violation => violation.impact === 'critical'
    );
    
    expect(criticalViolations.length).toBe(0);
  });

  test('viewer should be accessible', async ({ page }) => {
    await navigateToViewer(page);
    
    // Run accessibility scan
    const accessibilityScanResults = await runAccessibilityScan(page);
    
    // Log any violations
    if (accessibilityScanResults.violations.length > 0) {
      console.log('Accessibility violations found on viewer page:');
      console.log(JSON.stringify(accessibilityScanResults.violations, null, 2));
    }
    
    // Check that there are no critical accessibility violations
    const criticalViolations = accessibilityScanResults.violations.filter(
      violation => violation.impact === 'critical'
    );
    
    expect(criticalViolations.length).toBe(0);
  });

  test('should be navigable with keyboard', async ({ page }) => {
    await navigateToHome(page);
    
    // Check that we can navigate to the editor using keyboard
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter');
    
    // Check that we're on the editor page
    await expect(page.locator('.editor-header')).toBeVisible();
    
    // Check that we can navigate back to home using keyboard
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter');
    
    // Check that we're back on the home page
    await expect(page.locator('h1')).toContainText('PowerFlow Web Designer');
  });

  test('should have proper focus indicators', async ({ page }) => {
    await navigateToHome(page);
    
    // Tab to the first button
    await page.keyboard.press('Tab');
    
    // Take a screenshot to verify focus indicator is visible
    await page.screenshot({ path: './test-results/focus-indicator.png' });
    
    // Check that the focused element has a visible focus indicator
    const focusedElement = await page.evaluate(() => {
      const activeElement = document.activeElement;
      if (activeElement) {
        const styles = window.getComputedStyle(activeElement);
        return {
          outlineWidth: styles.outlineWidth,
          outlineStyle: styles.outlineStyle,
          outlineColor: styles.outlineColor
        };
      }
      return null;
    });
    
    console.log('Focus indicator styles:', focusedElement);
    
    // Check that there is some kind of outline
    expect(focusedElement).not.toBeNull();
    expect(focusedElement?.outlineWidth).not.toBe('0px');
  });
});
