<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GB标准符号测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #a8071a;
        }
        .status.loading {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .category {
            margin-bottom: 30px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            overflow: hidden;
        }
        .category-header {
            background: #f0f2f5;
            padding: 15px;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #e8e8e8;
        }
        .symbols-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            padding: 20px;
        }
        .symbol-card {
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
            background: #fafafa;
            transition: all 0.3s;
        }
        .symbol-card:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }
        .symbol-preview {
            width: 60px;
            height: 60px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .symbol-preview svg {
            max-width: 50px;
            max-height: 50px;
            color: #333;
        }
        .symbol-name {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .symbol-id {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        .stats {
            background: #e6f7ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
        .stats-item {
            display: inline-block;
            margin: 0 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PowerFlow Web Designer - GB标准电力符号测试</h1>
            <p>验证新增的GB标准电力符号是否正确加载和显示</p>
        </div>

        <div id="status" class="status loading">
            正在测试符号加载...
        </div>

        <div id="content" style="display: none;">
            <div class="stats">
                <div class="stats-item">测试状态: <span id="test-status">运行中</span></div>
                <div class="stats-item">符号总数: <span id="total-symbols">0</span></div>
                <div class="stats-item">GB标准符号: <span id="gb-symbols">0</span></div>
            </div>

            <div id="results"></div>
        </div>
    </div>

    <script>
        // 测试GB标准符号
        const gbSymbolCategories = {
            'transformer': '变压器',
            'switchgear': '开关设备',
            'motor': '电动机',
            'measurement': '测量设备',
            'protection': '保护设备',
            'transmission': '输电设备',
            'capacitor': '电容器',
            'reactor': '电抗器',
            'relay': '继电器'
        };

        const expectedSymbols = {
            'transformer': ['dry-type-transformer', 'oil-immersed-transformer', 'auto-transformer'],
            'switchgear': ['load-switch', 'earthing-switch', 'motor-operated-switch', 'sf6-circuit-breaker'],
            'motor': ['induction-motor', 'synchronous-motor', 'dc-motor'],
            'measurement': ['ammeter', 'voltmeter', 'power-factor-meter', 'energy-meter', 'frequency-meter'],
            'protection': ['lightning-arrester', 'surge-protector', 'overcurrent-relay', 'differential-relay'],
            'transmission': ['overhead-line', 'underground-cable', 'transmission-tower'],
            'capacitor': ['power-capacitor', 'capacitor-bank', 'coupling-capacitor'],
            'reactor': ['current-limiting-reactor', 'shunt-reactor', 'smoothing-reactor'],
            'relay': ['distance-relay', 'frequency-relay', 'voltage-relay']
        };

        async function testSymbolLoading() {
            const statusEl = document.getElementById('status');
            const contentEl = document.getElementById('content');
            const resultsEl = document.getElementById('results');
            
            try {
                statusEl.textContent = '正在测试符号文件访问...';
                
                let totalSymbols = 0;
                let loadedSymbols = 0;
                let results = [];

                // 测试每个分类的符号文件
                for (const [category, categoryName] of Object.entries(gbSymbolCategories)) {
                    const fileName = `gb-${category === 'switchgear' ? 'switchgear' : 
                                           category === 'measurement' ? 'measurement' : 
                                           category === 'protection' ? 'protection' : 
                                           category === 'transmission' ? 'transmission' : 
                                           category === 'capacitor' ? 'capacitors' : 
                                           category === 'reactor' ? 'reactors' : 
                                           category === 'relay' ? 'relays' : 
                                           category === 'motor' ? 'motors' : 
                                           'transformers'}.json`;
                    
                    try {
                        const response = await fetch(`/src/data/symbols/${fileName}`);
                        if (response.ok) {
                            const symbols = await response.json();
                            const expectedCount = expectedSymbols[category]?.length || 0;
                            totalSymbols += expectedCount;
                            loadedSymbols += symbols.length;
                            
                            results.push({
                                category,
                                categoryName,
                                symbols,
                                expected: expectedCount,
                                loaded: symbols.length,
                                success: symbols.length >= expectedCount
                            });
                        } else {
                            results.push({
                                category,
                                categoryName,
                                symbols: [],
                                expected: expectedSymbols[category]?.length || 0,
                                loaded: 0,
                                success: false,
                                error: `HTTP ${response.status}`
                            });
                        }
                    } catch (error) {
                        results.push({
                            category,
                            categoryName,
                            symbols: [],
                            expected: expectedSymbols[category]?.length || 0,
                            loaded: 0,
                            success: false,
                            error: error.message
                        });
                    }
                }

                // 更新状态
                const successCount = results.filter(r => r.success).length;
                const allSuccess = successCount === results.length;
                
                statusEl.className = `status ${allSuccess ? 'success' : 'error'}`;
                statusEl.textContent = allSuccess ? 
                    `✅ 测试完成！成功加载 ${loadedSymbols} 个GB标准符号` : 
                    `❌ 测试完成，但有 ${results.length - successCount} 个分类加载失败`;

                // 更新统计信息
                document.getElementById('test-status').textContent = allSuccess ? '通过' : '部分失败';
                document.getElementById('total-symbols').textContent = totalSymbols;
                document.getElementById('gb-symbols').textContent = loadedSymbols;

                // 渲染结果
                renderResults(results);
                contentEl.style.display = 'block';

            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 测试失败: ${error.message}`;
            }
        }

        function renderResults(results) {
            const resultsEl = document.getElementById('results');
            resultsEl.innerHTML = '';

            results.forEach(result => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'category';

                const headerDiv = document.createElement('div');
                headerDiv.className = 'category-header';
                headerDiv.innerHTML = `
                    ${result.categoryName} (${result.category})
                    <span style="float: right; color: ${result.success ? '#52c41a' : '#ff4d4f'};">
                        ${result.success ? '✅' : '❌'} ${result.loaded}/${result.expected}
                        ${result.error ? ` - ${result.error}` : ''}
                    </span>
                `;

                categoryDiv.appendChild(headerDiv);

                if (result.symbols.length > 0) {
                    const gridDiv = document.createElement('div');
                    gridDiv.className = 'symbols-grid';

                    result.symbols.forEach(symbol => {
                        const symbolCard = document.createElement('div');
                        symbolCard.className = 'symbol-card';

                        symbolCard.innerHTML = `
                            <div class="symbol-preview">
                                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="10" y="10" width="80" height="80" fill="none" stroke="currentColor" stroke-width="2"/>
                                    <text x="50" y="55" font-size="12" text-anchor="middle" fill="currentColor">符号</text>
                                </svg>
                            </div>
                            <div class="symbol-name">${symbol.name || symbol.id}</div>
                            <div class="symbol-id">${symbol.id}</div>
                        `;

                        gridDiv.appendChild(symbolCard);
                    });

                    categoryDiv.appendChild(gridDiv);
                }

                resultsEl.appendChild(categoryDiv);
            });
        }

        // 启动测试
        testSymbolLoading();
    </script>
</body>
</html>
