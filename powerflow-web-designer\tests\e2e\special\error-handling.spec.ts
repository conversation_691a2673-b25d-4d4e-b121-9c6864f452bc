import { test, expect } from '@playwright/test';
import { navigateToEditor, navigateToViewer } from '../helpers';

/**
 * Level 4: Special Error Handling Tests
 * These tests verify that the application handles errors correctly.
 */

test.describe('Error Handling', () => {
  test('should handle invalid URLs gracefully', async ({ page }) => {
    // Navigate to an invalid URL
    await page.goto('/invalid-page');

    // Check that we get redirected to a 404 page or error page
    await expect(page.locator('body')).toContainText(/not found|404|error/i);
  });

  test('should handle large diagrams without crashing', async ({ page }) => {
    await navigateToEditor(page);

    // Create a new diagram
    await page.getByRole('button', { name: 'New' }).click();
    await page.getByLabel('Diagram Name').fill('Large Diagram Test');
    await page.getByRole('button', { name: 'Create' }).click();

    // Generate a large diagram programmatically
    await page.evaluate(() => {
      const diagramStore = window.__pinia?.state.value?.diagram;
      if (diagramStore && diagramStore.currentDiagram) {
        // Add many symbols
        for (let i = 0; i < 50; i++) {
          const symbol = {
            id: `symbol-${i}`,
            type: 'circuit-breaker',
            x: Math.random() * 1000,
            y: Math.random() * 1000,
            width: 50,
            height: 50,
            rotation: 0,
            label: `Symbol ${i}`,
            layerId: diagramStore.currentDiagram.layers[0].id
          };
          diagramStore.currentDiagram.symbols.push(symbol);
        }
      }
    });

    // Wait for the diagram to render
    await page.waitForTimeout(1000);

    // Check that the application is still responsive
    await expect(page.locator('.diagram-canvas')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Home' })).toBeEnabled();
  });

  test('should handle invalid inputs in property fields', async ({ page }) => {
    await navigateToEditor(page);

    // Create a new diagram
    await page.getByRole('button', { name: 'New' }).click();
    await page.getByLabel('Diagram Name').fill('Invalid Input Test');
    await page.getByRole('button', { name: 'Create' }).click();

    // Add a symbol to the diagram
    const symbolLibrary = page.locator('.symbol-library');
    const firstSymbol = symbolLibrary.locator('.symbol-item').first();
    await firstSymbol.dragTo(page.locator('.diagram-canvas'));

    // Select the symbol
    await page.locator('.symbol-instance').first().click();

    // Try to enter invalid input in a numeric field
    const widthInput = page.locator('.property-panel').getByLabel('Width');
    await widthInput.fill('invalid');

    // Click outside to trigger validation
    await page.locator('.diagram-canvas').click();

    // Check for error message or that the field reverts to a valid value
    await expect(widthInput).not.toHaveValue('invalid');
  });

  test('should handle network errors when loading resources', async ({ page }) => {
    // Intercept requests to simulate network errors
    await page.route('**/*.{png,jpg,jpeg,svg}', route => route.abort());

    // Navigate to the home page
    await page.goto('/');

    // Check that the page still loads despite missing resources
    await expect(page.locator('h1')).toContainText('PowerFlow Web Designer');
  });
});
