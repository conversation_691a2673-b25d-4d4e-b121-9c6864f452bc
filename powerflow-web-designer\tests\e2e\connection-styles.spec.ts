import { test, expect } from '@playwright/test';
import { 
  navigateToEditor, 
  createNewDiagram, 
  addSymbolToDiagram, 
  createConnection,
  applyConnectionTheme,
  createCustomConnectionTheme
} from './helpers';

test.describe('Connection Styles and Themes', () => {
  test('should display the connection theme selector', async ({ page }) => {
    await createNewDiagram(page, 'Connection Theme Test Diagram');
    
    // Add two symbols
    await addSymbolToDiagram(page, 'Circuit Breaker');
    await addSymbolToDiagram(page, 'Transformer');
    
    // Create a connection
    await createConnection(page, 0, 1);
    
    // Select the connection
    await page.locator('.connection').first().click();
    
    // Open the connection theme selector
    await page.getByRole('button', { name: 'Connection Themes' }).click();
    
    // Check that the theme selector is visible
    await expect(page.locator('.connection-theme-selector')).toBeVisible();
    
    // Check that it shows the default themes
    await expect(page.locator('.connection-theme-selector')).toContainText('Default');
    await expect(page.locator('.connection-theme-selector')).toContainText('Dashed');
    await expect(page.locator('.connection-theme-selector')).toContainText('Thick');
  });

  test('should apply a predefined theme to a connection', async ({ page }) => {
    await createNewDiagram(page, 'Apply Theme Test Diagram');
    
    // Add two symbols
    await addSymbolToDiagram(page, 'Circuit Breaker');
    await addSymbolToDiagram(page, 'Transformer');
    
    // Create a connection
    await createConnection(page, 0, 1);
    
    // Get the initial connection style
    const initialStyle = await page.locator('.connection').first().getAttribute('data-theme');
    
    // Apply a theme
    await applyConnectionTheme(page, 0, 'Dashed');
    
    // Check that the theme was applied
    const newStyle = await page.locator('.connection').first().getAttribute('data-theme');
    
    expect(newStyle).not.toBe(initialStyle);
    expect(newStyle).toBe('Dashed');
  });

  test('should create a custom connection theme', async ({ page }) => {
    await createNewDiagram(page, 'Custom Theme Test Diagram');
    
    // Add two symbols
    await addSymbolToDiagram(page, 'Circuit Breaker');
    await addSymbolToDiagram(page, 'Transformer');
    
    // Create a connection
    await createConnection(page, 0, 1);
    
    // Select the connection
    await page.locator('.connection').first().click();
    
    // Create a custom theme
    await createCustomConnectionTheme(page, 'Custom Blue Theme', 'Blue', '3', 'dashed');
    
    // Check that the custom theme appears in the selector
    await expect(page.locator('.connection-theme-selector')).toContainText('Custom Blue Theme');
  });

  test('should apply a custom theme to a connection', async ({ page }) => {
    await createNewDiagram(page, 'Apply Custom Theme Test Diagram');
    
    // Add two symbols
    await addSymbolToDiagram(page, 'Circuit Breaker');
    await addSymbolToDiagram(page, 'Transformer');
    
    // Create a connection
    await createConnection(page, 0, 1);
    
    // Create a custom theme
    await createCustomConnectionTheme(page, 'Custom Red Theme', 'Red', '4', 'dotted');
    
    // Apply the custom theme
    await applyConnectionTheme(page, 0, 'Custom Red Theme');
    
    // Check that the theme was applied
    const style = await page.locator('.connection').first().getAttribute('data-theme');
    expect(style).toBe('Custom Red Theme');
    
    // Check the visual properties
    const connectionElement = page.locator('.connection').first();
    const strokeColor = await connectionElement.evaluate(el => window.getComputedStyle(el).stroke);
    const strokeWidth = await connectionElement.evaluate(el => window.getComputedStyle(el).strokeWidth);
    const strokeDasharray = await connectionElement.evaluate(el => window.getComputedStyle(el).strokeDasharray);
    
    // Verify the styles match our custom theme
    expect(strokeColor.toLowerCase()).toContain('red');
    expect(parseInt(strokeWidth)).toBe(4);
    expect(strokeDasharray).not.toBe('none');
  });

  test('should edit an existing custom theme', async ({ page }) => {
    await createNewDiagram(page, 'Edit Theme Test Diagram');
    
    // Add two symbols
    await addSymbolToDiagram(page, 'Circuit Breaker');
    await addSymbolToDiagram(page, 'Transformer');
    
    // Create a connection
    await createConnection(page, 0, 1);
    
    // Create a custom theme
    await createCustomConnectionTheme(page, 'Theme To Edit', 'Green', '2', 'solid');
    
    // Open the connection theme selector
    await page.getByRole('button', { name: 'Connection Themes' }).click();
    
    // Click the edit button for the custom theme
    await page.locator('.connection-theme-selector').getByText('Theme To Edit').parent().getByRole('button', { name: 'Edit' }).click();
    
    // Check that the theme editor is visible
    await expect(page.locator('.connection-theme-editor')).toBeVisible();
    
    // Edit the theme
    await page.locator('.connection-theme-editor').getByLabel('Color').selectOption('Purple');
    await page.locator('.connection-theme-editor').getByLabel('Thickness').fill('5');
    
    // Save the changes
    await page.locator('.connection-theme-editor').getByRole('button', { name: 'Save' }).click();
    
    // Apply the edited theme
    await applyConnectionTheme(page, 0, 'Theme To Edit');
    
    // Check the visual properties
    const connectionElement = page.locator('.connection').first();
    const strokeColor = await connectionElement.evaluate(el => window.getComputedStyle(el).stroke);
    const strokeWidth = await connectionElement.evaluate(el => window.getComputedStyle(el).strokeWidth);
    
    // Verify the styles match our edited theme
    expect(strokeColor.toLowerCase()).toContain('purple');
    expect(parseInt(strokeWidth)).toBe(5);
  });
});
