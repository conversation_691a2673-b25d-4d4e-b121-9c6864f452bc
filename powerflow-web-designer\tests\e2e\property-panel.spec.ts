import { test, expect } from '@playwright/test';
import { 
  navigateToEditor, 
  createNewDiagram, 
  addSymbolToDiagram, 
  selectSymbol,
  openPropertyPanel
} from './helpers';

test.describe('Property Panel', () => {
  test('should open the property panel', async ({ page }) => {
    await createNewDiagram(page, 'Property Panel Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Open the property panel
    await openPropertyPanel(page, 0);
    
    // Check that the property panel is visible
    await expect(page.locator('.property-panel')).toBeVisible();
    
    // Check that it shows the correct symbol properties
    await expect(page.locator('.property-panel')).toContainText('Circuit Breaker');
  });

  test('should edit symbol properties', async ({ page }) => {
    await createNewDiagram(page, 'Edit Properties Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Open the property panel
    await openPropertyPanel(page, 0);
    
    // Edit the name property
    await page.locator('.property-panel').getByLabel('Name').fill('Modified Circuit Breaker');
    
    // Save the changes
    await page.locator('.property-panel').getByRole('button', { name: 'Apply' }).click();
    
    // Check that the changes were applied
    await expect(page.locator('.symbol-instance')).toHaveAttribute('data-name', 'Modified Circuit Breaker');
  });

  test('should edit symbol dimensions', async ({ page }) => {
    await createNewDiagram(page, 'Dimensions Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Open the property panel
    await openPropertyPanel(page, 0);
    
    // Get the initial dimensions
    const initialWidth = await page.locator('.property-panel').getByLabel('Width').inputValue();
    const initialHeight = await page.locator('.property-panel').getByLabel('Height').inputValue();
    
    // Edit the dimensions
    await page.locator('.property-panel').getByLabel('Width').fill((parseInt(initialWidth) + 20).toString());
    await page.locator('.property-panel').getByLabel('Height').fill((parseInt(initialHeight) + 20).toString());
    
    // Save the changes
    await page.locator('.property-panel').getByRole('button', { name: 'Apply' }).click();
    
    // Check that the dimensions were updated
    const newWidth = await page.locator('.property-panel').getByLabel('Width').inputValue();
    const newHeight = await page.locator('.property-panel').getByLabel('Height').inputValue();
    
    expect(parseInt(newWidth)).toBeGreaterThan(parseInt(initialWidth));
    expect(parseInt(newHeight)).toBeGreaterThan(parseInt(initialHeight));
  });

  test('should edit symbol position', async ({ page }) => {
    await createNewDiagram(page, 'Position Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Open the property panel
    await openPropertyPanel(page, 0);
    
    // Get the initial position
    const initialX = await page.locator('.property-panel').getByLabel('X Position').inputValue();
    const initialY = await page.locator('.property-panel').getByLabel('Y Position').inputValue();
    
    // Edit the position
    await page.locator('.property-panel').getByLabel('X Position').fill((parseInt(initialX) + 50).toString());
    await page.locator('.property-panel').getByLabel('Y Position').fill((parseInt(initialY) + 50).toString());
    
    // Save the changes
    await page.locator('.property-panel').getByRole('button', { name: 'Apply' }).click();
    
    // Check that the position was updated
    const newX = await page.locator('.property-panel').getByLabel('X Position').inputValue();
    const newY = await page.locator('.property-panel').getByLabel('Y Position').inputValue();
    
    expect(parseInt(newX)).toBeGreaterThan(parseInt(initialX));
    expect(parseInt(newY)).toBeGreaterThan(parseInt(initialY));
  });

  test('should add custom properties', async ({ page }) => {
    await createNewDiagram(page, 'Custom Properties Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Open the property panel
    await openPropertyPanel(page, 0);
    
    // Count initial properties
    const initialPropertyCount = await page.locator('.property-item').count();
    
    // Click the "Add Property" button
    await page.locator('.property-panel').getByRole('button', { name: 'Add Property' }).click();
    
    // Enter the property name and value
    await page.getByLabel('Property Name').fill('Custom Property');
    await page.getByLabel('Property Value').fill('Custom Value');
    
    // Click the Add button
    await page.getByRole('button', { name: 'Add' }).click();
    
    // Check that a new property was added
    await expect(page.locator('.property-item')).toHaveCount(initialPropertyCount + 1);
    await expect(page.locator('.property-panel')).toContainText('Custom Property');
    await expect(page.locator('.property-panel')).toContainText('Custom Value');
  });
});
