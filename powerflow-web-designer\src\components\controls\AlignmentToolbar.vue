<template>
  <div class="alignment-toolbar" v-if="showToolbar">
    <div class="toolbar-section">
      <div class="section-title">Align</div>
      <div class="button-group">
        <button
          class="toolbar-button"
          title="Align Left"
          @click="handleAlign(AlignmentType.LEFT)"
        >
          <i class="fas fa-align-left"></i>
        </button>
        <button
          class="toolbar-button"
          title="Align Center"
          @click="handleAlign(AlignmentType.CENTER)"
        >
          <i class="fas fa-align-center"></i>
        </button>
        <button
          class="toolbar-button"
          title="Align Right"
          @click="handleAlign(AlignmentType.RIGHT)"
        >
          <i class="fas fa-align-right"></i>
        </button>
      </div>
      <div class="button-group">
        <button
          class="toolbar-button"
          title="Align Top"
          @click="handleAlign(AlignmentType.TOP)"
        >
          <i class="fas fa-align-left fa-rotate-90"></i>
        </button>
        <button
          class="toolbar-button"
          title="Align Middle"
          @click="handleAlign(AlignmentType.MIDDLE)"
        >
          <i class="fas fa-align-center fa-rotate-90"></i>
        </button>
        <button
          class="toolbar-button"
          title="Align Bottom"
          @click="handleAlign(AlignmentType.BOTTOM)"
        >
          <i class="fas fa-align-right fa-rotate-90"></i>
        </button>
      </div>
    </div>

    <div class="toolbar-section">
      <div class="section-title">Distribute</div>
      <div class="button-group">
        <div class="dropdown">
          <button
            class="toolbar-button"
            title="Distribute Horizontally"
            @click="toggleHorizontalDropdown"
            :disabled="!canDistribute"
          >
            <i class="fas fa-arrows-alt-h"></i>
          </button>
          <div class="dropdown-content" v-if="showHorizontalDropdown">
            <button class="dropdown-item" @click="handleDistribute(DistributionType.HORIZONTAL)">Centers</button>
            <button class="dropdown-item" @click="handleDistribute(DistributionType.HORIZONTAL_SPACING)">Equal Spacing</button>
            <button class="dropdown-item" @click="handleDistribute(DistributionType.HORIZONTAL_LEFT)">Left Edges</button>
            <button class="dropdown-item" @click="handleDistribute(DistributionType.HORIZONTAL_RIGHT)">Right Edges</button>
          </div>
        </div>
        <div class="dropdown">
          <button
            class="toolbar-button"
            title="Distribute Vertically"
            @click="toggleVerticalDropdown"
            :disabled="!canDistribute"
          >
            <i class="fas fa-arrows-alt-v"></i>
          </button>
          <div class="dropdown-content" v-if="showVerticalDropdown">
            <button class="dropdown-item" @click="handleDistribute(DistributionType.VERTICAL)">Centers</button>
            <button class="dropdown-item" @click="handleDistribute(DistributionType.VERTICAL_SPACING)">Equal Spacing</button>
            <button class="dropdown-item" @click="handleDistribute(DistributionType.VERTICAL_TOP)">Top Edges</button>
            <button class="dropdown-item" @click="handleDistribute(DistributionType.VERTICAL_BOTTOM)">Bottom Edges</button>
          </div>
        </div>
      </div>
    </div>

    <div class="toolbar-section">
      <div class="section-title">Guides</div>
      <div class="button-group">
        <button
          class="toolbar-button"
          :title="showAlignmentGuides ? 'Hide Alignment Guides' : 'Show Alignment Guides'"
          @click="toggleAlignmentGuides"
        >
          <i :class="showAlignmentGuides ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { AlignmentType, DistributionType } from '@/utils/alignment';

// Store
const diagramStore = useDiagramStore();

// State
const showHorizontalDropdown = ref(false);
const showVerticalDropdown = ref(false);
const showAlignmentGuides = ref(true);

// Computed
const showToolbar = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return diagramStore.currentDiagram.selectedSymbolIds.length >= 2;
});

const canDistribute = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return diagramStore.currentDiagram.selectedSymbolIds.length >= 3;
});

// Methods
const handleAlign = (alignmentType: AlignmentType) => {
  diagramStore.alignSymbols(alignmentType);
};

const handleDistribute = (distributionType: DistributionType) => {
  // Close dropdowns
  showHorizontalDropdown.value = false;
  showVerticalDropdown.value = false;

  // Distribute symbols
  diagramStore.distributeSymbols(distributionType);
};

const toggleHorizontalDropdown = () => {
  showHorizontalDropdown.value = !showHorizontalDropdown.value;
  showVerticalDropdown.value = false;
};

const toggleVerticalDropdown = () => {
  showVerticalDropdown.value = !showVerticalDropdown.value;
  showHorizontalDropdown.value = false;
};

const toggleAlignmentGuides = () => {
  showAlignmentGuides.value = !showAlignmentGuides.value;

  // Emit an event to update the alignment guides visibility in the canvas
  diagramStore.setAlignmentGuidesVisibility(showAlignmentGuides.value);
};
</script>

<style scoped>
.alignment-toolbar {
  display: flex;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-section {
  margin-right: 16px;
}

.section-title {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 4px;
  color: #666;
}

.button-group {
  display: flex;
  margin-bottom: 4px;
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.toolbar-button:hover {
  background-color: #e9e9e9;
}

.toolbar-button:active {
  background-color: #ddd;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-button i {
  font-size: 14px;
  color: #333;
}

/* Dropdown styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: block;
  min-width: 120px;
  padding: 4px 0;
  margin: 2px 0 0;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 6px 12px;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: #333;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item:active {
  background-color: #e6f7ff;
}
</style>
