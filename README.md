# PowerFlow Web Designer

PowerFlow Web Designer是一个基于Web的电力系统设计工具，允许用户创建、编辑和模拟电力系统图。

## 项目结构

本项目的主要代码位于`powerflow-web-designer`目录中。根目录的`package.json`文件仅作为包装器，将命令转发到主项目目录。

### 主要目录结构

```
powerflow-web-designer/
├── src/                  # 源代码
│   ├── assets/           # 静态资源
│   ├── components/       # Vue组件
│   ├── stores/           # Pinia状态管理
│   ├── types/            # TypeScript类型定义
│   ├── utils/            # 工具函数
│   ├── App.vue           # 主应用组件
│   └── main.ts           # 应用入口
├── tests/                # 测试文件
│   ├── e2e/              # 端到端测试
│   └── unit/             # 单元测试
├── public/               # 公共静态资源
├── package.json          # 项目依赖和脚本
└── vite.config.ts        # Vite配置
```

## 开发

### 安装依赖

```bash
cd powerflow-web-designer
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 运行测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e
```

## 技术栈

- Vue 3
- TypeScript
- Vite
- Pinia
- Ant Design Vue
- Konva.js
- Vitest
- Playwright
