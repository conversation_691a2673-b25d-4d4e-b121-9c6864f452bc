:root {
  --primary-color: #1890ff;
  --header-background-color: #1890ff;
  --header-border-color: #096dd9;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --font-size-base: 14px;
  --heading-color: rgba(0, 0, 0, 0.85);
  --text-color: rgba(0, 0, 0, 0.65);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  --disabled-color: rgba(0, 0, 0, 0.25);
  --border-radius-base: 2px;
  --border-color-base: #d9d9d9;
  --box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);

  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto <PERSON>s', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  line-height: 1.5;
  font-weight: 400;
  color: var(--text-color);
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  width: 100%;
  height: 100%;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

body {
  margin: 0;
}

h1 {
  font-size: 2em;
  line-height: 1.2;
  color: var(--heading-color);
}

h2 {
  font-size: 1.5em;
  line-height: 1.2;
  color: var(--heading-color);
}

h3 {
  font-size: 1.2em;
  line-height: 1.2;
  color: var(--heading-color);
}

#app {
  width: 100%;
  height: 100%;
}

/* 紧凑型折叠面板样式 */
.symbol-library-panel .ant-collapse > .ant-collapse-item > .ant-collapse-header {
  padding: 3px 6px;
  font-size: 12px;
  min-height: 24px;
  line-height: 1.2;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.85);
}

.symbol-library-panel .ant-collapse-content > .ant-collapse-content-box {
  padding: 2px;
}

.symbol-library-panel .ant-collapse-arrow {
  font-size: 10px !important;
  margin-right: 4px !important;
}

/* 统一折叠面板样式 */
.symbol-library-panel .ant-collapse {
  border: none;
  background-color: transparent;
}

.symbol-library-panel .ant-collapse-item {
  border-bottom: 1px solid #e8e8e8;
}

/* 紧凑型标签页样式 */
.ant-tabs-tab {
  padding: 4px 8px !important;
  font-size: 12px !important;
}

.ant-tabs-tab-btn {
  font-size: 12px !important;
  font-weight: 500 !important;
}

.ant-tabs-nav {
  margin-bottom: 4px !important;
}

/* 紧凑型表单样式 */
.ant-form-item {
  margin-bottom: 12px;
}

.ant-form-item-label > label {
  font-size: 12px;
}

/* 紧凑型输入框样式 */
.ant-input {
  padding: 2px 6px;
  font-size: 12px;
  height: 24px;
}

.ant-input-search .ant-input {
  padding: 2px 6px;
  font-size: 12px;
  height: 24px;
}

.ant-input-search .ant-input-search-button {
  height: 24px;
  width: 24px;
  padding: 0;
}

.symbol-library-panel .ant-input-search {
  margin-bottom: 0;
}

/* 紧凑型按钮样式 */
.ant-btn {
  padding: 0 8px;
  font-size: 12px;
  height: 24px;
  line-height: 22px;
}

.ant-btn-sm {
  height: 22px;
  padding: 0 4px;
  font-size: 12px;
  line-height: 20px;
}

/* 统一按钮图标大小 */
.ant-btn .anticon {
  font-size: 14px;
}

/* 紧凑型下拉菜单样式 */
.ant-select-single .ant-select-selector {
  height: 28px !important;
}

.ant-select-single .ant-select-selector .ant-select-selection-item {
  line-height: 28px !important;
}

.ant-select-single.ant-select-sm .ant-select-selector {
  height: 24px !important;
}

.ant-select-single.ant-select-sm .ant-select-selector .ant-select-selection-item {
  line-height: 24px !important;
}

/* 紧凑型数字输入框样式 */
.ant-input-number {
  height: 28px;
  line-height: 28px;
}

.ant-input-number-input {
  height: 28px;
  font-size: 12px;
}

.ant-input-number-sm {
  height: 24px;
  line-height: 24px;
}

.ant-input-number-sm .ant-input-number-input {
  height: 24px;
  font-size: 12px;
}
