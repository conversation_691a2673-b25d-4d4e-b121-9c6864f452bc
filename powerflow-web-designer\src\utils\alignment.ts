/**
 * Alignment and distribution utilities for PowerFlow Web Designer
 * Implements functions for aligning and distributing elements
 */

import type { Position } from '@/types/symbol';
import type { SymbolInstance } from '@/types/symbolInstance';

// Alignment types
export enum AlignmentType {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
  TOP = 'top',
  MIDDLE = 'middle',
  BOTTOM = 'bottom',
}

// Distribution types
export enum DistributionType {
  HORIZONTAL = 'horizontal',           // Distribute centers horizontally
  VERTICAL = 'vertical',               // Distribute centers vertically
  HORIZONTAL_SPACING = 'horizontal-spacing', // Distribute with equal spacing horizontally
  VERTICAL_SPACING = 'vertical-spacing',     // Distribute with equal spacing vertically
  HORIZONTAL_LEFT = 'horizontal-left',       // Distribute left edges horizontally
  HORIZONTAL_RIGHT = 'horizontal-right',     // Distribute right edges horizontally
  VERTICAL_TOP = 'vertical-top',             // Distribute top edges vertically
  VERTICAL_BOTTOM = 'vertical-bottom',       // Distribute bottom edges vertically
}

// Symbol bounds interface
interface SymbolBounds {
  id: string;
  left: number;
  top: number;
  right: number;
  bottom: number;
  width: number;
  height: number;
  centerX: number;
  centerY: number;
  rotation?: number;
}

/**
 * Calculate the bounds of a symbol instance
 * @param symbol Symbol instance
 * @returns Symbol bounds
 */
export function calculateSymbolBounds(symbol: SymbolInstance): SymbolBounds {
  // Use the actual dimensions of the symbol
  const width = symbol.dimensions?.width || 100;
  const height = symbol.dimensions?.height || 100;

  // Account for rotation if present
  const rotation = symbol.rotation || 0;

  // Calculate bounds based on position and dimensions
  const left = symbol.position.x;
  const top = symbol.position.y;
  const right = left + width;
  const bottom = top + height;
  const centerX = left + width / 2;
  const centerY = top + height / 2;

  return {
    id: symbol.id,
    left,
    top,
    right,
    bottom,
    width,
    height,
    centerX,
    centerY,
    rotation,
  };
}

/**
 * Calculate the bounds of multiple symbol instances
 * @param symbols Array of symbol instances
 * @returns Array of symbol bounds
 */
export function calculateSymbolsBounds(symbols: SymbolInstance[]): SymbolBounds[] {
  return symbols.map(calculateSymbolBounds);
}

/**
 * Calculate the group bounds of multiple symbol instances
 * @param symbols Array of symbol instances
 * @returns Group bounds
 */
export function calculateGroupBounds(symbols: SymbolInstance[]): SymbolBounds {
  const bounds = calculateSymbolsBounds(symbols);

  // Calculate the bounding box that contains all symbols
  const left = Math.min(...bounds.map(b => b.left));
  const top = Math.min(...bounds.map(b => b.top));
  const right = Math.max(...bounds.map(b => b.right));
  const bottom = Math.max(...bounds.map(b => b.bottom));
  const width = right - left;
  const height = bottom - top;
  const centerX = left + width / 2;
  const centerY = top + height / 2;

  return {
    id: 'group',
    left,
    top,
    right,
    bottom,
    width,
    height,
    centerX,
    centerY,
    // Group doesn't have rotation
    rotation: 0,
  };
}

/**
 * Align symbols according to the specified alignment type
 * @param symbols Array of symbol instances
 * @param alignmentType Alignment type
 * @returns Array of new positions for the symbols
 */
export function alignSymbols(
  symbols: SymbolInstance[],
  alignmentType: AlignmentType
): { id: string; position: Position }[] {
  if (symbols.length < 2) {
    return symbols.map(s => ({ id: s.id, position: { ...s.position } }));
  }

  const bounds = calculateSymbolsBounds(symbols);
  const groupBounds = calculateGroupBounds(symbols);

  // Calculate new positions based on alignment type
  return bounds.map(b => {
    const position = { ...symbols.find(s => s.id === b.id)!.position };

    switch (alignmentType) {
      case AlignmentType.LEFT:
        position.x = groupBounds.left;
        break;
      case AlignmentType.CENTER:
        position.x = groupBounds.centerX - b.width / 2;
        break;
      case AlignmentType.RIGHT:
        position.x = groupBounds.right - b.width;
        break;
      case AlignmentType.TOP:
        position.y = groupBounds.top;
        break;
      case AlignmentType.MIDDLE:
        position.y = groupBounds.centerY - b.height / 2;
        break;
      case AlignmentType.BOTTOM:
        position.y = groupBounds.bottom - b.height;
        break;
    }

    return { id: b.id, position };
  });
}

/**
 * Distribute symbols according to the specified distribution type
 * @param symbols Array of symbol instances
 * @param distributionType Distribution type
 * @returns Array of new positions for the symbols
 */
export function distributeSymbols(
  symbols: SymbolInstance[],
  distributionType: DistributionType
): { id: string; position: Position }[] {
  if (symbols.length < 3) {
    return symbols.map(s => ({ id: s.id, position: { ...s.position } }));
  }

  const bounds = calculateSymbolsBounds(symbols);
  const groupBounds = calculateGroupBounds(symbols);

  // Sort bounds based on distribution type
  const sortedBounds = [...bounds].sort((a, b) => {
    switch (distributionType) {
      case DistributionType.HORIZONTAL:
      case DistributionType.HORIZONTAL_SPACING:
        return a.centerX - b.centerX;
      case DistributionType.VERTICAL:
      case DistributionType.VERTICAL_SPACING:
        return a.centerY - b.centerY;
      case DistributionType.HORIZONTAL_LEFT:
        return a.left - b.left;
      case DistributionType.HORIZONTAL_RIGHT:
        return a.right - b.right;
      case DistributionType.VERTICAL_TOP:
        return a.top - b.top;
      case DistributionType.VERTICAL_BOTTOM:
        return a.bottom - b.bottom;
      default:
        return a.centerX - b.centerX;
    }
  });

  const newPositions: { id: string; position: Position }[] = [];

  // Handle different distribution types
  switch (distributionType) {
    case DistributionType.HORIZONTAL:
      distributeHorizontally(sortedBounds, groupBounds, symbols, newPositions);
      break;
    case DistributionType.VERTICAL:
      distributeVertically(sortedBounds, groupBounds, symbols, newPositions);
      break;
    case DistributionType.HORIZONTAL_SPACING:
      distributeHorizontallyWithEqualSpacing(sortedBounds, groupBounds, symbols, newPositions);
      break;
    case DistributionType.VERTICAL_SPACING:
      distributeVerticallyWithEqualSpacing(sortedBounds, groupBounds, symbols, newPositions);
      break;
    case DistributionType.HORIZONTAL_LEFT:
      distributeHorizontallyByLeftEdge(sortedBounds, groupBounds, symbols, newPositions);
      break;
    case DistributionType.HORIZONTAL_RIGHT:
      distributeHorizontallyByRightEdge(sortedBounds, groupBounds, symbols, newPositions);
      break;
    case DistributionType.VERTICAL_TOP:
      distributeVerticallyByTopEdge(sortedBounds, groupBounds, symbols, newPositions);
      break;
    case DistributionType.VERTICAL_BOTTOM:
      distributeVerticallyByBottomEdge(sortedBounds, groupBounds, symbols, newPositions);
      break;
    default:
      distributeHorizontally(sortedBounds, groupBounds, symbols, newPositions);
  }

  return newPositions;
}

/**
 * Distribute symbols horizontally (center to center)
 */
function distributeHorizontally(
  sortedBounds: SymbolBounds[],
  groupBounds: SymbolBounds,
  symbols: SymbolInstance[],
  newPositions: { id: string; position: Position }[]
): void {
  // Calculate the total distance between first and last center
  const firstCenter = sortedBounds[0].centerX;
  const lastCenter = sortedBounds[sortedBounds.length - 1].centerX;
  const totalDistance = lastCenter - firstCenter;

  // Calculate the distance between centers
  const distanceBetweenCenters = totalDistance / (sortedBounds.length - 1);

  // Position each symbol
  for (let i = 0; i < sortedBounds.length; i++) {
    const bound = sortedBounds[i];
    const symbol = symbols.find(s => s.id === bound.id)!;
    const position = { ...symbol.position };

    // Calculate new center position
    const newCenterX = firstCenter + i * distanceBetweenCenters;

    // Adjust position to align center
    position.x = newCenterX - bound.width / 2;

    newPositions.push({ id: bound.id, position });
  }
}

/**
 * Distribute symbols vertically (center to center)
 */
function distributeVertically(
  sortedBounds: SymbolBounds[],
  groupBounds: SymbolBounds,
  symbols: SymbolInstance[],
  newPositions: { id: string; position: Position }[]
): void {
  // Calculate the total distance between first and last center
  const firstCenter = sortedBounds[0].centerY;
  const lastCenter = sortedBounds[sortedBounds.length - 1].centerY;
  const totalDistance = lastCenter - firstCenter;

  // Calculate the distance between centers
  const distanceBetweenCenters = totalDistance / (sortedBounds.length - 1);

  // Position each symbol
  for (let i = 0; i < sortedBounds.length; i++) {
    const bound = sortedBounds[i];
    const symbol = symbols.find(s => s.id === bound.id)!;
    const position = { ...symbol.position };

    // Calculate new center position
    const newCenterY = firstCenter + i * distanceBetweenCenters;

    // Adjust position to align center
    position.y = newCenterY - bound.height / 2;

    newPositions.push({ id: bound.id, position });
  }
}

/**
 * Distribute symbols horizontally with equal spacing between them
 */
function distributeHorizontallyWithEqualSpacing(
  sortedBounds: SymbolBounds[],
  groupBounds: SymbolBounds,
  symbols: SymbolInstance[],
  newPositions: { id: string; position: Position }[]
): void {
  // Calculate total width of all symbols
  const totalSymbolWidth = sortedBounds.reduce((sum, bound) => sum + bound.width, 0);

  // Calculate available space
  const availableSpace = groupBounds.width - totalSymbolWidth;

  // Calculate space between symbols
  const spaceBetween = availableSpace / (sortedBounds.length - 1);

  // Position each symbol
  let currentX = groupBounds.left;

  for (const bound of sortedBounds) {
    const symbol = symbols.find(s => s.id === bound.id)!;
    const position = { ...symbol.position };

    position.x = currentX;
    currentX += bound.width + spaceBetween;

    newPositions.push({ id: bound.id, position });
  }
}

/**
 * Distribute symbols vertically with equal spacing between them
 */
function distributeVerticallyWithEqualSpacing(
  sortedBounds: SymbolBounds[],
  groupBounds: SymbolBounds,
  symbols: SymbolInstance[],
  newPositions: { id: string; position: Position }[]
): void {
  // Calculate total height of all symbols
  const totalSymbolHeight = sortedBounds.reduce((sum, bound) => sum + bound.height, 0);

  // Calculate available space
  const availableSpace = groupBounds.height - totalSymbolHeight;

  // Calculate space between symbols
  const spaceBetween = availableSpace / (sortedBounds.length - 1);

  // Position each symbol
  let currentY = groupBounds.top;

  for (const bound of sortedBounds) {
    const symbol = symbols.find(s => s.id === bound.id)!;
    const position = { ...symbol.position };

    position.y = currentY;
    currentY += bound.height + spaceBetween;

    newPositions.push({ id: bound.id, position });
  }
}

/**
 * Distribute symbols horizontally by left edge
 */
function distributeHorizontallyByLeftEdge(
  sortedBounds: SymbolBounds[],
  groupBounds: SymbolBounds,
  symbols: SymbolInstance[],
  newPositions: { id: string; position: Position }[]
): void {
  // Calculate the total distance between first and last left edge
  const firstLeft = sortedBounds[0].left;
  const lastLeft = sortedBounds[sortedBounds.length - 1].left;
  const totalDistance = lastLeft - firstLeft;

  // Calculate the distance between left edges
  const distanceBetweenLeftEdges = totalDistance / (sortedBounds.length - 1);

  // Position each symbol
  for (let i = 0; i < sortedBounds.length; i++) {
    const bound = sortedBounds[i];
    const symbol = symbols.find(s => s.id === bound.id)!;
    const position = { ...symbol.position };

    // Calculate new left position
    position.x = firstLeft + i * distanceBetweenLeftEdges;

    newPositions.push({ id: bound.id, position });
  }
}

/**
 * Distribute symbols horizontally by right edge
 */
function distributeHorizontallyByRightEdge(
  sortedBounds: SymbolBounds[],
  groupBounds: SymbolBounds,
  symbols: SymbolInstance[],
  newPositions: { id: string; position: Position }[]
): void {
  // Calculate the total distance between first and last right edge
  const firstRight = sortedBounds[0].right;
  const lastRight = sortedBounds[sortedBounds.length - 1].right;
  const totalDistance = lastRight - firstRight;

  // Calculate the distance between right edges
  const distanceBetweenRightEdges = totalDistance / (sortedBounds.length - 1);

  // Position each symbol
  for (let i = 0; i < sortedBounds.length; i++) {
    const bound = sortedBounds[i];
    const symbol = symbols.find(s => s.id === bound.id)!;
    const position = { ...symbol.position };

    // Calculate new right position and convert to left position
    const newRight = firstRight + i * distanceBetweenRightEdges;
    position.x = newRight - bound.width;

    newPositions.push({ id: bound.id, position });
  }
}

/**
 * Distribute symbols vertically by top edge
 */
function distributeVerticallyByTopEdge(
  sortedBounds: SymbolBounds[],
  groupBounds: SymbolBounds,
  symbols: SymbolInstance[],
  newPositions: { id: string; position: Position }[]
): void {
  // Calculate the total distance between first and last top edge
  const firstTop = sortedBounds[0].top;
  const lastTop = sortedBounds[sortedBounds.length - 1].top;
  const totalDistance = lastTop - firstTop;

  // Calculate the distance between top edges
  const distanceBetweenTopEdges = totalDistance / (sortedBounds.length - 1);

  // Position each symbol
  for (let i = 0; i < sortedBounds.length; i++) {
    const bound = sortedBounds[i];
    const symbol = symbols.find(s => s.id === bound.id)!;
    const position = { ...symbol.position };

    // Calculate new top position
    position.y = firstTop + i * distanceBetweenTopEdges;

    newPositions.push({ id: bound.id, position });
  }
}

/**
 * Distribute symbols vertically by bottom edge
 */
function distributeVerticallyByBottomEdge(
  sortedBounds: SymbolBounds[],
  groupBounds: SymbolBounds,
  symbols: SymbolInstance[],
  newPositions: { id: string; position: Position }[]
): void {
  // Calculate the total distance between first and last bottom edge
  const firstBottom = sortedBounds[0].bottom;
  const lastBottom = sortedBounds[sortedBounds.length - 1].bottom;
  const totalDistance = lastBottom - firstBottom;

  // Calculate the distance between bottom edges
  const distanceBetweenBottomEdges = totalDistance / (sortedBounds.length - 1);

  // Position each symbol
  for (let i = 0; i < sortedBounds.length; i++) {
    const bound = sortedBounds[i];
    const symbol = symbols.find(s => s.id === bound.id)!;
    const position = { ...symbol.position };

    // Calculate new bottom position and convert to top position
    const newBottom = firstBottom + i * distanceBetweenBottomEdges;
    position.y = newBottom - bound.height;

    newPositions.push({ id: bound.id, position });
  }
}

/**
 * Create alignment guides for the specified symbols and alignment type
 * @param symbols Array of symbol instances
 * @param alignmentType Alignment type
 * @returns Array of guide lines
 */
export function createAlignmentGuides(
  symbols: SymbolInstance[],
  alignmentType: AlignmentType
): { start: Position; end: Position }[] {
  if (symbols.length < 2) {
    return [];
  }

  const bounds = calculateSymbolsBounds(symbols);
  const groupBounds = calculateGroupBounds(symbols);

  // Create guide line based on alignment type
  let start: Position;
  let end: Position;

  switch (alignmentType) {
    case AlignmentType.LEFT:
      start = { x: groupBounds.left, y: groupBounds.top - 10 };
      end = { x: groupBounds.left, y: groupBounds.bottom + 10 };
      break;
    case AlignmentType.CENTER:
      start = { x: groupBounds.centerX, y: groupBounds.top - 10 };
      end = { x: groupBounds.centerX, y: groupBounds.bottom + 10 };
      break;
    case AlignmentType.RIGHT:
      start = { x: groupBounds.right, y: groupBounds.top - 10 };
      end = { x: groupBounds.right, y: groupBounds.bottom + 10 };
      break;
    case AlignmentType.TOP:
      start = { x: groupBounds.left - 10, y: groupBounds.top };
      end = { x: groupBounds.right + 10, y: groupBounds.top };
      break;
    case AlignmentType.MIDDLE:
      start = { x: groupBounds.left - 10, y: groupBounds.centerY };
      end = { x: groupBounds.right + 10, y: groupBounds.centerY };
      break;
    case AlignmentType.BOTTOM:
      start = { x: groupBounds.left - 10, y: groupBounds.bottom };
      end = { x: groupBounds.right + 10, y: groupBounds.bottom };
      break;
    default:
      return [];
  }

  return [{ start, end }];
}
