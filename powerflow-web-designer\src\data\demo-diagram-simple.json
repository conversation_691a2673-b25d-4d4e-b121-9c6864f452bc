{"name": "Demo", "description": "Simple demo diagram", "size": {"width": 5000, "height": 5000}, "grid": {"visible": false, "size": 20, "snapToGrid": false, "majorGridSize": 100, "minorColor": "#e0e0e0", "majorColor": "#c0c0c0", "pattern": "lines"}, "viewport": {"position": {"x": 0, "y": 0}, "scale": 1}, "layers": [{"id": "layer-1", "name": "Layer 1", "visible": true, "locked": false, "symbolInstanceIds": ["input1", "transformer1", "load1"], "connectionIds": ["edge1", "edge2"]}], "symbolInstances": {"input1": {"id": "input1", "definitionId": "circuit-breaker", "position": {"x": 100, "y": 100}, "rotation": 0, "scale": 1, "bindings": {"status": true, "current": 100}, "properties": {"name": "Input Breaker"}}, "transformer1": {"id": "transformer1", "definitionId": "transformer", "position": {"x": 300, "y": 100}, "rotation": 0, "scale": 1, "bindings": {}, "properties": {"name": "Transformer"}}, "load1": {"id": "load1", "definitionId": "load", "position": {"x": 500, "y": 100}, "rotation": 0, "scale": 1, "bindings": {}, "properties": {"name": "Load"}}}, "connections": {"edge1": {"id": "edge1", "source": {"symbolInstanceId": "input1", "connectionPointId": "out"}, "target": {"symbolInstanceId": "transformer1", "connectionPointId": "primary"}, "type": "cable", "lineType": "polyline", "label": "Input Cable", "waypoints": [], "style": {"strokeColor": "black", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": true, "padding": 20}}, "edge2": {"id": "edge2", "source": {"symbolInstanceId": "transformer1", "connectionPointId": "secondary"}, "target": {"symbolInstanceId": "load1", "connectionPointId": "in"}, "type": "cable", "lineType": "polyline", "label": "Output Cable", "waypoints": [], "style": {"strokeColor": "black", "lineWidth": 2, "lineCap": "round", "lineJoin": "round", "lineOpacity": 1}, "routingOptions": {"autoRoute": true, "horizontalFirst": true, "padding": 20}}}, "groups": {}, "selectedSymbolIds": [], "selectedConnectionIds": [], "selectedGroupIds": [], "version": 1}