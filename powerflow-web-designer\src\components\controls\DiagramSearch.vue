<template>
  <div class="diagram-search">
    <a-input-search
      v-model:value="searchText"
      :placeholder="$locale.t('search.placeholder')"
      style="width: 250px"
      @search="handleSearch"
      @change="handleSearchChange"
    />

    <a-dropdown v-if="searchResults.length > 0" :trigger="['click']" placement="bottomRight">
      <template #overlay>
        <a-menu>
          <a-menu-item
            v-for="result in searchResults"
            :key="result.id"
            @click="selectResult(result)"
          >
            <div class="search-result-item">
              <div class="search-result-icon">
                <component :is="result.type === 'symbol' ? 'box-outlined' : 'line-outlined'" />
              </div>
              <div class="search-result-info">
                <div class="search-result-name">{{ result.name }}</div>
                <div class="search-result-type">{{ result.typeName }}</div>
              </div>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
      <a-button type="text">
        {{ searchResults.length }} {{ $locale.t('search.results') }} <down-outlined />
      </a-button>
    </a-dropdown>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { DownOutlined, BoxOutlined, LineOutlined } from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';
import { Position } from '@/types/symbol';

// Props
const props = defineProps<{
  stageRef: any;
}>();

// Emits
const emit = defineEmits<{
  (e: 'focusElement', id: string, type: 'symbol' | 'connection', position: Position): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const searchText = ref('');
const searchResults = ref<Array<{
  id: string;
  name: string;
  type: 'symbol' | 'connection';
  typeName: string;
  position: Position;
}>>([]);

// Methods
const handleSearch = () => {
  if (!searchText.value.trim() || !diagramStore.currentDiagram) {
    searchResults.value = [];
    return;
  }

  const results: typeof searchResults.value = [];
  const searchLower = searchText.value.toLowerCase();

  // Search in symbols
  Object.values(diagramStore.currentDiagram.symbolInstances).forEach(symbol => {
    const definition = diagramStore.getSymbolDefinition(symbol.definitionId);
    if (!definition) return;

    // Check if the symbol name matches
    if (definition.name.toLowerCase().includes(searchLower)) {
      results.push({
        id: symbol.id,
        name: definition.name,
        type: 'symbol',
        typeName: `Symbol (${definition.category})`,
        position: symbol.position,
      });
    }

    // Check if any binding value matches
    Object.values(symbol.bindings).forEach(binding => {
      const bindingValue = String(binding.value);
      if (bindingValue.toLowerCase().includes(searchLower)) {
        const slot = definition.bindingSlots.find(s => s.id === binding.slotId);
        results.push({
          id: symbol.id,
          name: `${definition.name} (${slot?.name || 'Binding'}: ${bindingValue})`,
          type: 'symbol',
          typeName: `Symbol (${definition.category})`,
          position: symbol.position,
        });
      }
    });
  });

  // Search in connections
  Object.values(diagramStore.currentDiagram.connections).forEach(connection => {
    // Check if the connection label matches
    if (connection.label && connection.label.toLowerCase().includes(searchLower)) {
      // Calculate the middle point of the connection
      const sourceSymbol = diagramStore.currentDiagram?.symbolInstances[connection.source.symbolInstanceId];
      const targetSymbol = diagramStore.currentDiagram?.symbolInstances[connection.target.symbolInstanceId];

      if (!sourceSymbol || !targetSymbol) return;

      const position = {
        x: (sourceSymbol.position.x + targetSymbol.position.x) / 2,
        y: (sourceSymbol.position.y + targetSymbol.position.y) / 2,
      };

      results.push({
        id: connection.id,
        name: connection.label,
        type: 'connection',
        typeName: `Connection (${connection.type})`,
        position,
      });
    }
  });

  searchResults.value = results;
};

const handleSearchChange = () => {
  // Auto-search as the user types
  handleSearch();
};

const selectResult = (result: typeof searchResults.value[0]) => {
  if (!diagramStore.currentDiagram) return;

  // Select the element
  if (result.type === 'symbol') {
    diagramStore.currentDiagram.selectedSymbolIds = [result.id];
    diagramStore.currentDiagram.selectedConnectionIds = [];
  } else {
    diagramStore.currentDiagram.selectedSymbolIds = [];
    diagramStore.currentDiagram.selectedConnectionIds = [result.id];
  }

  // Focus on the element
  emit('focusElement', result.id, result.type, result.position);
};

// Watch for diagram changes
watch(() => diagramStore.currentDiagram, () => {
  // Clear search results when the diagram changes
  searchResults.value = [];
  searchText.value = '';
}, { deep: true });
</script>

<style scoped>
.diagram-search {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.search-result-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: var(--primary-color);
}

.search-result-info {
  display: flex;
  flex-direction: column;
}

.search-result-name {
  font-weight: 500;
}

.search-result-type {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
