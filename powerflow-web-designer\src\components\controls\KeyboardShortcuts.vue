<template>
  <div class="keyboard-shortcuts">
    <a-tooltip title="Keyboard Shortcuts">
      <a-button @click="showShortcutsModal">
        <template #icon><control-outlined /></template>
      </a-button>
    </a-tooltip>

    <a-modal
      v-model:visible="isModalVisible"
      title="Keyboard Shortcuts"
      :footer="null"
      width="900px"
      :body-style="{ padding: '16px', maxHeight: '600px', overflowY: 'auto' }"
    >
      <div class="shortcuts-container">
        <div class="shortcuts-grid">
          <!-- Edit Category -->
          <div class="shortcut-category">
            <h4 class="category-title">Edit</h4>
            <div class="shortcut-list">
              <div class="shortcut-item" v-for="shortcut in editShortcuts" :key="shortcut.key">
                <span class="shortcut-action">{{ shortcut.action }}</span>
                <div class="shortcut-keys">
                  <kbd v-for="(key, index) in shortcut.shortcut.split('+')" :key="index">
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>

          <!-- View Category -->
          <div class="shortcut-category">
            <h4 class="category-title">View</h4>
            <div class="shortcut-list">
              <div class="shortcut-item" v-for="shortcut in viewShortcuts" :key="shortcut.key">
                <span class="shortcut-action">{{ shortcut.action }}</span>
                <div class="shortcut-keys">
                  <kbd v-for="(key, index) in shortcut.shortcut.split('+')" :key="index">
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>

          <!-- Selection Category -->
          <div class="shortcut-category">
            <h4 class="category-title">Selection</h4>
            <div class="shortcut-list">
              <div class="shortcut-item" v-for="shortcut in selectionShortcuts" :key="shortcut.key">
                <span class="shortcut-action">{{ shortcut.action }}</span>
                <div class="shortcut-keys">
                  <kbd v-for="(key, index) in shortcut.shortcut.split('+')" :key="index">
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>

          <!-- Rotation Category -->
          <div class="shortcut-category">
            <h4 class="category-title">Rotation</h4>
            <div class="shortcut-list">
              <div class="shortcut-item" v-for="shortcut in rotationShortcuts" :key="shortcut.key">
                <span class="shortcut-action">{{ shortcut.action }}</span>
                <div class="shortcut-keys">
                  <kbd v-for="(key, index) in shortcut.shortcut.split('+')" :key="index">
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>

          <!-- Alignment Category -->
          <div class="shortcut-category">
            <h4 class="category-title">Alignment</h4>
            <div class="shortcut-list">
              <div class="shortcut-item" v-for="shortcut in alignmentShortcuts" :key="shortcut.key">
                <span class="shortcut-action">{{ shortcut.action }}</span>
                <div class="shortcut-keys">
                  <kbd v-for="(key, index) in shortcut.shortcut.split('+')" :key="index">
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>

          <!-- Grid Category -->
          <div class="shortcut-category">
            <h4 class="category-title">Grid</h4>
            <div class="shortcut-list">
              <div class="shortcut-item" v-for="shortcut in gridShortcuts" :key="shortcut.key">
                <span class="shortcut-action">{{ shortcut.action }}</span>
                <div class="shortcut-keys">
                  <kbd v-for="(key, index) in shortcut.shortcut.split('+')" :key="index">
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
// KeyboardOutlined icon doesn't exist in ant-design/icons-vue
// Using a different icon as a replacement
import { ControlOutlined } from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';
import { message } from 'ant-design-vue';

// Props
const props = defineProps<{
  readOnly?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'delete'): void;
  (e: 'copy'): void;
  (e: 'paste'): void;
  (e: 'cut'): void;
  (e: 'undo'): void;
  (e: 'redo'): void;
  (e: 'save'): void;
  (e: 'selectAll'): void;
  (e: 'escape'): void;
  (e: 'zoomIn'): void;
  (e: 'zoomOut'): void;
  (e: 'resetView'): void;
  (e: 'fitContent'): void;
  (e: 'rotateClockwise'): void;
  (e: 'rotateCounterclockwise'): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const isModalVisible = ref(false);

// Organized shortcuts by category
const editShortcuts = [
  { key: '1', action: 'Copy', shortcut: 'Ctrl+C' },
  { key: '2', action: 'Paste', shortcut: 'Ctrl+V' },
  { key: '3', action: 'Cut', shortcut: 'Ctrl+X' },
  { key: '4', action: 'Delete', shortcut: 'Delete' },
  { key: '5', action: 'Undo', shortcut: 'Ctrl+Z' },
  { key: '6', action: 'Redo', shortcut: 'Ctrl+Y' },
  { key: '7', action: 'Save', shortcut: 'Ctrl+S' },
];

const viewShortcuts = [
  { key: '8', action: 'Zoom In', shortcut: 'Ctrl++' },
  { key: '9', action: 'Zoom Out', shortcut: 'Ctrl+-' },
  { key: '10', action: 'Reset View', shortcut: 'Ctrl+0' },
  { key: '11', action: 'Fit Content', shortcut: 'Ctrl+1' },
  { key: '12', action: 'Zoom to Selection', shortcut: 'Ctrl+2' },
];

const selectionShortcuts = [
  { key: '13', action: 'Select All', shortcut: 'Ctrl+A' },
  { key: '14', action: 'Cancel', shortcut: 'Escape' },
];

const rotationShortcuts = [
  { key: '15', action: 'Rotate Clockwise', shortcut: 'Ctrl+R' },
  { key: '16', action: 'Rotate Counterclockwise', shortcut: 'Ctrl+Shift+R' },
];

const alignmentShortcuts = [
  { key: '17', action: 'Align Left', shortcut: 'Ctrl+Shift+L' },
  { key: '18', action: 'Align Center', shortcut: 'Ctrl+Shift+C' },
  { key: '19', action: 'Align Right', shortcut: 'Ctrl+Shift+R' },
  { key: '20', action: 'Align Top', shortcut: 'Ctrl+Shift+T' },
  { key: '21', action: 'Align Middle', shortcut: 'Ctrl+Shift+M' },
  { key: '22', action: 'Align Bottom', shortcut: 'Ctrl+Shift+B' },
];

const gridShortcuts = [
  { key: '23', action: 'Toggle Grid', shortcut: 'Ctrl+G' },
  { key: '24', action: 'Toggle Snap to Grid', shortcut: 'Ctrl+Shift+G' },
];

// Methods
const showShortcutsModal = () => {
  isModalVisible.value = true;
};


const handleKeyDown = (event: KeyboardEvent) => {
  // Ignore keyboard events when typing in input fields
  if (
    event.target instanceof HTMLInputElement ||
    event.target instanceof HTMLTextAreaElement ||
    event.target instanceof HTMLSelectElement
  ) {
    return;
  }

  // Check for keyboard shortcuts
  const isCtrl = event.ctrlKey || event.metaKey;

  // Handle shortcuts
  if (isCtrl) {
    switch (event.key.toLowerCase()) {
      case 'c':
        emit('copy');
        event.preventDefault();
        break;
      case 'v':
        if (!props.readOnly) {
          emit('paste');
          event.preventDefault();
        }
        break;
      case 'x':
        if (!props.readOnly) {
          emit('cut');
          event.preventDefault();
        }
        break;
      case 'z':
        if (!props.readOnly) {
          emit('undo');
          event.preventDefault();
        }
        break;
      case 'y':
        if (!props.readOnly) {
          emit('redo');
          event.preventDefault();
        }
        break;
      case 's':
        if (!props.readOnly) {
          emit('save');
          event.preventDefault();
        }
        break;
      case 'a':
        emit('selectAll');
        event.preventDefault();
        break;
      case '+':
      case '=': // = is the same key as + without shift
        emit('zoomIn');
        event.preventDefault();
        break;
      case '-':
        emit('zoomOut');
        event.preventDefault();
        break;
      case '0':
        emit('resetView');
        event.preventDefault();
        break;
      case '1':
        emit('fitContent');
        event.preventDefault();
        break;
      case 'r':
        if (!props.readOnly) {
          if (event.shiftKey) {
            emit('rotateCounterclockwise');
          } else {
            emit('rotateClockwise');
          }
          event.preventDefault();
        }
        break;
    }
  } else if (event.key === 'Delete' || event.key === 'Backspace') {
    if (!props.readOnly) {
      emit('delete');
      event.preventDefault();
    }
  } else if (event.key === 'Escape') {
    emit('escape');
    event.preventDefault();
  }
};

// Lifecycle hooks
onMounted(() => {
  // Add keyboard event listener
  window.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  // Remove keyboard event listener
  window.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
.keyboard-shortcuts {
  display: inline-block;
}

.shortcuts-container {
  max-height: 500px;
  overflow-y: auto;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 8px;
}

.shortcut-category {
  background: #fafafa;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e8e8e8;
}

.category-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 4px;
}

.shortcut-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
  font-size: 12px;
}

.shortcut-action {
  color: #555;
  font-weight: 500;
  flex: 1;
  margin-right: 8px;
}

.shortcut-keys {
  display: flex;
  gap: 2px;
  align-items: center;
}

kbd {
  display: inline-block;
  padding: 2px 4px;
  font-size: 10px;
  line-height: 1;
  color: #444d56;
  vertical-align: middle;
  background-color: #fafbfc;
  border: 1px solid #d1d5da;
  border-radius: 2px;
  box-shadow: inset 0 -1px 0 #d1d5da;
  margin: 0 1px;
  min-width: 16px;
  text-align: center;
}

/* Responsive design for smaller screens */
@media (max-width: 1024px) {
  .shortcuts-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .shortcuts-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .shortcut-item {
    font-size: 11px;
  }

  kbd {
    font-size: 9px;
    padding: 1px 3px;
  }
}
</style>
