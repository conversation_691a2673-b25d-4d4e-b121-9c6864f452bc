<template>
  <div class="grid-settings-panel">
    <div class="panel-header">
      <h3>网格设置</h3>
    </div>

    <div class="panel-content">
      <!-- Grid Visibility -->
      <div class="setting-group">
        <div class="setting-label">网格可见性</div>
        <div class="setting-control">
          <a-switch
            v-model:checked="gridVisible"
            @change="updateGridVisibility"
          />
          <span class="setting-hint">{{ gridVisible ? '显示' : '隐藏' }}</span>
        </div>
      </div>

      <!-- Snap to Grid -->
      <div class="setting-group">
        <div class="setting-label">对齐网格</div>
        <div class="setting-control">
          <a-switch
            v-model:checked="snapToGrid"
            @change="updateSnapToGrid"
          />
          <span class="setting-hint">{{ snapToGrid ? '启用' : '禁用' }}</span>
        </div>
      </div>

      <!-- Grid Size -->
      <div class="setting-group">
        <div class="setting-label">网格大小</div>
        <div class="setting-control">
          <a-input-number
            v-model:value="gridSize"
            :min="5"
            :max="100"
            :step="5"
            @change="updateGridSize"
          />
          <span class="setting-hint">像素</span>
        </div>
      </div>

      <!-- Major Grid Size -->
      <div class="setting-group">
        <div class="setting-label">主网格大小</div>
        <div class="setting-control">
          <a-input-number
            v-model:value="majorGridSize"
            :min="gridSize * 2"
            :max="500"
            :step="gridSize"
            @change="updateMajorGridSize"
          />
          <span class="setting-hint">像素</span>
        </div>
      </div>

      <!-- Grid Pattern -->
      <div class="setting-group">
        <div class="setting-label">网格样式</div>
        <div class="setting-control">
          <a-radio-group v-model:value="gridPattern" @change="updateGridPattern">
            <a-radio-button value="lines">线条</a-radio-button>
            <a-radio-button value="dots">点状</a-radio-button>
          </a-radio-group>
        </div>
      </div>

      <!-- Grid Colors -->
      <div class="setting-group">
        <div class="setting-label">次网格颜色</div>
        <div class="setting-control">
          <a-input
            v-model:value="minorColor"
            @change="updateMinorColor"
            class="color-input"
          >
            <template #prefix>
              <div class="color-preview" :style="{ backgroundColor: minorColor }"></div>
            </template>
          </a-input>
        </div>
      </div>

      <div class="setting-group">
        <div class="setting-label">主网格颜色</div>
        <div class="setting-control">
          <a-input
            v-model:value="majorColor"
            @change="updateMajorColor"
            class="color-input"
          >
            <template #prefix>
              <div class="color-preview" :style="{ backgroundColor: majorColor }"></div>
            </template>
          </a-input>
        </div>
      </div>

      <!-- Keyboard Shortcuts -->
      <div class="setting-group shortcuts">
        <div class="setting-label">Keyboard Shortcuts</div>
        <div class="shortcut-list">
          <div class="shortcut-item">
            <span class="shortcut-key">Ctrl+G</span>
            <span class="shortcut-description">Toggle Grid Visibility</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">Ctrl+Shift+G</span>
            <span class="shortcut-description">Toggle Snap to Grid</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">Ctrl+[</span>
            <span class="shortcut-description">Decrease Grid Size</span>
          </div>
          <div class="shortcut-item">
            <span class="shortcut-key">Ctrl+]</span>
            <span class="shortcut-description">Increase Grid Size</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { GridPatternType } from '@/types/diagram';

// Store
const diagramStore = useDiagramStore();

// Reactive state
const gridVisible = ref(false); // Grid disabled by default
const snapToGrid = ref(false); // Snap to grid disabled by default
const gridSize = ref(20);
const majorGridSize = ref(100);
const gridPattern = ref<string>('lines');
const minorColor = ref('#e0e0e0');
const majorColor = ref('#c0c0c0');

// Initialize from current diagram
onMounted(() => {
  if (diagramStore.currentDiagram?.grid) {
    const grid = diagramStore.currentDiagram.grid;
    gridVisible.value = grid.visible;
    snapToGrid.value = grid.snapToGrid;
    gridSize.value = grid.size;
    majorGridSize.value = grid.majorGridSize;
    gridPattern.value = grid.pattern;
    minorColor.value = grid.minorColor;
    majorColor.value = grid.majorColor;
  }
});

// Update methods
const updateGridVisibility = (visible: boolean) => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.updateGridSettings({ visible });
};

const updateSnapToGrid = (snap: boolean) => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.updateGridSettings({ snapToGrid: snap });
};

const updateGridSize = (size: number) => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.updateGridSettings({ size });

  // Ensure major grid size is at least twice the grid size
  if (majorGridSize.value < size * 2) {
    majorGridSize.value = size * 2;
    diagramStore.updateGridSettings({ majorGridSize: size * 2 });
  }
};

const updateMajorGridSize = (size: number) => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.updateGridSettings({ majorGridSize: size });
};

const updateGridPattern = (pattern: string) => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.updateGridSettings({
    pattern: pattern as GridPatternType
  });
};

const updateMinorColor = (event: Event) => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.updateGridSettings({ minorColor: minorColor.value });
};

const updateMajorColor = (event: Event) => {
  if (!diagramStore.currentDiagram) return;
  diagramStore.updateGridSettings({ majorColor: majorColor.value });
};
</script>

<style scoped>
.grid-settings-panel {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 300px;
}

.panel-header {
  padding: 5px 8px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #f5f5f5;
}

.panel-header h3 {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
}

.panel-content {
  padding: 10px;
}

.setting-group {
  margin-bottom: 12px;
}

.setting-label {
  font-size: 12px;
  margin-bottom: 6px;
  color: rgba(0, 0, 0, 0.65);
}

.setting-control {
  display: flex;
  align-items: center;
}

.setting-hint {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}

.color-input {
  width: 120px;
}

.color-preview {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
}

.shortcuts {
  margin-top: 24px;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.shortcut-list {
  margin-top: 8px;
}

.shortcut-item {
  display: flex;
  margin-bottom: 8px;
}

.shortcut-key {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 2px 6px;
  font-size: 12px;
  font-family: monospace;
  min-width: 80px;
}

.shortcut-description {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}
</style>
