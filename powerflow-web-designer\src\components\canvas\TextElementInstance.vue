<template>
  <v-group
    :config="{
      x: textElement.position.x,
      y: textElement.position.y,
      rotation: textElement.rotation,
      draggable: !readOnly && !textElement.locked,
      listening: true,
    }"
    @dragstart="handleDragStart"
    @dragmove="handleDragMove"
    @dragend="handleDragEnd"
    @click="handleClick"
    @dblclick="handleDoubleClick"
    @contextmenu="handleContextMenu"
  >
    <!-- Background rectangle (if background color is set) -->
    <v-rect
      v-if="hasBackground"
      :config="{
        x: 0,
        y: 0,
        width: textBounds.width,
        height: textBounds.height,
        fill: textElement.style.backgroundColor,
        opacity: textElement.style.backgroundOpacity || 1,
        stroke: textElement.style.borderColor,
        strokeWidth: textElement.style.borderWidth || 0,
        cornerRadius: textElement.style.borderRadius || 0,
      }"
    />

    <!-- Text content -->
    <v-text
      :config="{
        x: getPaddingLeft(),
        y: getPaddingTop(),
        text: textElement.content || 'Text',
        fontSize: textElement.style?.fontSize || 14,
        fontFamily: textElement.style?.fontFamily || 'Arial',
        fontStyle: getFontStyle(),
        fill: textElement.style?.color || '#000000',
        align: textElement.style?.textAlign || 'left',
        width: getTextWidth(),
        height: getTextHeight(),
        lineHeight: textElement.style?.lineHeight || 1.2,
        letterSpacing: textElement.style?.letterSpacing || 0,
        wrap: 'word',
        ellipsis: true,
        verticalAlign: 'middle',
      }"
    />

    <!-- Selection indicator -->
    <v-rect
      v-if="isSelected"
      :config="{
        x: -2,
        y: -2,
        width: textBounds.width + 4,
        height: textBounds.height + 4,
        stroke: '#1890ff',
        strokeWidth: 2,
        dash: [5, 5],
        fill: 'transparent',
        listening: false,
      }"
    />

    <!-- Resize handles (when selected) -->
    <template v-if="isSelected && !readOnly">
      <v-circle
        v-for="handle in resizeHandles"
        :key="handle.id"
        :config="{
          x: handle.x,
          y: handle.y,
          radius: 4,
          fill: '#1890ff',
          stroke: '#ffffff',
          strokeWidth: 1,
          draggable: true,
          listening: true,
        }"
        @dragmove="(e) => handleResize(e, handle.id)"
      />
    </template>
  </v-group>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { TextElement, TextAlignment } from '@/types/textElement';
import { getTextElementBounds } from '@/types/textElement';

// Props
interface Props {
  textElement: TextElement;
  isSelected?: boolean;
  readOnly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
  readOnly: false,
});

// Emits
const emit = defineEmits<{
  (e: 'select', id: string, event?: MouseEvent): void;
  (e: 'move', id: string, position: { x: number; y: number }): void;
  (e: 'resize', id: string, size: { width: number; height: number }): void;
  (e: 'edit', id: string): void;
  (e: 'contextmenu', event: MouseEvent): void;
}>();

// State
const isDragging = ref(false);
const dragStartPosition = ref({ x: 0, y: 0 });

// Computed
const textBounds = computed(() => getTextElementBounds(props.textElement));

const hasBackground = computed(() => {
  const style = props.textElement.style;
  return style.backgroundColor && style.backgroundColor !== 'transparent';
});

const resizeHandles = computed(() => {
  if (!props.isSelected || props.readOnly) return [];
  
  const bounds = textBounds.value;
  return [
    { id: 'nw', x: 0, y: 0 },
    { id: 'ne', x: bounds.width, y: 0 },
    { id: 'sw', x: 0, y: bounds.height },
    { id: 'se', x: bounds.width, y: bounds.height },
    { id: 'n', x: bounds.width / 2, y: 0 },
    { id: 's', x: bounds.width / 2, y: bounds.height },
    { id: 'w', x: 0, y: bounds.height / 2 },
    { id: 'e', x: bounds.width, y: bounds.height / 2 },
  ];
});

// Methods
const getPaddingLeft = () => {
  return props.textElement.style?.padding?.left || 4;
};

const getPaddingTop = () => {
  return props.textElement.style?.padding?.top || 4;
};

const getTextWidth = () => {
  const totalWidth = props.textElement.size?.width || 100;
  const paddingLeft = getPaddingLeft();
  const paddingRight = props.textElement.style?.padding?.right || 4;
  return Math.max(0, totalWidth - paddingLeft - paddingRight);
};

const getTextHeight = () => {
  const totalHeight = props.textElement.size?.height || 30;
  const paddingTop = getPaddingTop();
  const paddingBottom = props.textElement.style?.padding?.bottom || 4;
  return Math.max(0, totalHeight - paddingTop - paddingBottom);
};

const getFontStyle = () => {
  const weight = props.textElement.style?.fontWeight || 'normal';
  const style = props.textElement.style?.fontStyle || 'normal';

  if (weight === 'bold' && style === 'italic') {
    return 'bold italic';
  } else if (weight === 'bold') {
    return 'bold';
  } else if (style === 'italic') {
    return 'italic';
  }
  return 'normal';
};

const handleClick = (event: any) => {
  event.cancelBubble = true;
  emit('select', props.textElement.id, event.evt);
};

const handleDoubleClick = (event: any) => {
  event.cancelBubble = true;
  if (!props.readOnly) {
    emit('edit', props.textElement.id);
  }
};

const handleDragStart = (event: any) => {
  if (props.readOnly || props.textElement.locked) return;
  
  isDragging.value = true;
  dragStartPosition.value = {
    x: event.target.x(),
    y: event.target.y(),
  };
};

const handleDragMove = (event: any) => {
  if (!isDragging.value) return;
  
  // Emit move event with new position
  emit('move', props.textElement.id, {
    x: event.target.x(),
    y: event.target.y(),
  });
};

const handleDragEnd = (event: any) => {
  if (!isDragging.value) return;
  
  isDragging.value = false;
  
  // Final position update
  emit('move', props.textElement.id, {
    x: event.target.x(),
    y: event.target.y(),
  });
};

const handleResize = (event: any, handleId: string) => {
  if (props.readOnly || props.textElement.locked) return;
  
  const bounds = textBounds.value;
  const newX = event.target.x();
  const newY = event.target.y();
  
  let newWidth = bounds.width;
  let newHeight = bounds.height;
  
  // Calculate new size based on handle position
  switch (handleId) {
    case 'se':
      newWidth = Math.max(20, newX);
      newHeight = Math.max(20, newY);
      break;
    case 'sw':
      newWidth = Math.max(20, bounds.width - newX);
      newHeight = Math.max(20, newY);
      break;
    case 'ne':
      newWidth = Math.max(20, newX);
      newHeight = Math.max(20, bounds.height - newY);
      break;
    case 'nw':
      newWidth = Math.max(20, bounds.width - newX);
      newHeight = Math.max(20, bounds.height - newY);
      break;
    case 'e':
      newWidth = Math.max(20, newX);
      break;
    case 'w':
      newWidth = Math.max(20, bounds.width - newX);
      break;
    case 's':
      newHeight = Math.max(20, newY);
      break;
    case 'n':
      newHeight = Math.max(20, bounds.height - newY);
      break;
  }
  
  emit('resize', props.textElement.id, {
    width: newWidth,
    height: newHeight,
  });
};

const handleContextMenu = (event: any) => {
  event.cancelBubble = true;
  emit('contextmenu', event.evt);
};
</script>

<style scoped>
/* Component-specific styles if needed */
</style>
