import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import Home from '../views/Home.vue';
import Editor from '../views/Editor.vue';
import Viewer from '../views/Viewer.vue';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
  },
  {
    path: '/editor',
    name: 'Editor',
    component: Editor,
  },
  {
    path: '/viewer',
    name: 'Viewer',
    component: Viewer,
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
