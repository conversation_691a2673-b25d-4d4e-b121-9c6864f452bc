<template>
  <div class="selection-manager">
    <!-- Selection rectangle -->
    <v-rect
      v-if="selectionActive"
      :config="{
        x: selectionRect.x,
        y: selectionRect.y,
        width: selectionRect.width,
        height: selectionRect.height,
        fill: 'rgba(24, 144, 255, 0.1)',
        stroke: '#1890ff',
        strokeWidth: 1,
        dash: [5, 5],
      }"
    />

    <!-- Selection handles for multi-select -->
    <v-group v-if="hasMultipleSelection">
      <v-rect
        :config="{
          x: selectionBounds.x,
          y: selectionBounds.y,
          width: selectionBounds.width,
          height: selectionBounds.height,
          stroke: '#1890ff',
          strokeWidth: 1,
          dash: [5, 5],
          fill: 'transparent',
        }"
      />

      <!-- Selection handles -->
      <v-circle
        v-for="handle in selectionHandles"
        :key="handle.id"
        :config="{
          x: handle.x,
          y: handle.y,
          radius: 6,
          fill: '#fff',
          stroke: '#1890ff',
          strokeWidth: 1,
          draggable: true,
        }"
        @dragmove="(e) => handleHandleDragMove(handle.id, e)"
      />
    </v-group>

    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { Position, Size } from '@/types/symbol';

// Store
const diagramStore = useDiagramStore();

// Props
const props = defineProps<{
  stageRef: any;
}>();

// State
const selectionActive = ref(false);
const selectionStart = ref<Position>({ x: 0, y: 0 });
const selectionRect = ref<Position & Size>({ x: 0, y: 0, width: 0, height: 0 });
const selectionBounds = ref<Position & Size>({ x: 0, y: 0, width: 0, height: 0 });

// Computed
const hasSelection = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  if (!diagramStore.currentDiagram.selectedSymbolIds ||
      !diagramStore.currentDiagram.selectedConnectionIds) return false;

  return (
    diagramStore.currentDiagram.selectedSymbolIds.length > 0 ||
    diagramStore.currentDiagram.selectedConnectionIds.length > 0
  );
});

const hasMultipleSelection = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  if (!diagramStore.currentDiagram.selectedSymbolIds) return false;

  return diagramStore.currentDiagram.selectedSymbolIds.length > 1;
});

const selectedSymbols = computed(() => {
  if (!diagramStore.currentDiagram) return [];
  if (!diagramStore.currentDiagram.selectedSymbolIds) return [];

  return diagramStore.currentDiagram.selectedSymbolIds.map(id =>
    diagramStore.currentDiagram?.symbolInstances[id]
  ).filter(Boolean);
});

const selectionHandles = computed(() => {
  if (!hasMultipleSelection.value) return [];

  const bounds = selectionBounds.value;

  return [
    { id: 'top-left', x: bounds.x, y: bounds.y },
    { id: 'top-right', x: bounds.x + bounds.width, y: bounds.y },
    { id: 'bottom-left', x: bounds.x, y: bounds.y + bounds.height },
    { id: 'bottom-right', x: bounds.x + bounds.width, y: bounds.y + bounds.height },
  ];
});

// Methods
const startSelection = (position: Position) => {
  selectionActive.value = true;
  selectionStart.value = { ...position };
  selectionRect.value = {
    x: position.x,
    y: position.y,
    width: 0,
    height: 0,
  };
};

const updateSelection = (position: Position) => {
  if (!selectionActive.value) return;

  const x = Math.min(selectionStart.value.x, position.x);
  const y = Math.min(selectionStart.value.y, position.y);
  const width = Math.abs(position.x - selectionStart.value.x);
  const height = Math.abs(position.y - selectionStart.value.y);

  selectionRect.value = { x, y, width, height };
};

const endSelection = () => {
  if (!selectionActive.value) return;

  // Find symbols within the selection rectangle
  if (diagramStore.currentDiagram) {
    const selectedIds: string[] = [];

    // Check each symbol
    Object.values(diagramStore.currentDiagram.symbolInstances).forEach(symbol => {
      // Check if the symbol is within the selection rectangle
      if (
        symbol.position.x >= selectionRect.value.x &&
        symbol.position.x <= selectionRect.value.x + selectionRect.value.width &&
        symbol.position.y >= selectionRect.value.y &&
        symbol.position.y <= selectionRect.value.y + selectionRect.value.height
      ) {
        selectedIds.push(symbol.id);
      }
    });

    // Update selection
    diagramStore.currentDiagram.selectedSymbolIds = selectedIds;
    diagramStore.currentDiagram.selectedConnectionIds = [];
  }

  // Reset selection state
  selectionActive.value = false;
};

const cancelSelection = () => {
  selectionActive.value = false;
};

const updateSelectionBounds = () => {
  if (!hasMultipleSelection.value) return;

  // Calculate bounds of selected symbols
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  selectedSymbols.value.forEach(symbol => {
    if (!symbol) return;

    minX = Math.min(minX, symbol.position.x);
    minY = Math.min(minY, symbol.position.y);
    maxX = Math.max(maxX, symbol.position.x);
    maxY = Math.max(maxY, symbol.position.y);
  });

  selectionBounds.value = {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY,
  };
};

const handleHandleDragMove = (handleId: string, event: any) => {
  if (!hasMultipleSelection.value || !diagramStore.currentDiagram) return;

  // Get the new position
  const newX = event.target.x();
  const newY = event.target.y();

  // Calculate the scale factor based on the handle position
  let scaleX = 1;
  let scaleY = 1;

  const bounds = selectionBounds.value;
  const originalWidth = bounds.width;
  const originalHeight = bounds.height;

  if (handleId === 'top-left') {
    scaleX = (bounds.x + bounds.width - newX) / bounds.width;
    scaleY = (bounds.y + bounds.height - newY) / bounds.height;
  } else if (handleId === 'top-right') {
    scaleX = (newX - bounds.x) / bounds.width;
    scaleY = (bounds.y + bounds.height - newY) / bounds.height;
  } else if (handleId === 'bottom-left') {
    scaleX = (bounds.x + bounds.width - newX) / bounds.width;
    scaleY = (newY - bounds.y) / bounds.height;
  } else if (handleId === 'bottom-right') {
    scaleX = (newX - bounds.x) / bounds.width;
    scaleY = (newY - bounds.y) / bounds.height;
  }

  // Apply the scale to all selected symbols
  selectedSymbols.value.forEach(symbol => {
    if (!symbol) return;

    // Calculate the relative position from the selection bounds origin
    const relX = symbol.position.x - bounds.x;
    const relY = symbol.position.y - bounds.y;

    // Apply the scale
    symbol.position.x = bounds.x + relX * scaleX;
    symbol.position.y = bounds.y + relY * scaleY;

    // Update the symbol scale
    symbol.scale = symbol.scale * Math.min(scaleX, scaleY);
  });

  // Mark the diagram as modified
  diagramStore.modified = true;

  // Update the selection bounds
  updateSelectionBounds();
};

// Watch for changes in selection
watch(() => diagramStore.currentDiagram?.selectedSymbolIds, () => {
  updateSelectionBounds();
}, { deep: true });

// Expose methods to parent component
defineExpose({
  startSelection,
  updateSelection,
  endSelection,
  cancelSelection,
});
</script>

<style scoped>
.selection-manager {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
</style>
