# PowerFlow Web Designer Bug Tracking

This document tracks bugs and issues found during testing of the PowerFlow Web Designer application.

## Bug Severity Levels

- **Critical**: Prevents core functionality from working, causes data loss, or crashes the application
- **High**: Significantly impacts usability but has workarounds
- **Medium**: Affects functionality but doesn't prevent core tasks
- **Low**: Minor visual or non-functional issues

## Open Issues

| ID | Component | Description | Severity | Status | Assigned To |
|----|-----------|-------------|----------|--------|-------------|
| B001 | DiagramCanvas | Canvas doesn't resize properly when window is resized | Medium | In Progress | |
| B002 | ConnectionLine | Bezier curve connections don't render correctly | High | In Progress | |
| B003 | SymbolInstance | Symbol rotation doesn't update connection points | High | Open | |
| B004 | DiagramStore | Undo/redo stack can cause memory issues with large diagrams | Medium | Open | |
| B005 | PropertyPanel | Changes to symbol properties don't always update the diagram | Medium | Fixed | |
| B008 | ConnectionCreation | Connection waypoints don't snap to grid properly | Medium | Open | |
| B009 | LayerManagement | Moving symbols between layers causes rendering issues | Medium | Open | |
| B010 | DiagramStore | Pinia store integration issues with mock testing | Low | Open | |

## Fixed Issues

| ID | Component | Description | Severity | Fixed In | Fixed By |
|----|-----------|-------------|----------|----------|----------|
| B005 | PropertyPanel | Changes to symbol properties don't always update the diagram | Medium | v0.1.2 | |
| B006 | DiagramRenderer | Selection doesn't clear when clicking on empty canvas | Medium | v0.1.1 | |
| B007 | SymbolLibraryPanel | Dragging symbols sometimes creates duplicates | Low | v0.1.1 | |

## Performance Issues

| ID | Component | Description | Severity | Status | Assigned To |
|----|-----------|-------------|----------|--------|-------------|
| P001 | DiagramRenderer | Rendering slows down with 100+ symbols | High | In Progress | |
| P002 | DiagramStore | JSON serialization is slow for large diagrams | Medium | Open | |
| P003 | ConnectionLine | Connection line rendering is inefficient | Medium | In Progress | |
| P004 | DiagramCanvas | Canvas panning becomes laggy with large diagrams | High | Open | |
| P005 | DiagramStore | Memory usage grows significantly with diagram size | High | Open | |

## Feature Requests

| ID | Component | Description | Priority | Status |
|----|-----------|-------------|----------|--------|
| F001 | DiagramCanvas | Add grid snapping toggle in UI | Medium | Planned |
| F002 | ConnectionLine | Add more connection line styles | Low | Planned |
| F003 | SymbolInstance | Add custom property editor | High | Planned |

## Testing Notes

### Integration Testing Issues

- Connection creation sometimes fails when symbols are far apart
- Layer visibility toggling doesn't always update the view immediately
- Selection of overlapping symbols is inconsistent
- Pinia store integration issues with mock testing (see B010)
- Connection waypoints don't snap to grid properly (see B008)
- Moving symbols between layers causes rendering issues (see B009)

### Performance Testing Issues

- Memory usage grows significantly with diagram size (see P005)
- Undo/redo operations become slow with complex diagrams
- Canvas panning becomes laggy with 100+ symbols (see P004)
- Virtualization needed for large diagrams

### Unit Testing Issues

- Mock components needed for Vue Konva components
- Test isolation issues with Pinia store
- Difficulty testing drag and drop functionality

## Bug Reporting Template

When reporting a new bug, please use the following template:

```
### Bug Description
[Detailed description of the bug]

### Steps to Reproduce
1. [First step]
2. [Second step]
3. [And so on...]

### Expected Behavior
[What you expected to happen]

### Actual Behavior
[What actually happened]

### Environment
- Browser: [e.g. Chrome 98.0.4758.102]
- OS: [e.g. Windows 10]
- Screen Resolution: [e.g. 1920x1080]

### Screenshots
[If applicable, add screenshots to help explain your problem]

### Additional Context
[Any other context about the problem here]
```

## Recently Added Bugs

### B008: Connection waypoints don't snap to grid properly

**Description**: When creating or editing polyline connections, waypoints don't consistently snap to the grid even when grid snapping is enabled.

**Steps to Reproduce**:
1. Create a new diagram with grid snapping enabled
2. Add two symbols to the canvas
3. Create a polyline connection between them
4. Add a waypoint to the connection
5. Try to move the waypoint

**Expected Behavior**: Waypoint should snap to the nearest grid point when moved.

**Actual Behavior**: Waypoint sometimes snaps to grid, but often ends up between grid points.

**Severity**: Medium

### B009: Moving symbols between layers causes rendering issues

**Description**: When moving symbols between layers, sometimes the symbol disappears or appears in both layers simultaneously until the canvas is refreshed.

**Steps to Reproduce**:
1. Create a diagram with at least two layers
2. Add a symbol to the first layer
3. Move the symbol to the second layer using the layer panel

**Expected Behavior**: Symbol should disappear from the first layer and appear in the second layer immediately.

**Actual Behavior**: Symbol sometimes remains visible in both layers or disappears completely until the canvas is refreshed.

**Severity**: Medium

### B010: Pinia store integration issues with mock testing

**Description**: Integration tests using Pinia store mocks fail due to circular dependencies and initialization issues.

**Steps to Reproduce**:
1. Create an integration test that uses the Pinia store
2. Mock the store methods and state
3. Run the test

**Expected Behavior**: Tests should run successfully with mocked store.

**Actual Behavior**: Tests fail with errors related to store initialization and circular dependencies.

**Severity**: Low (affects testing only)
