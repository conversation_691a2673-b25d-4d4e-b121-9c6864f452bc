<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Elements Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PowerFlow Web Designer - Text Elements System Test</h1>
        
        <div class="test-section">
            <div class="test-title">1. Text Element Type Definition Test</div>
            <div id="type-test-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. Text Element Creation Test</div>
            <div id="creation-test-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. Text Element Utility Functions Test</div>
            <div id="utility-test-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. Text Element Integration Test</div>
            <div id="integration-test-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">5. Summary</div>
            <div id="summary" class="test-result"></div>
        </div>
    </div>

    <script type="module">
        // Test results
        let testResults = [];

        function addTestResult(testName, success, message) {
            testResults.push({ testName, success, message });
            console.log(`${testName}: ${success ? 'PASS' : 'FAIL'} - ${message}`);
        }

        function displayResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = `test-result ${success ? 'success' : 'error'}`;
            element.innerHTML = `<strong>${success ? '✓ PASS' : '✗ FAIL'}</strong>: ${message}`;
        }

        // Test 1: Type Definition Test
        try {
            // Simulate text element type structure
            const textElementType = {
                id: 'string',
                content: 'string',
                position: { x: 'number', y: 'number' },
                size: { width: 'number', height: 'number' },
                style: {
                    fontSize: 'number',
                    fontFamily: 'string',
                    color: 'string',
                    textAlign: 'string',
                    fontWeight: 'string',
                    fontStyle: 'string',
                    textDecoration: 'string'
                },
                rotation: 'number',
                visible: 'boolean',
                locked: 'boolean'
            };

            addTestResult('Type Definition', true, 'Text element type structure is well-defined');
            displayResult('type-test-result', true, 'Text element type structure is properly defined with all required properties');
        } catch (error) {
            addTestResult('Type Definition', false, error.message);
            displayResult('type-test-result', false, `Type definition error: ${error.message}`);
        }

        // Test 2: Text Element Creation Test
        try {
            // Simulate text element creation
            function createTextElement(id, content, position, style = {}) {
                return {
                    id,
                    content,
                    position,
                    size: { width: 100, height: 30 },
                    style: {
                        fontSize: 14,
                        fontFamily: 'Arial',
                        color: '#000000',
                        textAlign: 'left',
                        fontWeight: 'normal',
                        fontStyle: 'normal',
                        textDecoration: 'none',
                        ...style
                    },
                    rotation: 0,
                    visible: true,
                    locked: false
                };
            }

            const testTextElement = createTextElement(
                'test-text-1',
                'Hello World',
                { x: 100, y: 50 },
                { fontSize: 16, color: '#ff0000' }
            );

            if (testTextElement.id === 'test-text-1' && 
                testTextElement.content === 'Hello World' &&
                testTextElement.style.fontSize === 16) {
                addTestResult('Text Element Creation', true, 'Text element created successfully');
                displayResult('creation-test-result', true, 'Text element creation function works correctly with proper defaults and style overrides');
            } else {
                throw new Error('Text element properties not set correctly');
            }
        } catch (error) {
            addTestResult('Text Element Creation', false, error.message);
            displayResult('creation-test-result', false, `Creation error: ${error.message}`);
        }

        // Test 3: Utility Functions Test
        try {
            // Simulate utility functions
            function getTextElementBounds(textElement) {
                return {
                    x: textElement.position.x,
                    y: textElement.position.y,
                    width: textElement.size.width,
                    height: textElement.size.height
                };
            }

            function addTextElement(diagram, textElement, layerId) {
                const updatedDiagram = { ...diagram };
                updatedDiagram.textElements = { ...diagram.textElements };
                updatedDiagram.textElements[textElement.id] = textElement;

                // Add to layer
                if (layerId && updatedDiagram.layers) {
                    const layer = updatedDiagram.layers.find(l => l.id === layerId);
                    if (layer) {
                        if (!layer.textElementIds) layer.textElementIds = [];
                        layer.textElementIds.push(textElement.id);
                    }
                }

                return updatedDiagram;
            }

            // Test the functions
            const testElement = {
                id: 'test-1',
                content: 'Test',
                position: { x: 10, y: 20 },
                size: { width: 80, height: 25 }
            };

            const bounds = getTextElementBounds(testElement);
            if (bounds.x === 10 && bounds.y === 20 && bounds.width === 80 && bounds.height === 25) {
                addTestResult('Utility Functions', true, 'Utility functions work correctly');
                displayResult('utility-test-result', true, 'Text element utility functions (bounds calculation, diagram integration) work as expected');
            } else {
                throw new Error('Utility function results incorrect');
            }
        } catch (error) {
            addTestResult('Utility Functions', false, error.message);
            displayResult('utility-test-result', false, `Utility functions error: ${error.message}`);
        }

        // Test 4: Integration Test
        try {
            // Simulate integration with diagram system
            const mockDiagram = {
                id: 'test-diagram',
                textElements: {},
                layers: [
                    { id: 'layer-1', name: 'Layer 1', textElementIds: [] }
                ],
                selectedTextElementIds: []
            };

            // Test adding text element to diagram
            const newTextElement = {
                id: 'integration-test-1',
                content: 'Integration Test',
                position: { x: 50, y: 75 },
                size: { width: 120, height: 30 }
            };

            // Simulate adding to diagram
            mockDiagram.textElements[newTextElement.id] = newTextElement;
            mockDiagram.layers[0].textElementIds.push(newTextElement.id);
            mockDiagram.selectedTextElementIds.push(newTextElement.id);

            if (mockDiagram.textElements['integration-test-1'] &&
                mockDiagram.layers[0].textElementIds.includes('integration-test-1') &&
                mockDiagram.selectedTextElementIds.includes('integration-test-1')) {
                addTestResult('Integration', true, 'Text elements integrate properly with diagram system');
                displayResult('integration-test-result', true, 'Text elements successfully integrate with diagram structure, layers, and selection system');
            } else {
                throw new Error('Integration test failed');
            }
        } catch (error) {
            addTestResult('Integration', false, error.message);
            displayResult('integration-test-result', false, `Integration error: ${error.message}`);
        }

        // Summary
        const passedTests = testResults.filter(r => r.success).length;
        const totalTests = testResults.length;
        const allPassed = passedTests === totalTests;

        displayResult('summary', allPassed, 
            `${passedTests}/${totalTests} tests passed. ${allPassed ? 'All text element system components are working correctly!' : 'Some issues need to be addressed.'}`
        );

        // Log detailed results
        console.log('\n=== TEXT ELEMENTS SYSTEM TEST RESULTS ===');
        testResults.forEach(result => {
            console.log(`${result.testName}: ${result.success ? 'PASS' : 'FAIL'} - ${result.message}`);
        });
        console.log(`\nOverall: ${passedTests}/${totalTests} tests passed`);
    </script>
</body>
</html>
