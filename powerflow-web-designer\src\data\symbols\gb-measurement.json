[{"id": "ammeter", "category": "measurement", "name": "电流表", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"55\" font-size=\"20\" text-anchor=\"middle\" fill=\"currentColor\">A</text><line x1=\"0\" y1=\"50\" x2=\"20\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "in", "position": {"x": 0, "y": 50}, "type": "input"}, {"id": "out", "position": {"x": 100, "y": 50}, "type": "output"}], "bindingSlots": [{"id": "current", "name": "电流", "dataType": "number", "defaultValue": 0, "description": "测量电流值 (A)"}, {"id": "range", "name": "量程", "dataType": "string", "defaultValue": "0-100A", "description": "电流表量程"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "voltmeter", "category": "measurement", "name": "电压表", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"55\" font-size=\"20\" text-anchor=\"middle\" fill=\"currentColor\">V</text><line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"0\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"80\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "positive", "position": {"x": 50, "y": 0}, "type": "input", "label": "正极"}, {"id": "negative", "position": {"x": 50, "y": 100}, "type": "input", "label": "负极"}], "bindingSlots": [{"id": "voltage", "name": "电压", "dataType": "number", "defaultValue": 0, "description": "测量电压值 (V)"}, {"id": "range", "name": "量程", "dataType": "string", "defaultValue": "0-500V", "description": "电压表量程"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "power-factor-meter", "category": "measurement", "name": "功率因数表", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"45\" font-size=\"12\" text-anchor=\"middle\" fill=\"currentColor\">cos</text><text x=\"50\" y=\"60\" font-size=\"12\" text-anchor=\"middle\" fill=\"currentColor\">φ</text><line x1=\"0\" y1=\"50\" x2=\"20\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"0\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "current_in", "position": {"x": 0, "y": 50}, "type": "input", "label": "电流输入"}, {"id": "current_out", "position": {"x": 100, "y": 50}, "type": "output", "label": "电流输出"}, {"id": "voltage", "position": {"x": 50, "y": 0}, "type": "input", "label": "电压输入"}], "bindingSlots": [{"id": "power_factor", "name": "功率因数", "dataType": "number", "defaultValue": 1.0, "description": "功率因数值"}, {"id": "phase_angle", "name": "相位角", "dataType": "number", "defaultValue": 0, "description": "相位角 (度)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "energy-meter", "category": "measurement", "name": "电能表", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"20\" y=\"20\" width=\"60\" height=\"60\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"40\" font-size=\"12\" text-anchor=\"middle\" fill=\"currentColor\">kWh</text><circle cx=\"35\" cy=\"55\" r=\"8\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><circle cx=\"65\" cy=\"55\" r=\"8\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"0\" y1=\"30\" x2=\"20\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"30\" x2=\"100\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"70\" x2=\"20\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"70\" x2=\"100\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "line_in", "position": {"x": 0, "y": 30}, "type": "input", "label": "火线输入"}, {"id": "line_out", "position": {"x": 100, "y": 30}, "type": "output", "label": "火线输出"}, {"id": "neutral_in", "position": {"x": 0, "y": 70}, "type": "input", "label": "零线输入"}, {"id": "neutral_out", "position": {"x": 100, "y": 70}, "type": "output", "label": "零线输出"}], "bindingSlots": [{"id": "energy", "name": "电能", "dataType": "number", "defaultValue": 0, "description": "累计电能 (kWh)"}, {"id": "power", "name": "功率", "dataType": "number", "defaultValue": 0, "description": "瞬时功率 (kW)"}, {"id": "voltage", "name": "电压", "dataType": "number", "defaultValue": 0, "description": "电压 (V)"}, {"id": "current", "name": "电流", "dataType": "number", "defaultValue": 0, "description": "电流 (A)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "frequency-meter", "category": "measurement", "name": "频率表", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"45\" font-size=\"16\" text-anchor=\"middle\" fill=\"currentColor\">Hz</text><path d=\"M 35 60 Q 50 55 65 60\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"0\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"80\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "positive", "position": {"x": 50, "y": 0}, "type": "input", "label": "正极"}, {"id": "negative", "position": {"x": 50, "y": 100}, "type": "input", "label": "负极"}], "bindingSlots": [{"id": "frequency", "name": "频率", "dataType": "number", "defaultValue": 50, "description": "测量频率值 (Hz)"}, {"id": "range", "name": "量程", "dataType": "string", "defaultValue": "45-55Hz", "description": "频率表量程"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}]