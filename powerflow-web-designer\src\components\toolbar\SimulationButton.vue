<template>
  <div class="simulation-button">
    <a-tooltip title="模拟设置">
      <a-button
        class="simulation-icon-button"
        @click="showSimulationModal"
      >
        <template #icon><experiment-outlined /></template>
      </a-button>
    </a-tooltip>

    <!-- 模拟设置弹窗 -->
    <a-modal
      v-model:visible="isModalVisible"
      title="模拟设置"
      @ok="saveSettings"
      :width="500"
    >
      <a-form :model="simulationSettings" layout="vertical" size="small">
        <a-form-item label="模拟模式">
          <a-select v-model:value="simulationSettings.mode">
            <a-select-option value="steady-state">稳态</a-select-option>
            <a-select-option value="transient">瞬态</a-select-option>
            <a-select-option value="fault">故障分析</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="时间步长 (毫秒)">
          <a-input-number
            v-model:value="simulationSettings.timeStep"
            :min="10"
            :max="1000"
            :step="10"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="最大时间 (秒)">
          <a-input-number
            v-model:value="simulationSettings.maxTime"
            :min="1"
            :max="3600"
            :step="1"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="自动停止">
          <a-switch v-model:checked="simulationSettings.autoStop" />
        </a-form-item>

        <a-form-item label="显示动画">
          <a-switch v-model:checked="simulationSettings.showAnimation" />
        </a-form-item>
      </a-form>

      <div class="simulation-controls">
        <a-space>
          <a-tooltip title="开始模拟">
            <a-button
              type="primary"
              :disabled="isSimulationRunning || !hasCurrentDiagram"
              @click="startSimulation"
            >
              <template #icon><play-circle-outlined /></template>
            </a-button>
          </a-tooltip>

          <a-tooltip title="暂停模拟">
            <a-button
              :disabled="!isSimulationRunning || !hasCurrentDiagram"
              @click="pauseSimulation"
            >
              <template #icon><pause-circle-outlined /></template>
            </a-button>
          </a-tooltip>

          <a-tooltip title="停止模拟">
            <a-button
              :disabled="!isSimulationRunning && !isSimulationPaused || !hasCurrentDiagram"
              @click="stopSimulation"
            >
              <template #icon><stop-outlined /></template>
            </a-button>
          </a-tooltip>

          <a-tooltip title="单步前进">
            <a-button
              :disabled="isSimulationRunning || !hasCurrentDiagram"
              @click="stepSimulation"
            >
              <template #icon><step-forward-outlined /></template>
            </a-button>
          </a-tooltip>
        </a-space>
      </div>
    </a-modal>

    <!-- 模拟状态悬浮提示 -->
    <a-popover
      v-if="isSimulationRunning || isSimulationPaused"
      placement="bottomRight"
      trigger="hover"
      :visible="simulationInfoVisible"
      @visibleChange="handleSimulationInfoVisibleChange"
    >
      <template #content>
        <div class="simulation-info">
          <div class="info-item">
            <span class="label">模拟模式:</span>
            <span class="value">{{ getModeName(simulationSettings.mode) }}</span>
          </div>
          <div class="info-item">
            <span class="label">模拟时间:</span>
            <span class="value">{{ simulationTime.toFixed(1) }}s</span>
          </div>
          <div class="info-item">
            <span class="label">模拟速度:</span>
            <span class="value">{{ simulationSpeed }}x</span>
          </div>
          <div class="info-item">
            <span class="label">状态:</span>
            <span class="value">{{ isSimulationPaused ? '已暂停' : '运行中' }}</span>
          </div>
        </div>
      </template>
      <a-badge :count="isSimulationRunning ? '•' : null" :dot="true" :offset="[-5, 5]" color="#52c41a">
        <a-button
          class="simulation-icon-button"
          :class="{ 'simulation-active': isSimulationRunning, 'simulation-paused': isSimulationPaused }"
        >
          <template #icon><experiment-outlined /></template>
        </a-button>
      </a-badge>
    </a-popover>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import {
  ExperimentOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  StepForwardOutlined
} from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';

// Store
const diagramStore = useDiagramStore();

// State
const isModalVisible = ref(false);
const simulationInfoVisible = ref(false);
const isSimulationRunning = ref(false);
const isSimulationPaused = ref(false);
const simulationTime = ref(0);
const simulationSpeed = ref(1);
const simulationSettings = ref({
  mode: 'steady-state',
  timeStep: 100,
  maxTime: 60,
  autoStop: true,
  showAnimation: true,
});

// Computed
const hasCurrentDiagram = computed(() => {
  return !!diagramStore.currentDiagram;
});

// Methods
const showSimulationModal = () => {
  isModalVisible.value = true;
};

const saveSettings = () => {
  isModalVisible.value = false;
};

const startSimulation = () => {
  isSimulationRunning.value = true;
  isSimulationPaused.value = false;
  simulationTime.value = 0;
  isModalVisible.value = false;
  simulationInfoVisible.value = true;
};

const pauseSimulation = () => {
  isSimulationRunning.value = false;
  isSimulationPaused.value = true;
};

const stopSimulation = () => {
  isSimulationRunning.value = false;
  isSimulationPaused.value = false;
  simulationTime.value = 0;
};

const stepSimulation = () => {
  simulationTime.value += simulationSettings.value.timeStep / 1000;
};

const handleSimulationInfoVisibleChange = (visible: boolean) => {
  simulationInfoVisible.value = visible;
};

const getModeName = (mode: string) => {
  switch (mode) {
    case 'steady-state':
      return '稳态';
    case 'transient':
      return '瞬态';
    case 'fault':
      return '故障分析';
    default:
      return mode;
  }
};
</script>

<style scoped>
.simulation-button {
  display: inline-block;
  position: relative;
}

.simulation-icon-button {
  color: white;
  border-color: rgba(255, 255, 255, 0.7);
  background-color: rgba(255, 255, 255, 0.15);
  height: 28px;
  padding: 0 8px;
}

.simulation-icon-button:hover {
  color: white;
  border-color: white;
  background-color: rgba(255, 255, 255, 0.25);
}

.simulation-controls {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.simulation-active {
  color: #52c41a !important;
  border-color: #52c41a !important;
  background-color: rgba(82, 196, 26, 0.2) !important;
}

.simulation-paused {
  color: #faad14 !important;
  border-color: #faad14 !important;
  background-color: rgba(250, 173, 20, 0.2) !important;
}

.simulation-info {
  width: 200px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-item .label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.65);
}

.info-item .value {
  color: rgba(0, 0, 0, 0.85);
}
</style>
