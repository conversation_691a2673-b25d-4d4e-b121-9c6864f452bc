import { test, expect } from '@playwright/test';

/**
 * Level 1: Basic Homepage Tests
 * These tests verify that the homepage loads correctly.
 */

test.describe('Homepage', () => {
  test('should load the homepage correctly', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');

    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');

    // Take a screenshot
    await page.screenshot({ path: './test-results/homepage.png' });

    // Log the current URL
    console.log(`Current URL: ${page.url()}`);

    // Log the page title
    const title = await page.title();
    console.log(`Page title: ${title}`);

    // Check that the page loads without errors
    await expect(page).toBeTruthy();
  });

  test('should display main UI elements', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');

    // Check that the app container is visible
    await expect(page.locator('#app')).toBeVisible();

    // Check for any visible elements in the app
    const visibleElements = await page.locator('#app *:visible').count();
    console.log(`Found ${visibleElements} visible elements on the homepage`);

    // Basic assertion - just check that there are some visible elements
    expect(visibleElements).toBeGreaterThan(0);
  });

  test('should have DOM structure', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');

    // Check that there is a basic DOM structure
    const bodyElements = await page.locator('body *').count();
    console.log(`Found ${bodyElements} elements in the DOM`);

    // Basic assertion - just check that there are some elements in the DOM
    expect(bodyElements).toBeGreaterThan(0);
  });
});
