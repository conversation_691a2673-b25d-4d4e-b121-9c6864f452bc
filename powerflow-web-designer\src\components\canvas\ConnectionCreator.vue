<template>
  <div class="connection-creator">
    <!-- Temporary connection line with orthogonal preview -->
    <v-line
      v-if="isCreatingConnection"
      :config="{
        points: getOrthogonalPreviewPoints(),
        stroke: '#1890ff',
        strokeWidth: 2,
        dash: [5, 5],
      }"
    />

    <!-- Connection points snapping indicators -->
    <v-circle
      v-for="point in snapPoints"
      :key="`snap-${point.symbolId}-${point.pointId}`"
      :config="{
        x: point.position.x,
        y: point.position.y,
        radius: 8,
        fill: 'rgba(24, 144, 255, 0.2)',
        stroke: '#1890ff',
        strokeWidth: 1,
        visible: isCreatingConnection && isPointInRange(point.position),
      }"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { Position } from '@/types/symbol';
import { ConnectionType, ConnectionLineType, defaultRoutingOptions } from '@/types/connection';
import { getConnectionPointPosition } from '@/types/symbolInstance';
import { getSymbolDefinition } from '@/utils/symbolLibrary';
import {
  getBusbarConnectionPoints,
  getBusbarConnectionPointPosition,
  isBusbarSymbol
} from '@/utils/busbarUtils';

// Store
const diagramStore = useDiagramStore();

// Props
const props = defineProps<{
  stageRef: any;
}>();

// State
const isCreatingConnection = ref(false);
const tempConnection = ref({
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
  sourceSymbolId: '',
  sourcePointId: '',
});
const mousePosition = ref<Position>({ x: 0, y: 0 });
const snapDistance = ref(50); // Increased snap distance for better connection detection

// Computed
const snapPoints = computed(() => {
  if (!diagramStore.currentDiagram) return [];

  const points: Array<{
    symbolId: string;
    pointId: string;
    position: Position;
  }> = [];

  console.log('Computing snap points...');
  console.log('Current diagram symbols:', Object.keys(diagramStore.currentDiagram.symbolInstances));
  console.log('Source symbol ID:', tempConnection.value.sourceSymbolId);

  // Get all connection points from all symbols
  Object.entries(diagramStore.currentDiagram.symbolInstances).forEach(([symbolId, symbol]) => {
    // Skip the source symbol
    if (symbolId === tempConnection.value.sourceSymbolId) return;

    const definition = getSymbolDefinition(symbol.definitionId);
    if (!definition) {
      console.warn(`No definition found for symbol ${symbolId} with definitionId ${symbol.definitionId}`);
      return;
    }

    console.log(`Processing symbol ${symbolId} (${definition.id})`);

    // Get connection points (dynamic for busbar symbols)
    const connectionPoints = isBusbarSymbol(definition)
      ? getBusbarConnectionPoints(symbol, definition)
      : definition.connectionPoints;

    console.log(`Symbol ${symbolId} has ${connectionPoints.length} connection points`);

    connectionPoints.forEach(point => {
      const position = isBusbarSymbol(definition)
        ? getBusbarConnectionPointPosition(symbol, definition, point.id)
        : getConnectionPointPosition(symbol, definition, point.id);

      if (position) {
        points.push({
          symbolId,
          pointId: point.id,
          position,
        });
        console.log(`Added snap point: ${symbolId}-${point.id} at (${position.x}, ${position.y})`);
      } else {
        console.warn(`Failed to get position for connection point ${point.id} on symbol ${symbolId}`);
      }
    });
  });

  console.log(`Total snap points: ${points.length}`);
  return points;
});

// Create orthogonal preview points for temporary connection
const getOrthogonalPreviewPoints = () => {
  if (!isCreatingConnection.value) {
    return [];
  }

  const start = { x: tempConnection.value.startX, y: tempConnection.value.startY };
  const end = { x: tempConnection.value.endX, y: tempConnection.value.endY };

  // Create orthogonal path preview (L-shaped)
  const dx = end.x - start.x;
  const dy = end.y - start.y;

  // Check if points are already aligned orthogonally
  if (Math.abs(dx) < 5) {
    // Vertically aligned - direct vertical connection
    return [start.x, start.y, end.x, end.y];
  }

  if (Math.abs(dy) < 5) {
    // Horizontally aligned - direct horizontal connection
    return [start.x, start.y, end.x, end.y];
  }

  // Create L-shaped path based on larger distance
  if (Math.abs(dx) > Math.abs(dy)) {
    // Horizontal first, then vertical
    return [start.x, start.y, end.x, start.y, end.x, end.y];
  } else {
    // Vertical first, then horizontal
    return [start.x, start.y, start.x, end.y, end.x, end.y];
  }
};

// Methods
const startConnectionCreation = (symbolId: string, pointId: string, position: Position) => {
  isCreatingConnection.value = true;
  tempConnection.value = {
    startX: position.x,
    startY: position.y,
    endX: position.x,
    endY: position.y,
    sourceSymbolId: symbolId,
    sourcePointId: pointId,
  };

  // Emit global connection operation start event for enhanced busbar feedback
  const startEvent = new CustomEvent('connection-operation-start', {
    detail: { symbolId, pointId, position }
  });
  document.dispatchEvent(startEvent);

  // Add event listeners
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

const updateConnectionCreation = (position: Position) => {
  if (!isCreatingConnection.value) return;

  // Update the end position
  tempConnection.value.endX = position.x;
  tempConnection.value.endY = position.y;

  // Update mouse position for snapping
  mousePosition.value = position;
};

const finishConnectionCreation = (targetSymbolId: string, targetPointId: string) => {
  if (!isCreatingConnection.value || !diagramStore.currentDiagram) return;

  // Create the connection with smart routing
  diagramStore.addConnection(
    tempConnection.value.sourceSymbolId,
    tempConnection.value.sourcePointId,
    targetSymbolId,
    targetPointId,
    ConnectionType.CABLE,
    ConnectionLineType.SMART, // Use smart connection type
    undefined, // layerId
    {}, // style
    undefined, // label
    defaultRoutingOptions // Use default routing options
  );

  // Emit global connection operation end event (successful completion)
  const endEvent = new CustomEvent('connection-operation-end', {
    detail: {
      completed: true,
      sourceSymbolId: tempConnection.value.sourceSymbolId,
      targetSymbolId: targetSymbolId
    }
  });
  document.dispatchEvent(endEvent);

  // Reset state (without emitting another end event)
  isCreatingConnection.value = false;
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
};

const cancelConnectionCreation = () => {
  isCreatingConnection.value = false;

  // Emit global connection operation end event
  const endEvent = new CustomEvent('connection-operation-end', {
    detail: { cancelled: true }
  });
  document.dispatchEvent(endEvent);

  // Remove event listeners
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
};

const handleMouseMove = (event: MouseEvent) => {
  try {
    if (!isCreatingConnection.value || !props.stageRef) return;

    // Prevent browser extension interference
    event.stopPropagation();

    // Get the mouse position relative to the stage with error handling
    const stage = props.stageRef;
    if (!stage || !stage.getNode) return;

    const stageNode = stage.getNode();
    if (!stageNode || !stageNode.getRelativePointerPosition) return;

    const stagePos = stageNode.getRelativePointerPosition();
    if (!stagePos) return;

    // Update the connection
    updateConnectionCreation(stagePos);
  } catch (error) {
    console.warn('Connection drag mouse move error (likely browser extension interference):', error);
    // Continue operation despite error
  }
};

const handleMouseUp = (event: MouseEvent) => {
  try {
    if (!isCreatingConnection.value) return;

    console.log('Mouse up - attempting to finish connection');

    // Prevent browser extension interference
    event.stopPropagation();

    // Check if we're over a connection point
    const nearestPoint = findNearestConnectionPoint();
    if (nearestPoint) {
      console.log(`Creating connection to ${nearestPoint.symbolId}-${nearestPoint.pointId}`);
      finishConnectionCreation(nearestPoint.symbolId, nearestPoint.pointId);
    } else {
      console.log('No connection point found within snap distance - cancelling');
      // Cancel if not over a connection point
      cancelConnectionCreation();
    }
  } catch (error) {
    console.warn('Connection drag mouse up error (likely browser extension interference):', error);
    // Ensure cleanup happens even if error occurs
    cancelConnectionCreation();
  }
};

const findNearestConnectionPoint = () => {
  if (!isCreatingConnection.value) return null;

  console.log('Finding nearest connection point...');
  console.log('Mouse position:', mousePosition.value);
  console.log('Available snap points:', snapPoints.value.length);
  console.log('Snap distance:', snapDistance.value);

  // Find the nearest connection point within snap distance
  let nearestPoint = null;
  let minDistance = snapDistance.value;

  snapPoints.value.forEach(point => {
    const distance = Math.sqrt(
      Math.pow(point.position.x - mousePosition.value.x, 2) +
      Math.pow(point.position.y - mousePosition.value.y, 2)
    );

    console.log(`Point ${point.symbolId}-${point.pointId} at (${point.position.x}, ${point.position.y}), distance: ${distance}`);

    if (distance < minDistance) {
      minDistance = distance;
      nearestPoint = point;
      console.log(`New nearest point found: ${point.symbolId}-${point.pointId}, distance: ${distance}`);
    }
  });

  console.log('Final nearest point:', nearestPoint);
  return nearestPoint;
};

const isPointInRange = (position: Position) => {
  if (!isCreatingConnection.value) return false;

  // Check if the point is within snap distance
  const distance = Math.sqrt(
    Math.pow(position.x - mousePosition.value.x, 2) +
    Math.pow(position.y - mousePosition.value.y, 2)
  );

  return distance < snapDistance.value;
};

// Global error handler for browser extension interference
const handleGlobalError = (error: ErrorEvent) => {
  // Check if error is related to browser extension interference
  if (error.message && (
    error.message.includes('offset is not a function') ||
    error.message.includes('isVisibleInPage') ||
    error.message.includes('preload-content.js') ||
    error.message.includes('shared.js')
  )) {
    console.warn('Browser extension interference detected during connection operation:', error.message);
    // Prevent the error from breaking the application
    error.preventDefault();
    return true;
  }
  return false;
};

// Lifecycle hooks
onMounted(() => {
  // Add global error handler for browser extension interference
  window.addEventListener('error', handleGlobalError);
});

onUnmounted(() => {
  // Clean up event listeners
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  window.removeEventListener('error', handleGlobalError);
});

// Expose methods to parent component
defineExpose({
  startConnectionCreation,
  updateConnectionCreation,
  finishConnectionCreation,
  cancelConnectionCreation,
});
</script>

<style scoped>
.connection-creator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
</style>
