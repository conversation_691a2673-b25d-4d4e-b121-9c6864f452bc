/**
 * Connection themes for PowerFlow Web Designer
 */

import { v4 as uuidv4 } from 'uuid';
import {
  ConnectionTheme,
  ConnectionType,
  ConnectionStyle,
  LineCapStyle,
  LineJoinStyle,
  JumpStyle,
  EndMarkerStyle,
  GradientType,
  PatternType,
  PowerFlowAnimationType,
  PowerFlowDirection,
  ThemeCategory,
} from '@/types/connection';

// Basic theme (default)
const basicTheme: ConnectionTheme = {
  id: 'basic',
  name: 'Basic',
  description: 'A simple black line',
  isBuiltIn: true,
  isDefault: true,
  style: {
    strokeColor: '#000000',
    lineWidth: 2,
    lineCap: LineCapStyle.ROUND,
    lineJoin: LineJoinStyle.ROUND,
    lineOpacity: 1,
    jumpStyle: JumpStyle.NONE,
  },
};

// Dashed theme
const dashedTheme: ConnectionTheme = {
  id: 'dashed',
  name: 'Dashed',
  description: 'A dashed black line',
  isBuiltIn: true,
  style: {
    strokeColor: '#000000',
    lineWidth: 2,
    lineDash: [5, 5],
    lineCap: LineCapStyle.ROUND,
    lineJoin: LineJoinStyle.ROUND,
    lineOpacity: 1,
    jumpStyle: JumpStyle.NONE,
  },
};

// Dotted theme
const dottedTheme: ConnectionTheme = {
  id: 'dotted',
  name: 'Dotted',
  description: 'A dotted black line',
  isBuiltIn: true,
  style: {
    strokeColor: '#000000',
    lineWidth: 2,
    lineDash: [2, 2],
    lineCap: LineCapStyle.ROUND,
    lineJoin: LineJoinStyle.ROUND,
    lineOpacity: 1,
    jumpStyle: JumpStyle.NONE,
  },
};

// Electrical power theme
const electricalPowerTheme: ConnectionTheme = {
  id: 'electrical-power',
  name: 'Electrical Power',
  description: 'A thick red line for electrical power connections',
  isBuiltIn: true,
  applicableTypes: [ConnectionType.CABLE],
  style: {
    strokeColor: '#ff0000',
    lineWidth: 3,
    lineCap: LineCapStyle.SQUARE,
    lineJoin: LineJoinStyle.MITER,
    lineOpacity: 1,
    jumpStyle: JumpStyle.NONE,
    animation: {
      type: PowerFlowAnimationType.PULSE,
      direction: PowerFlowDirection.FORWARD,
      speed: 5,
      color: '#ff0000',
      enabled: false,
    },
  },
};

// Control signal theme
const controlSignalTheme: ConnectionTheme = {
  id: 'control-signal',
  name: 'Control Signal',
  description: 'A blue line with arrow for control signals',
  isBuiltIn: true,
  applicableTypes: [ConnectionType.CONTROL],
  style: {
    strokeColor: '#0000ff',
    lineWidth: 1.5,
    lineCap: LineCapStyle.ROUND,
    lineJoin: LineJoinStyle.ROUND,
    lineOpacity: 1,
    jumpStyle: JumpStyle.ARC,
    targetMarker: {
      style: EndMarkerStyle.ARROW,
      size: 10,
      filled: true,
    },
    animation: {
      type: PowerFlowAnimationType.FLOW,
      direction: PowerFlowDirection.FORWARD,
      speed: 10,
      color: '#0000ff',
      enabled: false,
    },
  },
};

// Data flow theme
const dataFlowTheme: ConnectionTheme = {
  id: 'data-flow',
  name: 'Data Flow',
  description: 'A green dashed line with arrow for data flows',
  isBuiltIn: true,
  applicableTypes: [ConnectionType.SIGNAL],
  style: {
    strokeColor: '#00aa00',
    lineWidth: 1.5,
    lineDash: [5, 3],
    lineCap: LineCapStyle.ROUND,
    lineJoin: LineJoinStyle.ROUND,
    lineOpacity: 1,
    jumpStyle: JumpStyle.GAP,
    targetMarker: {
      style: EndMarkerStyle.ARROW,
      size: 8,
      filled: true,
    },
    animation: {
      type: PowerFlowAnimationType.DOTS,
      direction: PowerFlowDirection.FORWARD,
      speed: 15,
      color: '#00aa00',
      enabled: false,
    },
  },
};

// Gradient theme
const gradientTheme: ConnectionTheme = {
  id: 'gradient',
  name: 'Gradient',
  description: 'A line with gradient color',
  isBuiltIn: true,
  style: {
    strokeColor: '#000000',
    lineWidth: 3,
    lineCap: LineCapStyle.ROUND,
    lineJoin: LineJoinStyle.ROUND,
    lineOpacity: 1,
    jumpStyle: JumpStyle.NONE,
    gradient: {
      type: GradientType.LINEAR,
      startColor: '#ff0000',
      endColor: '#0000ff',
      angle: 0,
    },
  },
};

// Double line theme
const doubleLineTheme: ConnectionTheme = {
  id: 'double-line',
  name: 'Double Line',
  description: 'A double line',
  isBuiltIn: true,
  style: {
    strokeColor: '#000000',
    lineWidth: 2,
    lineCap: LineCapStyle.ROUND,
    lineJoin: LineJoinStyle.ROUND,
    lineOpacity: 1,
    jumpStyle: JumpStyle.NONE,
    doubleLine: {
      enabled: true,
      gap: 2,
      innerColor: '#ffffff',
      innerWidth: 1,
    },
  },
};

// Bidirectional theme
const bidirectionalTheme: ConnectionTheme = {
  id: 'bidirectional',
  name: 'Bidirectional',
  description: 'A line with arrows on both ends',
  isBuiltIn: true,
  style: {
    strokeColor: '#000000',
    lineWidth: 2,
    lineCap: LineCapStyle.ROUND,
    lineJoin: LineJoinStyle.ROUND,
    lineOpacity: 1,
    jumpStyle: JumpStyle.NONE,
    sourceMarker: {
      style: EndMarkerStyle.ARROW,
      size: 8,
      filled: true,
    },
    targetMarker: {
      style: EndMarkerStyle.ARROW,
      size: 8,
      filled: true,
    },
  },
};

// Highlighted theme
const highlightedTheme: ConnectionTheme = {
  id: 'highlighted',
  name: 'Highlighted',
  description: 'A highlighted line with shadow',
  isBuiltIn: true,
  style: {
    strokeColor: '#ff9900',
    lineWidth: 3,
    lineCap: LineCapStyle.ROUND,
    lineJoin: LineJoinStyle.ROUND,
    lineOpacity: 1,
    jumpStyle: JumpStyle.NONE,
    shadowColor: '#ff9900',
    shadowBlur: 10,
    shadowOffset: { x: 0, y: 0 },
  },
};

// All built-in themes
export const builtInThemes: ConnectionTheme[] = [
  basicTheme,
  dashedTheme,
  dottedTheme,
  electricalPowerTheme,
  controlSignalTheme,
  dataFlowTheme,
  gradientTheme,
  doubleLineTheme,
  bidirectionalTheme,
  highlightedTheme,
];

// Get theme by ID
export function getThemeById(id: string, themes: ConnectionTheme[]): ConnectionTheme | undefined {
  return [...builtInThemes, ...themes].find(theme => theme.id === id);
}

// Create a new theme
export function createTheme(
  name: string,
  style: ConnectionStyle,
  description?: string,
  parentThemeId?: string,
  applicableTypes?: ConnectionType[]
): ConnectionTheme {
  return {
    id: uuidv4(),
    name,
    description,
    style,
    parentThemeId,
    applicableTypes,
    isBuiltIn: false,
  };
}

// Apply a theme to a connection style
export function applyTheme(
  baseStyle: ConnectionStyle,
  theme: ConnectionTheme,
  themes: ConnectionTheme[]
): ConnectionStyle {
  // If the theme has a parent, apply the parent theme first
  let resultStyle = { ...baseStyle };
  
  if (theme.parentThemeId) {
    const parentTheme = getThemeById(theme.parentThemeId, themes);
    if (parentTheme) {
      resultStyle = applyTheme(resultStyle, parentTheme, themes);
    }
  }
  
  // Apply the theme style
  return { ...resultStyle, ...theme.style };
}

// Get themes by category
export function getThemesByCategory(
  category: ThemeCategory,
  themes: ConnectionTheme[]
): ConnectionTheme[] {
  // For now, we don't have category in the theme interface
  // This is a placeholder for future implementation
  return [...builtInThemes, ...themes];
}

// Get themes by connection type
export function getThemesByConnectionType(
  type: ConnectionType,
  themes: ConnectionTheme[]
): ConnectionTheme[] {
  return [...builtInThemes, ...themes].filter(
    theme => !theme.applicableTypes || theme.applicableTypes.includes(type)
  );
}

// Get default theme
export function getDefaultTheme(themes: ConnectionTheme[]): ConnectionTheme {
  // First, check for a custom default theme
  const customDefault = themes.find(theme => theme.isDefault);
  if (customDefault) return customDefault;
  
  // Otherwise, return the built-in default theme
  return builtInThemes.find(theme => theme.isDefault) || basicTheme;
}
