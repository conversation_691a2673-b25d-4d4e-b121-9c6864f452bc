<template>
  <!-- Full Toolbar (when expanded) -->
  <div v-if="showToolbar && !isMinimized" class="multi-selection-toolbar">
    <div class="toolbar-header">
      <span class="selection-count">
        {{ selectionCountText }}
      </span>
      <div class="header-buttons">
        <a-tooltip title="Minimize toolbar">
          <a-button
            type="text"
            size="small"
            @click="toggleMinimized"
            class="minimize-button"
          >
            <template #icon>
              <up-outlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-button
          type="text"
          size="small"
          @click="clearSelection"
          class="clear-button"
        >
          <template #icon><close-outlined /></template>
          Clear Selection
        </a-button>
      </div>
    </div>

    <div class="toolbar-actions">
      <!-- Selection Actions -->
      <div class="action-group">
        <div class="group-label">Selection</div>
        <a-button-group>
          <a-tooltip title="Select All Elements (Ctrl+A)">
            <a-button @click="$emit('select-all')" size="small">
              <template #icon><select-outlined /></template>
              Select All
            </a-button>
          </a-tooltip>
          <a-tooltip title="Invert Selection">
            <a-button @click="$emit('invert-selection')" size="small">
              <template #icon><swap-outlined /></template>
              Invert
            </a-button>
          </a-tooltip>
        </a-button-group>
      </div>

      <!-- Edit Actions -->
      <div class="action-group">
        <div class="group-label">Edit</div>
        <a-button-group>
          <a-tooltip title="Copy Selected (Ctrl+C)">
            <a-button
              @click="$emit('copy')"
              size="small"
              :disabled="!hasSelection"
            >
              <template #icon><copy-outlined /></template>
              Copy
            </a-button>
          </a-tooltip>
          <a-tooltip title="Cut Selected (Ctrl+X)">
            <a-button
              @click="$emit('cut')"
              size="small"
              :disabled="!hasSelection"
            >
              <template #icon><scissor-outlined /></template>
              Cut
            </a-button>
          </a-tooltip>
          <a-tooltip title="Delete Selected (Delete)">
            <a-button
              @click="$emit('delete')"
              size="small"
              :disabled="!hasSelection"
              danger
            >
              <template #icon><delete-outlined /></template>
              Delete
            </a-button>
          </a-tooltip>
        </a-button-group>
      </div>

      <!-- Rotation Actions -->
      <div class="action-group" v-if="hasSelection">
        <div class="group-label">Rotate</div>
        <a-button-group>
          <a-tooltip title="Rotate Counterclockwise (90°)">
            <a-button
              @click="$emit('rotate-counterclockwise')"
              size="small"
              :disabled="!hasSelection"
            >
              <template #icon><rotate-left-outlined /></template>
              CCW
            </a-button>
          </a-tooltip>
          <a-tooltip title="Rotate Clockwise (90°)">
            <a-button
              @click="$emit('rotate-clockwise')"
              size="small"
              :disabled="!hasSelection"
            >
              <template #icon><rotate-right-outlined /></template>
              CW
            </a-button>
          </a-tooltip>
        </a-button-group>
      </div>

      <!-- Arrangement Actions -->
      <div class="action-group" v-if="hasMultipleSymbols">
        <div class="group-label">Arrange</div>
        <a-button-group>
          <a-tooltip title="Bring to Front">
            <a-button @click="$emit('bring-to-front')" size="small">
              <template #icon><vertical-align-top-outlined /></template>
              Front
            </a-button>
          </a-tooltip>
          <a-tooltip title="Send to Back">
            <a-button @click="$emit('send-to-back')" size="small">
              <template #icon><vertical-align-bottom-outlined /></template>
              Back
            </a-button>
          </a-tooltip>
        </a-button-group>
      </div>

      <!-- Group Actions -->
      <div class="action-group" v-if="hasMultipleSymbols">
        <div class="group-label">Group</div>
        <a-button-group>
          <a-tooltip title="Create Group">
            <a-button @click="$emit('create-group')" size="small">
              <template #icon><group-outlined /></template>
              Group
            </a-button>
          </a-tooltip>
          <a-tooltip title="Ungroup Selected" v-if="hasSelectedGroups">
            <a-button @click="$emit('ungroup')" size="small">
              <template #icon><ungroup-outlined /></template>
              Ungroup
            </a-button>
          </a-tooltip>
        </a-button-group>
      </div>

      <!-- Alignment Actions (shown when 2+ symbols selected) -->
      <div class="action-group" v-if="canAlign">
        <div class="group-label">Align</div>
        <a-button-group>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="$emit('align', 'left')">
                  <align-left-outlined /> Align Left
                </a-menu-item>
                <a-menu-item @click="$emit('align', 'center')">
                  <align-center-outlined /> Align Center
                </a-menu-item>
                <a-menu-item @click="$emit('align', 'right')">
                  <align-right-outlined /> Align Right
                </a-menu-item>
              </a-menu>
            </template>
            <a-button size="small">
              <template #icon><align-center-outlined /></template>
              Align
              <down-outlined />
            </a-button>
          </a-dropdown>

          <a-dropdown v-if="canDistribute">
            <template #overlay>
              <a-menu>
                <a-menu-item @click="$emit('distribute', 'horizontal')">
                  <column-width-outlined /> Distribute Horizontally
                </a-menu-item>
                <a-menu-item @click="$emit('distribute', 'vertical')">
                  <column-height-outlined /> Distribute Vertically
                </a-menu-item>
              </a-menu>
            </template>
            <a-button size="small">
              <template #icon><column-width-outlined /></template>
              Distribute
              <down-outlined />
            </a-button>
          </a-dropdown>
        </a-button-group>
      </div>
    </div>
  </div>

  <!-- Floating Indicator (when minimized) -->
  <div v-if="showToolbar && isMinimized" class="floating-indicator">
    <a-tooltip title="Click to expand toolbar">
      <div class="indicator-content" @click="toggleMinimized">
        <span class="indicator-count">{{ selectionCountText }}</span>
        <down-outlined class="indicator-icon" />
      </div>
    </a-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import {
  CloseOutlined,
  SelectOutlined,
  SwapOutlined,
  CopyOutlined,
  ScissorOutlined,
  DeleteOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignBottomOutlined,
  GroupOutlined,
  UngroupOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  ColumnWidthOutlined,
  ColumnHeightOutlined,
  DownOutlined,
  UpOutlined,
  RotateLeftOutlined,
  RotateRightOutlined
} from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';

// Define emits
const emit = defineEmits<{
  (e: 'select-all'): void;
  (e: 'invert-selection'): void;
  (e: 'copy'): void;
  (e: 'cut'): void;
  (e: 'delete'): void;
  (e: 'bring-to-front'): void;
  (e: 'send-to-back'): void;
  (e: 'create-group'): void;
  (e: 'ungroup'): void;
  (e: 'align', type: string): void;
  (e: 'distribute', type: string): void;
  (e: 'rotate-clockwise'): void;
  (e: 'rotate-counterclockwise'): void;
  (e: 'clear-selection'): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const isMinimized = ref(false);

// Methods
const toggleMinimized = () => {
  isMinimized.value = !isMinimized.value;
};

// Computed properties
const showToolbar = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return hasSelection.value;
});

const hasSelection = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return (
    diagramStore.currentDiagram.selectedSymbolIds.length > 0 ||
    diagramStore.currentDiagram.selectedConnectionIds.length > 0 ||
    diagramStore.currentDiagram.selectedGroupIds.length > 0
  );
});

const hasMultipleSymbols = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return diagramStore.currentDiagram.selectedSymbolIds.length >= 2;
});

const hasSelectedGroups = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return diagramStore.currentDiagram.selectedGroupIds.length > 0;
});

const canAlign = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return diagramStore.currentDiagram.selectedSymbolIds.length >= 2;
});

const canDistribute = computed(() => {
  if (!diagramStore.currentDiagram) return false;
  return diagramStore.currentDiagram.selectedSymbolIds.length >= 3;
});

const selectionCountText = computed(() => {
  if (!diagramStore.currentDiagram) return '';
  
  const symbolCount = diagramStore.currentDiagram.selectedSymbolIds.length;
  const connectionCount = diagramStore.currentDiagram.selectedConnectionIds.length;
  const groupCount = diagramStore.currentDiagram.selectedGroupIds.length;
  
  const parts = [];
  if (symbolCount > 0) parts.push(`${symbolCount} symbol${symbolCount > 1 ? 's' : ''}`);
  if (connectionCount > 0) parts.push(`${connectionCount} connection${connectionCount > 1 ? 's' : ''}`);
  if (groupCount > 0) parts.push(`${groupCount} group${groupCount > 1 ? 's' : ''}`);
  
  return parts.join(', ') + ' selected';
});

// Methods
const clearSelection = () => {
  emit('clear-selection');
};
</script>

<style scoped>
.multi-selection-toolbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  z-index: 1000;
  max-width: 90vw;
  overflow-x: auto;
}

.toolbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.selection-count {
  font-weight: 500;
  color: #1890ff;
  font-size: 14px;
}

.header-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.minimize-button {
  color: #666;
  transition: all 0.2s ease;
}

.minimize-button:hover {
  color: #1890ff;
  background-color: #f0f0f0;
}

.clear-button {
  color: #666;
}

/* Floating Indicator Styles */
.floating-indicator {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.indicator-content {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 20px;
  padding: 8px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
  justify-content: center;
}

.indicator-content:hover {
  background-color: #f0f8ff;
  border-color: #1890ff;
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
}

.indicator-count {
  font-weight: 500;
  color: #1890ff;
  font-size: 13px;
  white-space: nowrap;
}

.indicator-icon {
  color: #1890ff;
  font-size: 12px;
  transition: transform 0.2s ease;
}

.indicator-content:hover .indicator-icon {
  transform: translateY(-1px);
}

.toolbar-actions {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  flex-wrap: wrap;
}

.action-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: fit-content;
}

.group-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

:deep(.ant-btn-group) {
  display: flex;
}

:deep(.ant-btn-group .ant-btn) {
  border-radius: 4px;
  margin-right: 4px;
}

:deep(.ant-btn-group .ant-btn:last-child) {
  margin-right: 0;
}

:deep(.ant-btn-small) {
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
}

/* Responsive design */
@media (max-width: 768px) {
  .multi-selection-toolbar {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
  }

  .toolbar-actions {
    gap: 8px;
  }

  .action-group {
    min-width: auto;
  }

  .header-buttons {
    gap: 2px;
  }

  .floating-indicator {
    bottom: 10px;
  }

  .indicator-content {
    padding: 6px 12px;
    min-width: 100px;
    border-radius: 16px;
  }

  .indicator-count {
    font-size: 12px;
  }

  .indicator-icon {
    font-size: 11px;
  }
}
</style>
