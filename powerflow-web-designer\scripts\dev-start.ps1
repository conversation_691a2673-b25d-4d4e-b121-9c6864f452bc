# PowerFlow Web Designer 开发环境启动脚本
# 使用方法: .\scripts\dev-start.ps1

param(
    [switch]$Clean,      # 清理依赖重新安装
    [switch]$Test,       # 同时启动测试监视
    [switch]$Debug,      # 启用调试模式
    [switch]$Help        # 显示帮助信息
)

# 显示帮助信息
if ($Help) {
    Write-Host "PowerFlow Web Designer 开发环境启动脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "使用方法:" -ForegroundColor Yellow
    Write-Host "  .\scripts\dev-start.ps1                # 启动开发服务器"
    Write-Host "  .\scripts\dev-start.ps1 -Clean         # 清理依赖后启动"
    Write-Host "  .\scripts\dev-start.ps1 -Test          # 启动开发服务器和测试监视"
    Write-Host "  .\scripts\dev-start.ps1 -Debug         # 启用调试模式"
    Write-Host "  .\scripts\dev-start.ps1 -Help          # 显示此帮助信息"
    Write-Host ""
    Write-Host "参数说明:" -ForegroundColor Yellow
    Write-Host "  -Clean   清理node_modules并重新安装依赖"
    Write-Host "  -Test    同时启动测试监视模式"
    Write-Host "  -Debug   启用详细的调试输出"
    Write-Host "  -Help    显示帮助信息"
    exit 0
}

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取脚本目录和项目根目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

# 切换到项目根目录
Set-Location $ProjectRoot

Write-Host "🚀 PowerFlow Web Designer 开发环境启动" -ForegroundColor Green
Write-Host "项目目录: $ProjectRoot" -ForegroundColor Cyan

# 检查Node.js和npm
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到Node.js或npm，请先安装Node.js" -ForegroundColor Red
    exit 1
}

# 检查package.json
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 错误: 未找到package.json文件" -ForegroundColor Red
    exit 1
}

# 清理依赖（如果指定）
if ($Clean) {
    Write-Host "🧹 清理依赖..." -ForegroundColor Yellow
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules"
        Write-Host "✅ 已删除node_modules目录" -ForegroundColor Green
    }
    if (Test-Path "package-lock.json") {
        Remove-Item -Force "package-lock.json"
        Write-Host "✅ 已删除package-lock.json" -ForegroundColor Green
    }
}

# 安装依赖
if (-not (Test-Path "node_modules") -or $Clean) {
    Write-Host "📦 安装依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 依赖安装完成" -ForegroundColor Green
}

# 检查端口占用
$port = 5173
try {
    $connection = Test-NetConnection -ComputerName localhost -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($connection) {
        Write-Host "⚠️  警告: 端口 $port 已被占用，Vite会自动选择其他端口" -ForegroundColor Yellow
    }
} catch {
    # 忽略端口检查错误
}

# 设置环境变量
$env:NODE_ENV = "development"
if ($Debug) {
    $env:DEBUG = "*"
    Write-Host "🐛 调试模式已启用" -ForegroundColor Yellow
}

# 启动开发服务器
Write-Host "🚀 启动开发服务器..." -ForegroundColor Green
Write-Host "📝 提示: 使用 Ctrl+C 停止服务器" -ForegroundColor Cyan

if ($Test) {
    Write-Host "🧪 同时启动测试监视..." -ForegroundColor Green
    # 在后台启动测试监视
    Start-Process -NoNewWindow -FilePath "npm" -ArgumentList "run", "test:watch"
    Start-Sleep -Seconds 2
}

# 启动开发服务器
try {
    npm run dev
} catch {
    Write-Host "❌ 开发服务器启动失败" -ForegroundColor Red
    exit 1
}

Write-Host "👋 开发服务器已停止" -ForegroundColor Yellow
