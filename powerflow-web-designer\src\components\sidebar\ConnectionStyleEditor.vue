<template>
  <div class="connection-style-editor">
    <h3>Connection Style</h3>

    <div v-if="selectedConnection">
      <!-- Line Type -->
      <div class="form-group">
        <label>Line Type</label>
        <select v-model="lineType" @change="updateLineType">
          <option value="straight">Straight</option>
          <option value="polyline">Polyline</option>
          <option value="bezier">Bezier</option>
          <option value="smart">Smart</option>
        </select>
      </div>

      <!-- Line Color -->
      <div class="form-group">
        <label>Line Color</label>
        <input type="color" v-model="strokeColor" @change="updateStyle" />
      </div>

      <!-- Line Width -->
      <div class="form-group">
        <label>Line Width</label>
        <input
          type="range"
          v-model.number="lineWidth"
          min="1"
          max="10"
          step="1"
          @change="updateStyle"
        />
        <span>{{ lineWidth }}px</span>
      </div>

      <!-- Line Dash -->
      <div class="form-group">
        <label>Line Style</label>
        <select v-model="dashStyle" @change="updateDashStyle">
          <option value="solid">Solid</option>
          <option value="dashed">Dashed</option>
          <option value="dotted">Dotted</option>
          <option value="dash-dot">Dash-Dot</option>
        </select>
      </div>

      <!-- Line Cap -->
      <div class="form-group">
        <label>Line Cap</label>
        <select v-model="lineCap" @change="updateStyle">
          <option value="butt">Butt</option>
          <option value="round">Round</option>
          <option value="square">Square</option>
        </select>
      </div>

      <!-- Line Join -->
      <div class="form-group">
        <label>Line Join</label>
        <select v-model="lineJoin" @change="updateStyle">
          <option value="miter">Miter</option>
          <option value="round">Round</option>
          <option value="bevel">Bevel</option>
        </select>
      </div>

      <!-- Jump Style -->
      <div class="form-group">
        <label>Jump Style</label>
        <select v-model="jumpStyle" @change="updateStyle">
          <option value="none">None</option>
          <option value="arc">Arc</option>
          <option value="gap">Gap</option>
          <option value="square">Square</option>
        </select>
      </div>

      <!-- Jump Size (only if jump style is not none) -->
      <div class="form-group" v-if="jumpStyle !== 'none'">
        <label>Jump Size</label>
        <input
          type="range"
          v-model.number="jumpSize"
          min="5"
          max="20"
          step="1"
          @change="updateStyle"
        />
        <span>{{ jumpSize }}px</span>
      </div>

      <!-- Opacity -->
      <div class="form-group">
        <label>Opacity</label>
        <input
          type="range"
          v-model.number="lineOpacity"
          min="0.1"
          max="1"
          step="0.1"
          @change="updateStyle"
        />
        <span>{{ Math.round(lineOpacity * 100) }}%</span>
      </div>

      <!-- Shadow -->
      <div class="form-group">
        <label>
          <input type="checkbox" v-model="hasShadow" @change="updateShadow" />
          Enable Shadow
        </label>
      </div>

      <!-- Shadow settings (only if shadow is enabled) -->
      <div v-if="hasShadow">
        <div class="form-group">
          <label>Shadow Color</label>
          <input type="color" v-model="shadowColor" @change="updateStyle" />
        </div>

        <div class="form-group">
          <label>Shadow Blur</label>
          <input
            type="range"
            v-model.number="shadowBlur"
            min="1"
            max="20"
            step="1"
            @change="updateStyle"
          />
          <span>{{ shadowBlur }}px</span>
        </div>

        <div class="form-group">
          <label>Shadow Offset X</label>
          <input
            type="range"
            v-model.number="shadowOffsetX"
            min="-10"
            max="10"
            step="1"
            @change="updateStyle"
          />
          <span>{{ shadowOffsetX }}px</span>
        </div>

        <div class="form-group">
          <label>Shadow Offset Y</label>
          <input
            type="range"
            v-model.number="shadowOffsetY"
            min="-10"
            max="10"
            step="1"
            @change="updateStyle"
          />
          <span>{{ shadowOffsetY }}px</span>
        </div>
      </div>

      <!-- Power Flow Animation Editor -->
      <div class="section-divider"></div>
      <power-flow-animation-editor />

      <!-- Smart Connection Editor (only if line type is smart) -->
      <template v-if="lineType === 'smart'">
        <div class="section-divider"></div>
        <smart-connection-editor />
      </template>
    </div>

    <div v-else class="no-selection">
      No connection selected
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import {
  Connection,
  ConnectionStyle,
  ConnectionLineType,
  LineCapStyle,
  LineJoinStyle,
  JumpStyle
} from '@/types/connection';
import PowerFlowAnimationEditor from './PowerFlowAnimationEditor.vue';
import SmartConnectionEditor from './SmartConnectionEditor.vue';

// Store
const diagramStore = useDiagramStore();

// Computed
const selectedConnection = computed<Connection | null>(() => {
  if (!diagramStore.currentDiagram || !diagramStore.selectedConnectionId) {
    return null;
  }

  return diagramStore.currentDiagram.connections[diagramStore.selectedConnectionId];
});

// Form state
const lineType = ref<string>('straight');
const strokeColor = ref<string>('#000000');
const lineWidth = ref<number>(2);
const dashStyle = ref<string>('solid');
const lineCap = ref<LineCapStyle>(LineCapStyle.ROUND);
const lineJoin = ref<LineJoinStyle>(LineJoinStyle.ROUND);
const jumpStyle = ref<JumpStyle>(JumpStyle.NONE);
const jumpSize = ref<number>(10);
const lineOpacity = ref<number>(1);
const hasShadow = ref<boolean>(false);
const shadowColor = ref<string>('#000000');
const shadowBlur = ref<number>(5);
const shadowOffsetX = ref<number>(2);
const shadowOffsetY = ref<number>(2);

// Initialize form values when selected connection changes
watch(selectedConnection, (connection) => {
  if (!connection) return;

  // Set line type
  lineType.value = connection.lineType;

  // Set style properties
  strokeColor.value = connection.style.strokeColor;
  lineWidth.value = connection.style.lineWidth;

  // Set dash style
  if (!connection.style.lineDash) {
    dashStyle.value = 'solid';
  } else if (connection.style.lineDash.join(',') === '5,5') {
    dashStyle.value = 'dashed';
  } else if (connection.style.lineDash.join(',') === '2,2') {
    dashStyle.value = 'dotted';
  } else if (connection.style.lineDash.join(',') === '10,5,2,5') {
    dashStyle.value = 'dash-dot';
  }

  // Set line cap and join
  lineCap.value = connection.style.lineCap || LineCapStyle.ROUND;
  lineJoin.value = connection.style.lineJoin || LineJoinStyle.ROUND;

  // Set jump style
  jumpStyle.value = connection.style.jumpStyle || JumpStyle.NONE;
  jumpSize.value = connection.style.jumpSize || 10;

  // Set opacity
  lineOpacity.value = connection.style.lineOpacity !== undefined ? connection.style.lineOpacity : 1;

  // Set shadow
  hasShadow.value = !!connection.style.shadowColor;
  shadowColor.value = connection.style.shadowColor || '#000000';
  shadowBlur.value = connection.style.shadowBlur || 5;
  shadowOffsetX.value = connection.style.shadowOffset?.x || 2;
  shadowOffsetY.value = connection.style.shadowOffset?.y || 2;
}, { immediate: true });

// Methods
const updateLineType = () => {
  if (!selectedConnection.value) return;

  diagramStore.updateConnection({
    ...selectedConnection.value,
    lineType: lineType.value as ConnectionLineType
  });
};

const updateDashStyle = () => {
  if (!selectedConnection.value) return;

  let lineDash: number[] | undefined;

  switch (dashStyle.value) {
    case 'solid':
      lineDash = undefined;
      break;
    case 'dashed':
      lineDash = [5, 5];
      break;
    case 'dotted':
      lineDash = [2, 2];
      break;
    case 'dash-dot':
      lineDash = [10, 5, 2, 5];
      break;
  }

  updateStyle({ lineDash });
};

const updateShadow = () => {
  if (!selectedConnection.value) return;

  if (hasShadow.value) {
    updateStyle({
      shadowColor: shadowColor.value,
      shadowBlur: shadowBlur.value,
      shadowOffset: {
        x: shadowOffsetX.value,
        y: shadowOffsetY.value
      }
    });
  } else {
    updateStyle({
      shadowColor: undefined,
      shadowBlur: undefined,
      shadowOffset: undefined
    });
  }
};

const updateStyle = (partialStyle: Partial<ConnectionStyle> = {}) => {
  if (!selectedConnection.value) return;

  const newStyle: ConnectionStyle = {
    strokeColor: strokeColor.value,
    lineWidth: lineWidth.value,
    lineOpacity: lineOpacity.value,
    lineCap: lineCap.value,
    lineJoin: lineJoin.value,
    jumpStyle: jumpStyle.value,
    jumpSize: jumpSize.value,
    ...partialStyle
  };

  if (hasShadow.value) {
    newStyle.shadowColor = shadowColor.value;
    newStyle.shadowBlur = shadowBlur.value;
    newStyle.shadowOffset = {
      x: shadowOffsetX.value,
      y: shadowOffsetY.value
    };
  }

  diagramStore.updateConnectionWithValidation({
    ...selectedConnection.value,
    style: newStyle
  });
};
</script>

<style scoped>
.connection-style-editor {
  padding: 16px;
}

.form-group {
  margin-bottom: 16px;
}

label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
}

input[type="range"] {
  width: 100%;
}

select {
  width: 100%;
  padding: 4px;
}

.no-selection {
  color: #888;
  font-style: italic;
}

.section-divider {
  height: 1px;
  background-color: #ddd;
  margin: 20px 0;
}
</style>
