<template>
  <v-group>
    <!-- Horizontal guides -->
    <v-group v-for="guide in horizontalGuides" :key="guide.id">
      <v-line
        :config="{
          points: [guide.start.x, guide.start.y, guide.end.x, guide.end.y],
          stroke: guideColor,
          strokeWidth: 1,
          dash: [5, 5],
        }"
      />
      <v-text
        :config="{
          x: guide.labelPosition.x,
          y: guide.labelPosition.y,
          text: guide.label,
          fontSize: 10,
          fontFamily: 'Arial',
          fill: guideColor,
          align: 'center',
          padding: 2,
          background: '#fff',
        }"
      />
    </v-group>

    <!-- Vertical guides -->
    <v-group v-for="guide in verticalGuides" :key="guide.id">
      <v-line
        :config="{
          points: [guide.start.x, guide.start.y, guide.end.x, guide.end.y],
          stroke: guideColor,
          strokeWidth: 1,
          dash: [5, 5],
        }"
      />
      <v-text
        :config="{
          x: guide.labelPosition.x,
          y: guide.labelPosition.y,
          text: guide.label,
          fontSize: 10,
          fontFamily: 'Arial',
          fill: guideColor,
          align: 'center',
          padding: 2,
          background: '#fff',
        }"
      />
    </v-group>

    <!-- Spacing guides -->
    <v-group v-for="guide in spacingGuides" :key="guide.id">
      <v-line
        :config="{
          points: [guide.start.x, guide.start.y, guide.end.x, guide.end.y],
          stroke: spacingColor,
          strokeWidth: 1,
          dash: [2, 2],
        }"
      />
      <v-text
        :config="{
          x: guide.labelPosition.x,
          y: guide.labelPosition.y,
          text: guide.label,
          fontSize: 10,
          fontFamily: 'Arial',
          fill: spacingColor,
          align: 'center',
          padding: 2,
          background: '#fff',
        }"
      />
    </v-group>
  </v-group>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { useDiagramStore } from '@/stores/diagram';
import { Position } from '@/types/symbol';
import { SymbolInstance } from '@/types/symbolInstance';
import { AlignmentType } from '@/utils/alignment';

// Props
const props = defineProps<{
  activeSymbolId?: string;
  showGuides?: boolean;
  threshold?: number;
}>();

// Store
const diagramStore = useDiagramStore();

// Constants
const guideColor = '#1890ff';
const spacingColor = '#52c41a';
const defaultThreshold = 10;

// Computed
const threshold = computed(() => props.threshold || defaultThreshold);

const activeSymbol = computed(() => {
  if (!props.activeSymbolId || !diagramStore.currentDiagram) return null;
  return diagramStore.currentDiagram.symbolInstances[props.activeSymbolId];
});

const otherSymbols = computed(() => {
  if (!diagramStore.currentDiagram) return [];
  
  return Object.values(diagramStore.currentDiagram.symbolInstances)
    .filter(symbol => symbol.id !== props.activeSymbolId);
});

// Guide interfaces
interface Guide {
  id: string;
  start: Position;
  end: Position;
  label: string;
  labelPosition: Position;
}

// Horizontal guides (top, middle, bottom)
const horizontalGuides = computed<Guide[]>(() => {
  if (!props.showGuides || !activeSymbol.value) return [];
  
  const guides: Guide[] = [];
  const active = activeSymbol.value;
  
  // Calculate active symbol's horizontal lines
  const activeTop = active.position.y;
  const activeMiddle = activeTop + (active.dimensions?.height || 100) / 2;
  const activeBottom = activeTop + (active.dimensions?.height || 100);
  
  // Check each other symbol for potential alignment
  otherSymbols.value.forEach(symbol => {
    const symbolTop = symbol.position.y;
    const symbolMiddle = symbolTop + (symbol.dimensions?.height || 100) / 2;
    const symbolBottom = symbolTop + (symbol.dimensions?.height || 100);
    
    // Check top alignment
    if (Math.abs(activeTop - symbolTop) < threshold.value) {
      guides.push(createHorizontalGuide('top', activeTop, active, symbol));
    }
    
    // Check middle alignment
    if (Math.abs(activeMiddle - symbolMiddle) < threshold.value) {
      guides.push(createHorizontalGuide('middle', activeMiddle, active, symbol));
    }
    
    // Check bottom alignment
    if (Math.abs(activeBottom - symbolBottom) < threshold.value) {
      guides.push(createHorizontalGuide('bottom', activeBottom, active, symbol));
    }
  });
  
  return guides;
});

// Vertical guides (left, center, right)
const verticalGuides = computed<Guide[]>(() => {
  if (!props.showGuides || !activeSymbol.value) return [];
  
  const guides: Guide[] = [];
  const active = activeSymbol.value;
  
  // Calculate active symbol's vertical lines
  const activeLeft = active.position.x;
  const activeCenter = activeLeft + (active.dimensions?.width || 100) / 2;
  const activeRight = activeLeft + (active.dimensions?.width || 100);
  
  // Check each other symbol for potential alignment
  otherSymbols.value.forEach(symbol => {
    const symbolLeft = symbol.position.x;
    const symbolCenter = symbolLeft + (symbol.dimensions?.width || 100) / 2;
    const symbolRight = symbolLeft + (symbol.dimensions?.width || 100);
    
    // Check left alignment
    if (Math.abs(activeLeft - symbolLeft) < threshold.value) {
      guides.push(createVerticalGuide('left', activeLeft, active, symbol));
    }
    
    // Check center alignment
    if (Math.abs(activeCenter - symbolCenter) < threshold.value) {
      guides.push(createVerticalGuide('center', activeCenter, active, symbol));
    }
    
    // Check right alignment
    if (Math.abs(activeRight - symbolRight) < threshold.value) {
      guides.push(createVerticalGuide('right', activeRight, active, symbol));
    }
  });
  
  return guides;
});

// Spacing guides
const spacingGuides = computed<Guide[]>(() => {
  if (!props.showGuides || !activeSymbol.value) return [];
  
  const guides: Guide[] = [];
  // Add spacing guides logic here if needed
  
  return guides;
});

// Helper functions
function createHorizontalGuide(
  type: string,
  y: number,
  activeSymbol: SymbolInstance,
  otherSymbol: SymbolInstance
): Guide {
  // Calculate the start and end points of the guide line
  const minX = Math.min(activeSymbol.position.x, otherSymbol.position.x) - 20;
  const maxX = Math.max(
    activeSymbol.position.x + (activeSymbol.dimensions?.width || 100),
    otherSymbol.position.x + (otherSymbol.dimensions?.width || 100)
  ) + 20;
  
  return {
    id: `h-${type}-${activeSymbol.id}-${otherSymbol.id}`,
    start: { x: minX, y },
    end: { x: maxX, y },
    label: type,
    labelPosition: { x: (minX + maxX) / 2, y: y - 15 },
  };
}

function createVerticalGuide(
  type: string,
  x: number,
  activeSymbol: SymbolInstance,
  otherSymbol: SymbolInstance
): Guide {
  // Calculate the start and end points of the guide line
  const minY = Math.min(activeSymbol.position.y, otherSymbol.position.y) - 20;
  const maxY = Math.max(
    activeSymbol.position.y + (activeSymbol.dimensions?.height || 100),
    otherSymbol.position.y + (otherSymbol.dimensions?.height || 100)
  ) + 20;
  
  return {
    id: `v-${type}-${activeSymbol.id}-${otherSymbol.id}`,
    start: { x, y: minY },
    end: { x, y: maxY },
    label: type,
    labelPosition: { x: x + 15, y: (minY + maxY) / 2 },
  };
}
</script>
