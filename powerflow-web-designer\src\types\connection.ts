/**
 * Connection types for PowerFlow Web Designer
 */

import type { Position } from './symbol';

// Connection endpoint type
export interface ConnectionEndpoint {
  symbolInstanceId: string;
  connectionPointId: string;
}

// Connection point type
export enum ConnectionType {
  CABLE = 'cable',
  BUSBAR = 'busbar',
  CONTROL = 'control',
  SIGNAL = 'signal',
}

// Line cap style
export enum LineCapStyle {
  BUTT = 'butt',
  ROUND = 'round',
  SQUARE = 'square',
}

// Line join style
export enum LineJoinStyle {
  MITER = 'miter',
  ROUND = 'round',
  BEVEL = 'bevel',
}

// Jump style for line intersections
export enum JumpStyle {
  NONE = 'none',
  ARC = 'arc',
  GAP = 'gap',
  SQUARE = 'square',
}

// End marker style
export enum EndMarkerStyle {
  NONE = 'none',
  ARROW = 'arrow',
  CIRCLE = 'circle',
  DIAMOND = 'diamond',
  SQUARE = 'square',
  CUSTOM = 'custom',
}

// Gradient type
export enum GradientType {
  NONE = 'none',
  LINEAR = 'linear',
  RADIAL = 'radial',
}

// Pattern type
export enum PatternType {
  NONE = 'none',
  DOTS = 'dots',
  LINES = 'lines',
  GRID = 'grid',
  CUSTOM = 'custom',
}

// Power flow animation type
export enum PowerFlowAnimationType {
  NONE = 'none',
  PULSE = 'pulse',
  FLOW = 'flow',
  DASH = 'dash',
}

// Power flow animation direction
export enum PowerFlowDirection {
  FORWARD = 'forward', // Source to target
  BACKWARD = 'backward', // Target to source
  BIDIRECTIONAL = 'bidirectional', // Both directions
}

// Power flow animation properties
export interface PowerFlowAnimation {
  type: PowerFlowAnimationType;
  direction: PowerFlowDirection;
  speed: number; // Animation speed (1-10)
  color?: string; // Animation color (defaults to line color if not specified)
  enabled: boolean; // Whether the animation is enabled
}

// Gradient definition
export interface GradientDefinition {
  type: GradientType;
  startColor: string;
  endColor: string;
  angle?: number; // For linear gradient (in degrees)
  radius?: number; // For radial gradient
  centerX?: number; // For radial gradient (0-1)
  centerY?: number; // For radial gradient (0-1)
}

// Pattern definition
export interface PatternDefinition {
  type: PatternType;
  foregroundColor: string;
  backgroundColor: string;
  size?: number; // Size of the pattern elements
  spacing?: number; // Spacing between pattern elements
  rotation?: number; // Rotation angle in degrees
  customPattern?: string; // SVG path for custom pattern
}

// End marker definition
export interface EndMarkerDefinition {
  style: EndMarkerStyle;
  size: number;
  color?: string; // If not specified, uses the stroke color
  filled?: boolean;
  width?: number; // Width factor (1 = normal)
  height?: number; // Height factor (1 = normal)
  offset?: number; // Offset from the end of the line
  customPath?: string; // SVG path for custom marker
}

// Double line definition
export interface DoubleLineDefinition {
  enabled: boolean;
  gap: number; // Gap between the two lines
  innerColor?: string; // Color of the inner line (if not specified, uses the stroke color)
  innerWidth?: number; // Width of the inner line (if not specified, uses the line width)
  innerDash?: number[]; // Dash pattern for the inner line
}

// Hover style definition
export interface HoverStyleDefinition {
  strokeColor?: string;
  lineWidth?: number;
  lineOpacity?: number;
  shadowColor?: string;
  shadowBlur?: number;
  shadowOffset?: Position;
}

// Selected style definition
export interface SelectedStyleDefinition {
  strokeColor?: string;
  lineWidth?: number;
  lineOpacity?: number;
  shadowColor?: string;
  shadowBlur?: number;
  shadowOffset?: Position;
}

// Connection line style
export interface ConnectionStyle {
  strokeColor: string;
  lineWidth: number;
  lineDash?: number[]; // For dashed lines
  lineOpacity?: number; // For transparency
  shadowColor?: string; // For shadow effect
  shadowBlur?: number; // For shadow effect
  shadowOffset?: Position; // For shadow effect
  lineCap?: LineCapStyle; // Line cap style
  lineJoin?: LineJoinStyle; // Line join style
  jumpStyle?: JumpStyle; // Jump style for line intersections
  jumpSize?: number; // Size of the jump
  animation?: PowerFlowAnimation; // Power flow animation

  // Enhanced styling options
  gradient?: GradientDefinition; // Gradient fill
  pattern?: PatternDefinition; // Pattern fill
  sourceMarker?: EndMarkerDefinition; // Marker at the source end
  targetMarker?: EndMarkerDefinition; // Marker at the target end
  doubleLine?: DoubleLineDefinition; // Double line style
  hoverStyle?: HoverStyleDefinition; // Style when hovered
  selectedStyle?: SelectedStyleDefinition; // Style when selected
}

// Connection line type
export enum ConnectionLineType {
  STRAIGHT = 'straight',
  POLYLINE = 'polyline',
  BEZIER = 'bezier',
  SMART = 'smart', // New smart connection type that uses path finding
}

// Connection routing options
export interface ConnectionRoutingOptions {
  avoidObstacles: boolean; // Whether to avoid obstacles
  padding: number; // Padding around obstacles
  preferStraightLines: boolean; // Whether to prefer straight lines
  smoothingFactor: number; // Factor for path smoothing (0-1)
  routingStrategy: string; // Routing strategy (direct, orthogonal, manhattan, metro)
  cornerRadius: number; // Radius for rounded corners (0 for sharp corners)
  jumpOverCrossings: boolean; // Whether to create jumps at crossings
  snapToGrid: boolean; // Whether to snap waypoints to grid
  optimizePath: boolean; // Whether to optimize the path by removing unnecessary waypoints
  gridSize: number; // Size of each grid cell
  proximityThreshold?: number; // Distance threshold for switching to orthogonal routing (default: 150)
  minOrthogonalDistance?: number; // Minimum distance for direct connection (default: 30)
  interferenceThreshold?: number; // Distance threshold for detecting line interference with obstacles (default: 50)
}

// Connection theme interface
export interface ConnectionTheme {
  id: string;
  name: string;
  description?: string;
  style: ConnectionStyle;
  parentThemeId?: string; // For theme inheritance
  isBuiltIn?: boolean; // Whether this is a built-in theme
  isDefault?: boolean; // Whether this is the default theme
  applicableTypes?: ConnectionType[]; // Connection types this theme can be applied to
}

// Theme category
export enum ThemeCategory {
  GENERAL = 'general',
  ELECTRICAL = 'electrical',
  PROCESS = 'process',
  NETWORK = 'network',
  CUSTOM = 'custom',
}

// Label position along the connection
export enum LabelPosition {
  START = 'start',
  MIDDLE = 'middle',
  END = 'end',
  CUSTOM = 'custom', // Custom position specified by percentage
}

// Label alignment relative to the connection
export enum LabelAlignment {
  ABOVE = 'above',
  CENTER = 'center',
  BELOW = 'below',
}

// Connection label interface
export interface ConnectionLabel {
  id: string;
  text: string;
  position: LabelPosition;
  alignment: LabelAlignment;
  positionPercentage?: number; // For custom position (0-100)
  offset?: Position; // Offset from the calculated position
  style?: {
    fontFamily?: string;
    fontSize?: number;
    fontWeight?: string;
    fontStyle?: string;
    textColor?: string;
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    padding?: number;
    rotation?: number; // Rotation angle in degrees
  };
}

// Annotation type
export enum AnnotationType {
  TEXT = 'text',
  ICON = 'icon',
  ARROW = 'arrow',
  CUSTOM = 'custom',
}

// Connection annotation interface
export interface ConnectionAnnotation {
  id: string;
  type: AnnotationType;
  content: string; // Text content or icon name
  position: LabelPosition;
  positionPercentage?: number; // For custom position (0-100)
  offset?: Position; // Offset from the calculated position
  style?: {
    textColor?: string;
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    padding?: number;
    rotation?: number; // Rotation angle in degrees
    scale?: number; // Scale factor for icons
  };
}

// Connection interface
export interface Connection {
  id: string;
  type: ConnectionType;
  source: ConnectionEndpoint;
  target: ConnectionEndpoint;
  lineType: ConnectionLineType;
  waypoints?: Position[]; // For polyline connections
  controlPoints?: Position[]; // For bezier curves
  routingOptions?: ConnectionRoutingOptions; // Options for smart routing
  style: ConnectionStyle;
  labels?: ConnectionLabel[]; // Multiple labels
  annotations?: ConnectionAnnotation[]; // Annotations
  selected?: boolean; // UI state - whether the connection is selected

  // Backward compatibility
  label?: string; // Deprecated - use labels instead
}

// Default animation settings for different connection types
const defaultAnimations: Record<ConnectionType, PowerFlowAnimation> = {
  [ConnectionType.CABLE]: {
    type: PowerFlowAnimationType.FLOW,
    direction: PowerFlowDirection.FORWARD,
    speed: 5,
    enabled: false,
  },
  [ConnectionType.BUSBAR]: {
    type: PowerFlowAnimationType.PULSE,
    direction: PowerFlowDirection.BIDIRECTIONAL,
    speed: 3,
    enabled: false,
  },
  [ConnectionType.CONTROL]: {
    type: PowerFlowAnimationType.DASH,
    direction: PowerFlowDirection.FORWARD,
    speed: 7,
    enabled: false,
  },
  [ConnectionType.SIGNAL]: {
    type: PowerFlowAnimationType.FLOW,
    direction: PowerFlowDirection.FORWARD,
    speed: 8,
    enabled: false,
  },
};

// Default styles for different connection types
const defaultStyles: Record<ConnectionType, ConnectionStyle> = {
  [ConnectionType.CABLE]: {
    strokeColor: '#000000',
    lineWidth: 2,
    lineCap: LineCapStyle.ROUND, // Round caps for seamless connection point integration
    lineJoin: LineJoinStyle.ROUND, // Round joins for smooth orthogonal routing
    jumpStyle: JumpStyle.NONE,
    animation: defaultAnimations[ConnectionType.CABLE],
  },
  [ConnectionType.BUSBAR]: {
    strokeColor: '#ff0000',
    lineWidth: 3,
    lineCap: LineCapStyle.ROUND, // Changed to round for better visual integration
    lineJoin: LineJoinStyle.ROUND, // Changed to round for smoother corners
    jumpStyle: JumpStyle.NONE,
    animation: defaultAnimations[ConnectionType.BUSBAR],
  },
  [ConnectionType.CONTROL]: {
    strokeColor: '#0000ff',
    lineWidth: 1,
    lineDash: [5, 5],
    lineCap: LineCapStyle.ROUND, // Round caps for seamless connection point integration
    lineJoin: LineJoinStyle.ROUND, // Round joins for smooth orthogonal routing
    jumpStyle: JumpStyle.GAP,
    jumpSize: 8,
    animation: defaultAnimations[ConnectionType.CONTROL],
  },
  [ConnectionType.SIGNAL]: {
    strokeColor: '#00ff00',
    lineWidth: 1,
    lineDash: [2, 2],
    lineCap: LineCapStyle.ROUND, // Changed to round for better visual integration
    lineJoin: LineJoinStyle.ROUND, // Changed to round for smoother corners
    jumpStyle: JumpStyle.ARC,
    jumpSize: 5,
    animation: defaultAnimations[ConnectionType.SIGNAL],
  },
};

// Default routing options for smart connections
// Mandatory orthogonal routing with bend minimization for professional electrical schematic style
export const defaultRoutingOptions: ConnectionRoutingOptions = {
  avoidObstacles: true,
  padding: 15,
  preferStraightLines: false, // Changed to false since we prioritize orthogonal routing over straight lines
  smoothingFactor: 0.3,
  routingStrategy: 'orthogonal', // MANDATORY: All connections use orthogonal routing
  cornerRadius: 8,
  jumpOverCrossings: false,
  snapToGrid: false, // Grid snap disabled for optimal routing independence
  optimizePath: true,
  gridSize: 20, // Grid size parameter (not used when snapToGrid is false)
  proximityThreshold: 100, // Reduced threshold for close proximity handling
  minOrthogonalDistance: 30, // Minimum distance for direct orthogonal connection
  interferenceThreshold: 50, // Distance threshold for detecting line interference with obstacles
};

// Default label style
export const defaultLabelStyle = {
  fontFamily: 'Arial',
  fontSize: 12,
  fontWeight: 'normal',
  fontStyle: 'normal',
  textColor: '#000000',
  backgroundColor: '#ffffff',
  borderColor: '#cccccc',
  borderWidth: 1,
  borderRadius: 4,
  padding: 4,
};

// Default annotation style
export const defaultAnnotationStyle = {
  textColor: '#000000',
  backgroundColor: '#ffffff',
  borderColor: '#cccccc',
  borderWidth: 1,
  borderRadius: 4,
  padding: 4,
  scale: 1,
};

// Factory function to create a new connection
export function createConnection(
  id: string,
  type: ConnectionType,
  source: ConnectionEndpoint,
  target: ConnectionEndpoint,
  lineType: ConnectionLineType = ConnectionLineType.STRAIGHT,
  waypoints: Position[] = [],
  controlPoints: Position[] = [],
  style: Partial<ConnectionStyle> = {},
  label?: string,
  routingOptions?: Partial<ConnectionRoutingOptions>,
  labels?: ConnectionLabel[],
  annotations?: ConnectionAnnotation[]
): Connection {
  // Get the default style for the connection type
  const defaultStyle = defaultStyles[type];

  // Determine waypoints and control points based on line type
  const connectionWaypoints = lineType === ConnectionLineType.POLYLINE ? waypoints : [];
  const connectionControlPoints = lineType === ConnectionLineType.BEZIER ? controlPoints : [];

  // Set routing options for smart connections
  const connectionRoutingOptions = lineType === ConnectionLineType.SMART
    ? { ...defaultRoutingOptions, ...routingOptions }
    : undefined;

  // Create labels array if a single label is provided
  const connectionLabels = labels || (label ? [
    {
      id: `${id}-label-1`,
      text: label,
      position: LabelPosition.MIDDLE,
      alignment: LabelAlignment.ABOVE,
      style: { ...defaultLabelStyle },
    }
  ] : undefined);

  return {
    id,
    type,
    source,
    target,
    lineType,
    waypoints: connectionWaypoints,
    controlPoints: connectionControlPoints,
    routingOptions: connectionRoutingOptions,
    style: { ...defaultStyle, ...style },
    label, // Keep for backward compatibility
    labels: connectionLabels,
    annotations,
  };
}

/**
 * Create a new connection label
 * @param id Label ID
 * @param text Label text
 * @param position Label position
 * @param alignment Label alignment
 * @param style Label style
 * @returns Connection label
 */
export function createConnectionLabel(
  id: string,
  text: string,
  position: LabelPosition = LabelPosition.MIDDLE,
  alignment: LabelAlignment = LabelAlignment.ABOVE,
  positionPercentage?: number,
  offset?: Position,
  style?: Partial<ConnectionLabel['style']>
): ConnectionLabel {
  return {
    id,
    text,
    position,
    alignment,
    positionPercentage,
    offset,
    style: style ? { ...defaultLabelStyle, ...style } : { ...defaultLabelStyle },
  };
}

/**
 * Create a new connection annotation
 * @param id Annotation ID
 * @param type Annotation type
 * @param content Annotation content
 * @param position Annotation position
 * @param style Annotation style
 * @returns Connection annotation
 */
export function createConnectionAnnotation(
  id: string,
  type: AnnotationType,
  content: string,
  position: LabelPosition = LabelPosition.MIDDLE,
  positionPercentage?: number,
  offset?: Position,
  style?: Partial<ConnectionAnnotation['style']>
): ConnectionAnnotation {
  return {
    id,
    type,
    content,
    position,
    positionPercentage,
    offset,
    style: style ? { ...defaultAnnotationStyle, ...style } : { ...defaultAnnotationStyle },
  };
}
