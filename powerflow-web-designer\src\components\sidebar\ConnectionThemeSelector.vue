<template>
  <div class="connection-theme-selector">
    <h3>Connection Theme</h3>
    
    <!-- Theme selector -->
    <div class="theme-selector">
      <a-select
        v-model:value="selectedThemeId"
        style="width: 100%"
        placeholder="Select a theme"
        @change="handleThemeChange"
      >
        <a-select-option
          v-for="theme in availableThemes"
          :key="theme.id"
          :value="theme.id"
        >
          {{ theme.name }}
        </a-select-option>
      </a-select>
      
      <div class="theme-actions">
        <a-button
          type="primary"
          size="small"
          @click="showThemeEditor = true"
          title="Create new theme"
        >
          <template #icon><plus-outlined /></template>
        </a-button>
        
        <a-button
          type="primary"
          size="small"
          @click="handleEditTheme"
          :disabled="!canEditTheme"
          title="Edit theme"
        >
          <template #icon><edit-outlined /></template>
        </a-button>
        
        <a-button
          type="primary"
          size="small"
          @click="handleDuplicateTheme"
          :disabled="!selectedTheme"
          title="Duplicate theme"
        >
          <template #icon><copy-outlined /></template>
        </a-button>
        
        <a-button
          danger
          size="small"
          @click="handleDeleteTheme"
          :disabled="!canDeleteTheme"
          title="Delete theme"
        >
          <template #icon><delete-outlined /></template>
        </a-button>
      </div>
    </div>
    
    <!-- Theme preview -->
    <div class="theme-preview" v-if="selectedTheme">
      <h4>Preview</h4>
      <div class="preview-container">
        <div class="preview-line" :style="previewLineStyle"></div>
        
        <!-- Source marker -->
        <div 
          v-if="hasSourceMarker" 
          class="preview-marker preview-source-marker"
          :style="sourceMarkerStyle"
        ></div>
        
        <!-- Target marker -->
        <div 
          v-if="hasTargetMarker" 
          class="preview-marker preview-target-marker"
          :style="targetMarkerStyle"
        ></div>
      </div>
      
      <div class="theme-description" v-if="selectedTheme.description">
        {{ selectedTheme.description }}
      </div>
    </div>
    
    <!-- Theme editor modal -->
    <a-modal
      v-model:visible="showThemeEditor"
      :title="editingTheme ? 'Edit Theme' : 'Create Theme'"
      width="600px"
      @ok="saveTheme"
      @cancel="cancelThemeEdit"
    >
      <connection-theme-editor
        v-if="showThemeEditor"
        :theme="editingTheme"
        :connection-type="connectionType"
        @update:theme="updateEditingTheme"
      />
    </a-modal>
    
    <!-- Confirmation dialog -->
    <a-modal
      v-model:visible="showConfirmDialog"
      title="Confirm Delete"
      @ok="confirmDeleteTheme"
      @cancel="cancelDeleteTheme"
      :okButtonProps="{ danger: true }"
      okText="Delete"
    >
      <p>Are you sure you want to delete the theme "{{ selectedTheme?.name }}"?</p>
      <p>This action cannot be undone.</p>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { 
  PlusOutlined, 
  EditOutlined, 
  CopyOutlined, 
  DeleteOutlined 
} from '@ant-design/icons-vue';
import { 
  ConnectionTheme, 
  ConnectionType, 
  ConnectionStyle,
  EndMarkerStyle
} from '@/types/connection';
import { useDiagramStore } from '@/stores/diagram';
import { useThemeStore } from '@/stores/theme';
import { 
  getThemeById, 
  getThemesByConnectionType, 
  createTheme 
} from '@/utils/connectionThemes';
import ConnectionThemeEditor from './ConnectionThemeEditor.vue';

// Props
const props = defineProps<{
  connectionId: string;
  connectionType: ConnectionType;
  connectionStyle: ConnectionStyle;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:style', style: ConnectionStyle): void;
}>();

// Stores
const diagramStore = useDiagramStore();
const themeStore = useThemeStore();

// State
const selectedThemeId = ref<string>('');
const showThemeEditor = ref(false);
const editingTheme = ref<ConnectionTheme | null>(null);
const showConfirmDialog = ref(false);

// Computed
const availableThemes = computed(() => {
  return getThemesByConnectionType(props.connectionType, themeStore.themes);
});

const selectedTheme = computed(() => {
  if (!selectedThemeId.value) return null;
  return getThemeById(selectedThemeId.value, themeStore.themes);
});

const canEditTheme = computed(() => {
  return selectedTheme.value && !selectedTheme.value.isBuiltIn;
});

const canDeleteTheme = computed(() => {
  return selectedTheme.value && !selectedTheme.value.isBuiltIn;
});

const previewLineStyle = computed(() => {
  if (!selectedTheme.value) return {};
  
  const style = selectedTheme.value.style;
  
  return {
    backgroundColor: style.strokeColor,
    height: `${style.lineWidth}px`,
    opacity: style.lineOpacity || 1,
    borderStyle: style.lineDash ? 'dashed' : 'solid',
    borderWidth: style.lineDash ? '1px' : '0',
    borderColor: style.strokeColor,
    boxShadow: style.shadowColor ? `0 0 ${style.shadowBlur || 5}px ${style.shadowColor}` : 'none',
  };
});

const hasSourceMarker = computed(() => {
  return selectedTheme.value?.style.sourceMarker?.style !== EndMarkerStyle.NONE;
});

const hasTargetMarker = computed(() => {
  return selectedTheme.value?.style.targetMarker?.style !== EndMarkerStyle.NONE;
});

const sourceMarkerStyle = computed(() => {
  if (!selectedTheme.value || !selectedTheme.value.style.sourceMarker) return {};
  
  const marker = selectedTheme.value.style.sourceMarker;
  const size = marker.size || 8;
  
  return {
    width: `${size}px`,
    height: `${size}px`,
    backgroundColor: marker.color || selectedTheme.value.style.strokeColor,
    left: '0',
    transform: 'translateX(-50%)',
  };
});

const targetMarkerStyle = computed(() => {
  if (!selectedTheme.value || !selectedTheme.value.style.targetMarker) return {};
  
  const marker = selectedTheme.value.style.targetMarker;
  const size = marker.size || 8;
  
  return {
    width: `${size}px`,
    height: `${size}px`,
    backgroundColor: marker.color || selectedTheme.value.style.strokeColor,
    right: '0',
    transform: 'translateX(50%)',
  };
});

// Methods
const handleThemeChange = (themeId: string) => {
  if (!themeId) return;
  
  const theme = getThemeById(themeId, themeStore.themes);
  if (!theme) return;
  
  // Apply the theme to the connection style
  emit('update:style', { ...theme.style });
};

const handleEditTheme = () => {
  if (!selectedTheme.value || selectedTheme.value.isBuiltIn) return;
  
  editingTheme.value = { ...selectedTheme.value };
  showThemeEditor.value = true;
};

const handleDuplicateTheme = () => {
  if (!selectedTheme.value) return;
  
  const newTheme = createTheme(
    `${selectedTheme.value.name} (Copy)`,
    { ...selectedTheme.value.style },
    selectedTheme.value.description,
    undefined,
    selectedTheme.value.applicableTypes
  );
  
  editingTheme.value = newTheme;
  showThemeEditor.value = true;
};

const handleDeleteTheme = () => {
  if (!selectedTheme.value || selectedTheme.value.isBuiltIn) return;
  
  showConfirmDialog.value = true;
};

const updateEditingTheme = (theme: ConnectionTheme) => {
  editingTheme.value = theme;
};

const saveTheme = () => {
  if (!editingTheme.value) return;
  
  // Save the theme
  themeStore.saveTheme(editingTheme.value);
  
  // Select the new/edited theme
  selectedThemeId.value = editingTheme.value.id;
  
  // Apply the theme to the connection style
  emit('update:style', { ...editingTheme.value.style });
  
  // Close the editor
  showThemeEditor.value = false;
  editingTheme.value = null;
};

const cancelThemeEdit = () => {
  showThemeEditor.value = false;
  editingTheme.value = null;
};

const confirmDeleteTheme = () => {
  if (!selectedTheme.value || selectedTheme.value.isBuiltIn) return;
  
  // Delete the theme
  themeStore.deleteTheme(selectedTheme.value.id);
  
  // Reset the selected theme
  selectedThemeId.value = '';
  
  // Close the confirmation dialog
  showConfirmDialog.value = false;
};

const cancelDeleteTheme = () => {
  showConfirmDialog.value = false;
};

// Initialize
onMounted(() => {
  // Try to find a matching theme for the current connection style
  const matchingTheme = availableThemes.value.find(theme => {
    // Simple matching based on stroke color and line width
    return (
      theme.style.strokeColor === props.connectionStyle.strokeColor &&
      theme.style.lineWidth === props.connectionStyle.lineWidth
    );
  });
  
  if (matchingTheme) {
    selectedThemeId.value = matchingTheme.id;
  }
});
</script>

<style scoped>
.connection-theme-selector {
  margin-bottom: 16px;
}

.theme-selector {
  margin-bottom: 16px;
}

.theme-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.theme-preview {
  margin-top: 16px;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.preview-container {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  margin: 16px 0;
}

.preview-line {
  width: 100%;
  height: 2px;
  background-color: #000;
}

.preview-marker {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #000;
  border-radius: 50%;
}

.theme-description {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}
</style>
