<template>
  <div class="collapsible-sidebar" :class="{ collapsed }">
    <div class="sidebar-content">
      <slot></slot>
    </div>
    <div class="sidebar-toggle" @click="toggleCollapse">
      <a-button type="text" size="small">
        <template #icon>
          <left-outlined v-if="!collapsed" />
          <right-outlined v-else />
        </template>
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';

// Props
const props = defineProps<{
  side?: 'left' | 'right';
  defaultCollapsed?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'collapse', collapsed: boolean): void;
}>();

// State
const collapsed = ref(props.defaultCollapsed || false);

// Methods
const toggleCollapse = () => {
  collapsed.value = !collapsed.value;
  emit('collapse', collapsed.value);
};
</script>

<style scoped>
.collapsible-sidebar {
  position: relative;
  height: 100%;
  transition: width 0.3s;
  background-color: #fff;
  overflow: hidden;
}

.collapsible-sidebar.collapsed {
  width: 24px !important;
}

.sidebar-content {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.sidebar-toggle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  z-index: 10;
}

.collapsible-sidebar[side="left"] .sidebar-toggle {
  right: 0;
  border-radius: 0 4px 4px 0;
}

.collapsible-sidebar[side="right"] .sidebar-toggle {
  left: 0;
  border-radius: 4px 0 0 4px;
}
</style>
