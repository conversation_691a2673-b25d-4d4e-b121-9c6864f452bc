import { test, expect } from '@playwright/test';
import { 
  navigateToEditor, 
  createNewDiagram, 
  addSymbolToDiagram, 
  createConnection,
  selectSymbol,
  openPropertyPanel,
  createGroup
} from './helpers';

test.describe('Editor Functionality', () => {
  test('should create a new diagram', async ({ page }) => {
    await navigateToEditor(page);
    
    // Click the New button in the toolbar
    await page.getByRole('button', { name: 'New' }).click();
    
    // Fill in the diagram name
    await page.getByLabel('Diagram Name').fill('Test Diagram');
    
    // Click the Create button
    await page.getByRole('button', { name: 'Create' }).click();
    
    // Check that the diagram is created
    await expect(page.locator('.diagram-canvas')).toBeVisible();
  });

  test('should add a symbol to the diagram', async ({ page }) => {
    await createNewDiagram(page, 'Symbol Test Diagram');
    
    // Count initial symbols
    const initialSymbolCount = await page.locator('.symbol-instance').count();
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Check that a new symbol was added
    await expect(page.locator('.symbol-instance')).toHaveCount(initialSymbolCount + 1);
  });

  test('should select a symbol and show properties', async ({ page }) => {
    await createNewDiagram(page, 'Selection Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Select the symbol
    await selectSymbol(page, 0);
    
    // Check that the property panel shows the symbol properties
    await expect(page.locator('.property-panel')).toBeVisible();
    await expect(page.locator('.property-panel')).toContainText('Circuit Breaker');
  });

  test('should create a connection between symbols', async ({ page }) => {
    await createNewDiagram(page, 'Connection Test Diagram');
    
    // Add two symbols
    await addSymbolToDiagram(page, 'Circuit Breaker');
    await addSymbolToDiagram(page, 'Transformer');
    
    // Count initial connections
    const initialConnectionCount = await page.locator('.connection').count();
    
    // Create a connection
    await createConnection(page, 0, 1);
    
    // Check that a new connection was created
    await expect(page.locator('.connection')).toHaveCount(initialConnectionCount + 1);
  });

  test('should create a group of symbols', async ({ page }) => {
    await createNewDiagram(page, 'Group Test Diagram');
    
    // Add three symbols
    await addSymbolToDiagram(page, 'Circuit Breaker');
    await addSymbolToDiagram(page, 'Transformer');
    await addSymbolToDiagram(page, 'Generator');
    
    // Count initial groups
    const initialGroupCount = await page.locator('.symbol-group').count();
    
    // Create a group
    await createGroup(page, [0, 1, 2]);
    
    // Check that a new group was created
    await expect(page.locator('.symbol-group')).toHaveCount(initialGroupCount + 1);
  });

  test('should delete a symbol', async ({ page }) => {
    await createNewDiagram(page, 'Delete Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Count symbols
    const symbolCount = await page.locator('.symbol-instance').count();
    
    // Select the symbol
    await selectSymbol(page, 0);
    
    // Press Delete key
    await page.keyboard.press('Delete');
    
    // Check that the symbol was deleted
    await expect(page.locator('.symbol-instance')).toHaveCount(symbolCount - 1);
  });

  test('should use undo and redo functionality', async ({ page }) => {
    await createNewDiagram(page, 'Undo Redo Test Diagram');
    
    // Add a symbol
    await addSymbolToDiagram(page, 'Circuit Breaker');
    
    // Count symbols
    const symbolCount = await page.locator('.symbol-instance').count();
    expect(symbolCount).toBeGreaterThan(0);
    
    // Undo the addition
    await page.getByRole('button', { name: 'Undo' }).click();
    
    // Check that the symbol was removed
    await expect(page.locator('.symbol-instance')).toHaveCount(symbolCount - 1);
    
    // Redo the addition
    await page.getByRole('button', { name: 'Redo' }).click();
    
    // Check that the symbol was added back
    await expect(page.locator('.symbol-instance')).toHaveCount(symbolCount);
  });
});
