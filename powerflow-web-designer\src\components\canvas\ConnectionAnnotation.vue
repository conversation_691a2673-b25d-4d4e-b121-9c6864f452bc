<template>
  <!-- Text annotation -->
  <template v-if="annotation.type === AnnotationType.TEXT">
    <v-label
      :config="{
        x: position.x,
        y: position.y,
        text: annotation.content,
        fill: annotationStyle.textColor,
        padding: annotationStyle.padding,
        rotation: annotationStyle.rotation || 0,
        cornerRadius: annotationStyle.borderRadius,
        align: 'center',
        verticalAlign: 'middle',
        width: 'auto',
        height: 'auto',
        listening: true,
        draggable: !readOnly,
      }"
      @dragstart="handleDragStart"
      @dragmove="handleDragMove"
      @dragend="handleDragEnd"
      @click="handleClick"
      @dblclick="handleDoubleClick"
    />
    <v-tag
      v-if="annotationStyle.backgroundColor || annotationStyle.borderColor"
      :config="{
        x: position.x,
        y: position.y,
        fill: annotationStyle.backgroundColor,
        stroke: annotationStyle.borderColor,
        strokeWidth: annotationStyle.borderWidth,
        cornerRadius: annotationStyle.borderRadius,
        width: annotationWidth,
        height: annotationHeight,
        rotation: annotationStyle.rotation || 0,
        offsetX: annotationWidth / 2,
        offsetY: annotationHeight / 2,
        shadowColor: selected ? '#1890ff' : undefined,
        shadowBlur: selected ? 5 : undefined,
        shadowOffset: selected ? { x: 0, y: 0 } : undefined,
        shadowOpacity: selected ? 0.5 : undefined,
        listening: false,
      }"
    />
  </template>
  
  <!-- Arrow annotation -->
  <template v-else-if="annotation.type === AnnotationType.ARROW">
    <v-arrow
      :config="{
        x: position.x,
        y: position.y,
        points: arrowPoints,
        pointerLength: 10,
        pointerWidth: 10,
        fill: annotationStyle.backgroundColor,
        stroke: annotationStyle.borderColor,
        strokeWidth: annotationStyle.borderWidth,
        rotation: annotationStyle.rotation || 0,
        shadowColor: selected ? '#1890ff' : undefined,
        shadowBlur: selected ? 5 : undefined,
        shadowOffset: selected ? { x: 0, y: 0 } : undefined,
        shadowOpacity: selected ? 0.5 : undefined,
        draggable: !readOnly,
        listening: true,
      }"
      @dragstart="handleDragStart"
      @dragmove="handleDragMove"
      @dragend="handleDragEnd"
      @click="handleClick"
      @dblclick="handleDoubleClick"
    />
  </template>
  
  <!-- Icon annotation -->
  <template v-else-if="annotation.type === AnnotationType.ICON">
    <v-label
      :config="{
        x: position.x,
        y: position.y,
        text: getIconUnicode(annotation.content),
        fontFamily: 'FontAwesome',
        fontSize: 16 * (annotationStyle.scale || 1),
        fill: annotationStyle.textColor,
        padding: annotationStyle.padding,
        rotation: annotationStyle.rotation || 0,
        align: 'center',
        verticalAlign: 'middle',
        width: 'auto',
        height: 'auto',
        listening: true,
        draggable: !readOnly,
      }"
      @dragstart="handleDragStart"
      @dragmove="handleDragMove"
      @dragend="handleDragEnd"
      @click="handleClick"
      @dblclick="handleDoubleClick"
    />
    <v-circle
      v-if="annotationStyle.backgroundColor || annotationStyle.borderColor"
      :config="{
        x: position.x,
        y: position.y,
        radius: 12 * (annotationStyle.scale || 1),
        fill: annotationStyle.backgroundColor,
        stroke: annotationStyle.borderColor,
        strokeWidth: annotationStyle.borderWidth,
        shadowColor: selected ? '#1890ff' : undefined,
        shadowBlur: selected ? 5 : undefined,
        shadowOffset: selected ? { x: 0, y: 0 } : undefined,
        shadowOpacity: selected ? 0.5 : undefined,
        listening: false,
      }"
    />
  </template>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ConnectionAnnotation, AnnotationType, LabelPosition } from '@/types/connection';
import { Position } from '@/types/symbol';

// Props
const props = defineProps<{
  annotation: ConnectionAnnotation;
  points: Position[];
  readOnly?: boolean;
  selected?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:annotation', annotation: ConnectionAnnotation): void;
  (e: 'select', annotationId: string): void;
  (e: 'edit', annotationId: string): void;
}>();

// State
const position = ref<Position>({ x: 0, y: 0 });
const annotationWidth = ref(0);
const annotationHeight = ref(0);
const dragStartPosition = ref<Position | null>(null);

// Computed
const annotationStyle = computed(() => {
  return {
    textColor: props.annotation.style?.textColor || '#000000',
    backgroundColor: props.annotation.style?.backgroundColor || '#ffffff',
    borderColor: props.annotation.style?.borderColor || '#cccccc',
    borderWidth: props.annotation.style?.borderWidth || 1,
    borderRadius: props.annotation.style?.borderRadius || 4,
    padding: props.annotation.style?.padding || 4,
    rotation: props.annotation.style?.rotation || 0,
    scale: props.annotation.style?.scale || 1,
  };
});

const arrowPoints = computed(() => {
  // Calculate arrow points based on position and direction
  const scale = annotationStyle.value.scale || 1;
  const length = 20 * scale;
  
  return [0, 0, length, 0];
});

// Calculate the position of the annotation based on the connection points
const calculateAnnotationPosition = () => {
  if (!props.points || props.points.length < 2) {
    return { x: 0, y: 0 };
  }

  let pos: Position;
  const points = props.points;

  // Calculate position based on annotation position type
  switch (props.annotation.position) {
    case LabelPosition.START:
      pos = calculatePositionAtPercentage(0);
      break;
    case LabelPosition.END:
      pos = calculatePositionAtPercentage(100);
      break;
    case LabelPosition.CUSTOM:
      pos = calculatePositionAtPercentage(props.annotation.positionPercentage || 50);
      break;
    case LabelPosition.MIDDLE:
    default:
      pos = calculatePositionAtPercentage(50);
      break;
  }

  // Apply custom offset if provided
  if (props.annotation.offset) {
    pos.x += props.annotation.offset.x;
    pos.y += props.annotation.offset.y;
  }

  return pos;
};

// Calculate position at a specific percentage along the connection
const calculatePositionAtPercentage = (percentage: number): Position => {
  const points = props.points;
  if (!points || points.length < 2) {
    return { x: 0, y: 0 };
  }

  // Calculate total length of the connection
  let totalLength = 0;
  const segmentLengths: number[] = [];

  for (let i = 0; i < points.length - 1; i++) {
    const length = calculateDistance(points[i], points[i + 1]);
    segmentLengths.push(length);
    totalLength += length;
  }

  // Calculate target distance
  const targetDistance = (percentage / 100) * totalLength;

  // Find the segment and position
  let currentDistance = 0;
  for (let i = 0; i < segmentLengths.length; i++) {
    const segmentLength = segmentLengths[i];
    
    if (currentDistance + segmentLength >= targetDistance) {
      // Calculate position within this segment
      const segmentPercentage = (targetDistance - currentDistance) / segmentLength;
      const start = points[i];
      const end = points[i + 1];
      
      return {
        x: start.x + (end.x - start.x) * segmentPercentage,
        y: start.y + (end.y - start.y) * segmentPercentage,
      };
    }
    
    currentDistance += segmentLength;
  }

  // Fallback to the last point
  return points[points.length - 1];
};

// Calculate distance between two points
const calculateDistance = (p1: Position, p2: Position): number => {
  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
};

// Get Unicode for FontAwesome icon
const getIconUnicode = (iconName: string): string => {
  // Map of common FontAwesome icons to their Unicode values
  const iconMap: Record<string, string> = {
    'arrow-right': '\uf061',
    'arrow-left': '\uf060',
    'arrow-up': '\uf062',
    'arrow-down': '\uf063',
    'circle': '\uf111',
    'square': '\uf0c8',
    'triangle': '\uf0d8',
    'star': '\uf005',
    'check': '\uf00c',
    'times': '\uf00d',
    'info': '\uf129',
    'warning': '\uf071',
    'error': '\uf06a',
    'question': '\uf128',
  };
  
  return iconMap[iconName] || '\uf111'; // Default to circle
};

// Event handlers
const handleDragStart = (e: any) => {
  if (props.readOnly) return;
  
  dragStartPosition.value = { ...position.value };
};

const handleDragMove = (e: any) => {
  if (props.readOnly) return;
  
  const newPos = { x: e.target.x(), y: e.target.y() };
  position.value = newPos;
};

const handleDragEnd = (e: any) => {
  if (props.readOnly) return;
  
  const newPos = { x: e.target.x(), y: e.target.y() };
  
  // Calculate the offset from the original position
  if (dragStartPosition.value) {
    const dx = newPos.x - dragStartPosition.value.x;
    const dy = newPos.y - dragStartPosition.value.y;
    
    // Update the annotation with the new offset
    const updatedAnnotation = { 
      ...props.annotation,
      offset: { 
        x: (props.annotation.offset?.x || 0) + dx,
        y: (props.annotation.offset?.y || 0) + dy,
      }
    };
    
    emit('update:annotation', updatedAnnotation);
  }
  
  dragStartPosition.value = null;
};

const handleClick = (e: any) => {
  emit('select', props.annotation.id);
};

const handleDoubleClick = (e: any) => {
  emit('edit', props.annotation.id);
};

// Calculate initial position
onMounted(() => {
  position.value = calculateAnnotationPosition();
  
  // Estimate annotation dimensions based on content
  if (props.annotation.type === AnnotationType.TEXT) {
    const textLength = props.annotation.content.length;
    const padding = annotationStyle.value.padding;
    
    annotationWidth.value = textLength * 8 + padding * 2;
    annotationHeight.value = 16 + padding * 2;
  } else {
    annotationWidth.value = 24;
    annotationHeight.value = 24;
  }
});

// Watch for changes in points or annotation
watch(() => props.points, () => {
  position.value = calculateAnnotationPosition();
}, { deep: true });

watch(() => props.annotation, () => {
  position.value = calculateAnnotationPosition();
  
  // Update annotation dimensions
  if (props.annotation.type === AnnotationType.TEXT) {
    const textLength = props.annotation.content.length;
    const padding = annotationStyle.value.padding;
    
    annotationWidth.value = textLength * 8 + padding * 2;
    annotationHeight.value = 16 + padding * 2;
  }
}, { deep: true });
</script>
