[{"id": "server-rack", "category": "datacenter", "name": "机柜", "svg": "<svg viewBox=\"0 0 100 120\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"20\" y=\"10\" width=\"60\" height=\"100\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"25\" y1=\"20\" x2=\"75\" y2=\"20\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"25\" y1=\"30\" x2=\"75\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"25\" y1=\"40\" x2=\"75\" y2=\"40\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"25\" y1=\"50\" x2=\"75\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"25\" y1=\"60\" x2=\"75\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"25\" y1=\"70\" x2=\"75\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"25\" y1=\"80\" x2=\"75\" y2=\"80\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"25\" y1=\"90\" x2=\"75\" y2=\"90\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"25\" y1=\"100\" x2=\"75\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"0\" y1=\"60\" x2=\"20\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"60\" x2=\"100\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"118\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">机柜</text></svg>", "dimensions": {"width": 100, "height": 120}, "connectionPoints": [{"id": "power_in", "position": {"x": 0, "y": 60}, "type": "input", "label": "电源输入"}, {"id": "power_out", "position": {"x": 100, "y": 60}, "type": "output", "label": "电源输出"}], "bindingSlots": [{"id": "power_consumption", "name": "功耗", "dataType": "number", "defaultValue": 0, "description": "机柜功耗 (kW)"}, {"id": "temperature", "name": "温度", "dataType": "number", "defaultValue": 25, "description": "机柜温度 (°C)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "hvdc-system", "category": "datacenter", "name": "高压直流", "svg": "<svg viewBox=\"0 0 120 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"20\" y=\"30\" width=\"80\" height=\"40\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"40\" x2=\"20\" y2=\"40\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"60\" x2=\"20\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"100\" y1=\"40\" x2=\"120\" y2=\"40\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"100\" y1=\"60\" x2=\"120\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"60\" y=\"45\" font-size=\"12\" text-anchor=\"middle\" fill=\"currentColor\">HVDC</text><text x=\"60\" y=\"60\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">高压直流</text><line x1=\"10\" y1=\"35\" x2=\"15\" y2=\"35\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"10\" y1=\"45\" x2=\"15\" y2=\"45\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"5\" y1=\"55\" x2=\"15\" y2=\"55\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"10\" y1=\"65\" x2=\"15\" y2=\"65\" stroke=\"currentColor\" stroke-width=\"2\"/></svg>", "dimensions": {"width": 120, "height": 100}, "connectionPoints": [{"id": "ac_in_l1", "position": {"x": 0, "y": 40}, "type": "input", "label": "交流输入L1"}, {"id": "ac_in_l2", "position": {"x": 0, "y": 60}, "type": "input", "label": "交流输入L2"}, {"id": "dc_out_pos", "position": {"x": 120, "y": 40}, "type": "output", "label": "直流输出正极"}, {"id": "dc_out_neg", "position": {"x": 120, "y": 60}, "type": "output", "label": "直流输出负极"}], "bindingSlots": [{"id": "input_voltage", "name": "输入电压", "dataType": "number", "defaultValue": 380, "description": "交流输入电压 (V)"}, {"id": "output_voltage", "name": "输出电压", "dataType": "number", "defaultValue": 240, "description": "直流输出电压 (V)"}, {"id": "efficiency", "name": "效率", "dataType": "number", "defaultValue": 95, "description": "转换效率 (%)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "ups", "category": "datacenter", "name": "不间断电源", "svg": "<svg viewBox=\"0 0 120 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"15\" y=\"25\" width=\"90\" height=\"50\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"50\" x2=\"15\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"105\" y1=\"50\" x2=\"120\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"60\" y=\"45\" font-size=\"14\" text-anchor=\"middle\" fill=\"currentColor\">UPS</text><text x=\"60\" y=\"60\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">不间断电源</text><rect x=\"25\" y=\"35\" width=\"15\" height=\"20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"45\" y=\"35\" width=\"15\" height=\"20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"65\" y=\"35\" width=\"15\" height=\"20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"32\" y1=\"32\" x2=\"32\" y2=\"35\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"52\" y1=\"32\" x2=\"52\" y2=\"35\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"72\" y1=\"32\" x2=\"72\" y2=\"35\" stroke=\"currentColor\" stroke-width=\"2\"/></svg>", "dimensions": {"width": 120, "height": 100}, "connectionPoints": [{"id": "ac_in", "position": {"x": 0, "y": 50}, "type": "input", "label": "交流输入"}, {"id": "ac_out", "position": {"x": 120, "y": 50}, "type": "output", "label": "交流输出"}], "bindingSlots": [{"id": "battery_level", "name": "电池电量", "dataType": "number", "defaultValue": 100, "description": "电池电量百分比 (%)"}, {"id": "load_percentage", "name": "负载率", "dataType": "number", "defaultValue": 0, "description": "负载百分比 (%)"}, {"id": "status", "name": "状态", "dataType": "string", "defaultValue": "normal", "description": "UPS运行状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "air-conditioning", "category": "datacenter", "name": "空调", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"10\" y=\"20\" width=\"80\" height=\"60\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><circle cx=\"50\" cy=\"50\" r=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 35 50 Q 50 35 65 50 Q 50 65 35 50\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"50\" y1=\"0\" x2=\"50\" y2=\"20\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"80\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"95\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">空调</text><path d=\"M 20 30 L 25 25 L 30 30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 70 30 L 75 25 L 80 30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 20 70 L 25 75 L 30 70\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 70 70 L 75 75 L 80 70\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "power_in", "position": {"x": 50, "y": 0}, "type": "input", "label": "电源输入"}, {"id": "control", "position": {"x": 50, "y": 100}, "type": "input", "label": "控制信号"}], "bindingSlots": [{"id": "temperature_setpoint", "name": "设定温度", "dataType": "number", "defaultValue": 22, "description": "设定温度 (°C)"}, {"id": "current_temperature", "name": "当前温度", "dataType": "number", "defaultValue": 25, "description": "当前温度 (°C)"}, {"id": "humidity", "name": "湿度", "dataType": "number", "defaultValue": 50, "description": "相对湿度 (%)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "distribution-panel", "category": "datacenter", "name": "配电柜", "svg": "<svg viewBox=\"0 0 100 120\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"15\" y=\"10\" width=\"70\" height=\"100\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"30\" x2=\"15\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"50\" x2=\"15\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"70\" x2=\"15\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"85\" y1=\"30\" x2=\"100\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"85\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"85\" y1=\"70\" x2=\"100\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"85\" y1=\"90\" x2=\"100\" y2=\"90\" stroke=\"currentColor\" stroke-width=\"3\"/><rect x=\"25\" y=\"20\" width=\"15\" height=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"45\" y=\"20\" width=\"15\" height=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"65\" y=\"20\" width=\"15\" height=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"25\" y=\"40\" width=\"15\" height=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"45\" y=\"40\" width=\"15\" height=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"65\" y=\"40\" width=\"15\" height=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"25\" y=\"60\" width=\"15\" height=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"45\" y=\"60\" width=\"15\" height=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"65\" y=\"60\" width=\"15\" height=\"15\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><text x=\"50\" y=\"115\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">配电柜</text></svg>", "dimensions": {"width": 100, "height": 120}, "connectionPoints": [{"id": "main_in_l1", "position": {"x": 0, "y": 30}, "type": "input", "label": "主进线L1"}, {"id": "main_in_l2", "position": {"x": 0, "y": 50}, "type": "input", "label": "主进线L2"}, {"id": "main_in_l3", "position": {"x": 0, "y": 70}, "type": "input", "label": "主进线L3"}, {"id": "out_1", "position": {"x": 100, "y": 30}, "type": "output", "label": "输出1"}, {"id": "out_2", "position": {"x": 100, "y": 50}, "type": "output", "label": "输出2"}, {"id": "out_3", "position": {"x": 100, "y": 70}, "type": "output", "label": "输出3"}, {"id": "out_4", "position": {"x": 100, "y": 90}, "type": "output", "label": "输出4"}], "bindingSlots": [{"id": "total_current", "name": "总电流", "dataType": "number", "defaultValue": 0, "description": "总电流 (A)"}, {"id": "voltage", "name": "电压", "dataType": "number", "defaultValue": 380, "description": "线电压 (V)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "battery-bank", "category": "datacenter", "name": "电池组", "svg": "<svg viewBox=\"0 0 120 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"10\" y=\"30\" width=\"25\" height=\"40\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><rect x=\"40\" y=\"30\" width=\"25\" height=\"40\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><rect x=\"70\" y=\"30\" width=\"25\" height=\"40\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"22\" y1=\"25\" x2=\"22\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"52\" y1=\"25\" x2=\"52\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"82\" y1=\"25\" x2=\"82\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"19\" y1=\"27\" x2=\"25\" y2=\"27\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"49\" y1=\"27\" x2=\"55\" y2=\"27\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"79\" y1=\"27\" x2=\"85\" y2=\"27\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"0\" y1=\"50\" x2=\"10\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"35\" y1=\"50\" x2=\"40\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"65\" y1=\"50\" x2=\"70\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"95\" y1=\"50\" x2=\"120\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"60\" y=\"85\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">电池组</text></svg>", "dimensions": {"width": 120, "height": 100}, "connectionPoints": [{"id": "positive", "position": {"x": 0, "y": 50}, "type": "output", "label": "正极"}, {"id": "negative", "position": {"x": 120, "y": 50}, "type": "output", "label": "负极"}], "bindingSlots": [{"id": "voltage", "name": "电压", "dataType": "number", "defaultValue": 48, "description": "电池组电压 (V)"}, {"id": "capacity", "name": "容量", "dataType": "number", "defaultValue": 100, "description": "电池容量 (Ah)"}, {"id": "charge_level", "name": "电量", "dataType": "number", "defaultValue": 100, "description": "电池电量 (%)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "mains-power-input", "category": "datacenter", "name": "市电进线", "svg": "<svg viewBox=\"0 0 100 120\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"20\" y1=\"20\" x2=\"80\" y2=\"20\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"20\" y1=\"30\" x2=\"80\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"20\" y1=\"40\" x2=\"80\" y2=\"40\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"0\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"40\" y1=\"30\" x2=\"40\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"60\" y1=\"30\" x2=\"60\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"40\" x2=\"50\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"30\" y1=\"60\" x2=\"70\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"35\" y1=\"70\" x2=\"65\" y2=\"70\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"40\" y1=\"80\" x2=\"60\" y2=\"80\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"45\" y1=\"90\" x2=\"55\" y2=\"90\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"90\" x2=\"50\" y2=\"120\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"85\" y=\"35\" font-size=\"10\" text-anchor=\"start\" fill=\"currentColor\">市电</text><text x=\"85\" y=\"45\" font-size=\"10\" text-anchor=\"start\" fill=\"currentColor\">进线</text></svg>", "dimensions": {"width": 100, "height": 120}, "connectionPoints": [{"id": "grid_in", "position": {"x": 50, "y": 0}, "type": "input", "label": "电网输入"}, {"id": "l1_out", "position": {"x": 40, "y": 50}, "type": "output", "label": "L1输出"}, {"id": "l2_out", "position": {"x": 60, "y": 50}, "type": "output", "label": "L2输出"}, {"id": "ground", "position": {"x": 50, "y": 120}, "type": "output", "label": "接地"}], "bindingSlots": [{"id": "voltage", "name": "电压", "dataType": "number", "defaultValue": 380, "description": "进线电压 (V)"}, {"id": "frequency", "name": "频率", "dataType": "number", "defaultValue": 50, "description": "电网频率 (Hz)"}, {"id": "current", "name": "电流", "dataType": "number", "defaultValue": 0, "description": "进线电流 (A)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "switching-power-supply", "category": "datacenter", "name": "开关电源", "svg": "<svg viewBox=\"0 0 120 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"15\" y=\"25\" width=\"90\" height=\"50\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"50\" x2=\"15\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"105\" y1=\"40\" x2=\"120\" y2=\"40\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"105\" y1=\"60\" x2=\"120\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"60\" y=\"45\" font-size=\"12\" text-anchor=\"middle\" fill=\"currentColor\">SMPS</text><text x=\"60\" y=\"60\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">开关电源</text><path d=\"M 25 35 L 35 35 L 30 45 L 40 45 L 35 55 L 45 55\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><rect x=\"55\" y=\"35\" width=\"20\" height=\"20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"65\" y1=\"35\" x2=\"65\" y2=\"55\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"80\" y1=\"40\" x2=\"95\" y2=\"40\" stroke=\"currentColor\" stroke-width=\"2\"/><line x1=\"80\" y1=\"50\" x2=\"95\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"2\"/></svg>", "dimensions": {"width": 120, "height": 100}, "connectionPoints": [{"id": "ac_in", "position": {"x": 0, "y": 50}, "type": "input", "label": "交流输入"}, {"id": "dc_out_pos", "position": {"x": 120, "y": 40}, "type": "output", "label": "直流输出+"}, {"id": "dc_out_neg", "position": {"x": 120, "y": 60}, "type": "output", "label": "直流输出-"}], "bindingSlots": [{"id": "input_voltage", "name": "输入电压", "dataType": "number", "defaultValue": 220, "description": "交流输入电压 (V)"}, {"id": "output_voltage", "name": "输出电压", "dataType": "number", "defaultValue": 48, "description": "直流输出电压 (V)"}, {"id": "efficiency", "name": "效率", "dataType": "number", "defaultValue": 90, "description": "转换效率 (%)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "lighting-equipment", "category": "datacenter", "name": "照明设备", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"10\" y=\"30\" width=\"80\" height=\"40\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"0\" x2=\"50\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"70\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/><circle cx=\"30\" cy=\"50\" r=\"8\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><circle cx=\"50\" cy=\"50\" r=\"8\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><circle cx=\"70\" cy=\"50\" r=\"8\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 22 42 L 38 58\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 38 42 L 22 58\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 42 42 L 58 58\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 58 42 L 42 58\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 62 42 L 78 58\" stroke=\"currentColor\" stroke-width=\"2\"/><path d=\"M 78 42 L 62 58\" stroke=\"currentColor\" stroke-width=\"2\"/><text x=\"50\" y=\"95\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">照明</text></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "power_in", "position": {"x": 50, "y": 0}, "type": "input", "label": "电源输入"}, {"id": "control", "position": {"x": 50, "y": 100}, "type": "input", "label": "控制信号"}], "bindingSlots": [{"id": "power_consumption", "name": "功耗", "dataType": "number", "defaultValue": 50, "description": "照明功耗 (W)"}, {"id": "brightness", "name": "亮度", "dataType": "number", "defaultValue": 100, "description": "亮度百分比 (%)"}, {"id": "status", "name": "状态", "dataType": "boolean", "defaultValue": true, "description": "照明开关状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "ats", "category": "datacenter", "name": "自动转换开关", "svg": "<svg viewBox=\"0 0 140 120\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"20\" y=\"30\" width=\"100\" height=\"60\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"45\" x2=\"20\" y2=\"45\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"0\" y1=\"75\" x2=\"20\" y2=\"75\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"120\" y1=\"60\" x2=\"140\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"3\"/><circle cx=\"35\" cy=\"45\" r=\"4\" fill=\"currentColor\"/><circle cx=\"35\" cy=\"75\" r=\"4\" fill=\"currentColor\"/><circle cx=\"105\" cy=\"60\" r=\"4\" fill=\"currentColor\"/><line x1=\"39\" y1=\"45\" x2=\"85\" y2=\"55\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"85\" y1=\"55\" x2=\"101\" y2=\"60\" stroke=\"currentColor\" stroke-width=\"3\"/><path d=\"M 80 50 L 90 45 L 90 55 Z\" fill=\"currentColor\"/><text x=\"70\" y=\"25\" font-size=\"12\" text-anchor=\"middle\" fill=\"currentColor\">ATS</text><text x=\"70\" y=\"110\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">自动转换开关</text><text x=\"10\" y=\"40\" font-size=\"8\" text-anchor=\"middle\" fill=\"currentColor\">主</text><text x=\"10\" y=\"85\" font-size=\"8\" text-anchor=\"middle\" fill=\"currentColor\">备</text><rect x=\"45\" y=\"35\" width=\"50\" height=\"20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-dasharray=\"3,2\"/><text x=\"70\" y=\"47\" font-size=\"8\" text-anchor=\"middle\" fill=\"currentColor\">控制器</text><line x1=\"70\" y1=\"0\" x2=\"70\" y2=\"30\" stroke=\"currentColor\" stroke-width=\"2\"/><circle cx=\"70\" cy=\"5\" r=\"3\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/></svg>", "dimensions": {"width": 140, "height": 120}, "connectionPoints": [{"id": "primary_in", "position": {"x": 0, "y": 45}, "type": "input", "label": "主电源输入"}, {"id": "secondary_in", "position": {"x": 0, "y": 75}, "type": "input", "label": "备用电源输入"}, {"id": "load_out", "position": {"x": 140, "y": 60}, "type": "output", "label": "负载输出"}, {"id": "control_signal", "position": {"x": 70, "y": 0}, "type": "input", "label": "控制信号"}], "bindingSlots": [{"id": "current_source", "name": "当前电源状态", "dataType": "string", "defaultValue": "primary", "enumValues": ["primary", "secondary", "transferring"], "description": "当前供电电源状态"}, {"id": "transfer_time", "name": "转换时间", "dataType": "number", "defaultValue": 100, "description": "电源转换时间 (ms)"}, {"id": "load_current", "name": "负载电流", "dataType": "number", "defaultValue": 0, "description": "负载电流 (A)"}, {"id": "primary_voltage", "name": "主电源电压", "dataType": "number", "defaultValue": 380, "description": "主电源电压 (V)"}, {"id": "secondary_voltage", "name": "备用电源电压", "dataType": "number", "defaultValue": 380, "description": "备用电源电压 (V)"}, {"id": "auto_mode", "name": "自动模式", "dataType": "boolean", "defaultValue": true, "description": "是否启用自动转换模式"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}]