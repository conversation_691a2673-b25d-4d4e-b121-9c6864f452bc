[{"id": "induction-motor", "category": "motor", "name": "异步电动机", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"55\" font-size=\"16\" text-anchor=\"middle\" fill=\"currentColor\">M</text><line x1=\"20\" y1=\"50\" x2=\"0\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"0\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "power_in", "position": {"x": 0, "y": 50}, "type": "input", "label": "电源输入"}, {"id": "mechanical_out", "position": {"x": 100, "y": 50}, "type": "output", "label": "机械输出"}], "bindingSlots": [{"id": "speed", "name": "转速", "dataType": "number", "defaultValue": 0, "description": "电机转速 (rpm)"}, {"id": "torque", "name": "转矩", "dataType": "number", "defaultValue": 0, "description": "输出转矩 (N·m)"}, {"id": "power", "name": "功率", "dataType": "number", "defaultValue": 0, "description": "输出功率 (kW)"}, {"id": "temperature", "name": "温度", "dataType": "number", "defaultValue": 25, "description": "电机温度 (°C)"}, {"id": "status", "name": "状态", "dataType": "enum", "defaultValue": "stopped", "enumValues": ["stopped", "starting", "running", "fault"], "description": "电机运行状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "synchronous-motor", "category": "motor", "name": "同步电动机", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"45\" font-size=\"14\" text-anchor=\"middle\" fill=\"currentColor\">SM</text><line x1=\"20\" y1=\"50\" x2=\"0\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"0\" stroke=\"currentColor\" stroke-width=\"3\"/><circle cx=\"50\" cy=\"65\" r=\"8\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><text x=\"50\" y=\"69\" font-size=\"8\" text-anchor=\"middle\" fill=\"currentColor\">励磁</text></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "power_in", "position": {"x": 0, "y": 50}, "type": "input", "label": "电源输入"}, {"id": "mechanical_out", "position": {"x": 100, "y": 50}, "type": "output", "label": "机械输出"}, {"id": "excitation", "position": {"x": 50, "y": 0}, "type": "input", "label": "励磁"}], "bindingSlots": [{"id": "speed", "name": "转速", "dataType": "number", "defaultValue": 0, "description": "电机转速 (rpm)"}, {"id": "excitation_current", "name": "励磁电流", "dataType": "number", "defaultValue": 0, "description": "励磁电流 (A)"}, {"id": "power_factor", "name": "功率因数", "dataType": "number", "defaultValue": 1.0, "description": "功率因数"}, {"id": "status", "name": "状态", "dataType": "enum", "defaultValue": "stopped", "enumValues": ["stopped", "starting", "synchronizing", "running", "fault"], "description": "电机运行状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "dc-motor", "category": "motor", "name": "直流电动机", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><text x=\"50\" y=\"45\" font-size=\"14\" text-anchor=\"middle\" fill=\"currentColor\">DC</text><text x=\"50\" y=\"60\" font-size=\"14\" text-anchor=\"middle\" fill=\"currentColor\">M</text><line x1=\"20\" y1=\"50\" x2=\"0\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"80\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"20\" x2=\"50\" y2=\"0\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"50\" y1=\"80\" x2=\"50\" y2=\"100\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "positive", "position": {"x": 50, "y": 0}, "type": "input", "label": "正极"}, {"id": "negative", "position": {"x": 50, "y": 100}, "type": "input", "label": "负极"}, {"id": "mechanical_out", "position": {"x": 100, "y": 50}, "type": "output", "label": "机械输出"}], "bindingSlots": [{"id": "speed", "name": "转速", "dataType": "number", "defaultValue": 0, "description": "电机转速 (rpm)"}, {"id": "armature_current", "name": "电枢电流", "dataType": "number", "defaultValue": 0, "description": "电枢电流 (A)"}, {"id": "field_current", "name": "励磁电流", "dataType": "number", "defaultValue": 0, "description": "励磁电流 (A)"}, {"id": "voltage", "name": "电压", "dataType": "number", "defaultValue": 0, "description": "电机电压 (V)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}]