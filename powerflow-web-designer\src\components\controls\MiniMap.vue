<template>
  <div class="mini-map" :class="{ expanded: isExpanded, visible: isVisible }">
    <div class="mini-map-header" @click="toggleExpand">
      <span>{{ $locale.t('miniMap.title') }}</span>
      <a-button type="text" size="small">
        <template #icon>
          <up-outlined v-if="isExpanded" />
          <down-outlined v-else />
        </template>
      </a-button>
    </div>

    <div class="mini-map-content" v-show="isExpanded">
      <div class="mini-map-canvas" ref="miniMapContainer">
        <v-stage
          ref="miniStage"
          :config="stageConfig"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
        >
          <v-layer>
            <!-- Symbols -->
            <v-group
              v-for="symbol in symbols"
              :key="symbol.id"
              :config="{
                x: symbol.position.x * scale,
                y: symbol.position.y * scale,
              }"
            >
              <v-rect
                :config="{
                  width: 10,
                  height: 10,
                  fill: getSymbolColor(symbol),
                  offsetX: 5,
                  offsetY: 5,
                }"
              />
            </v-group>

            <!-- Connections -->
            <v-line
              v-for="connection in connections"
              :key="connection.id"
              :config="{
                points: getConnectionPoints(connection),
                stroke: getConnectionColor(connection),
                strokeWidth: 1,
              }"
            />

            <!-- Viewport rectangle -->
            <v-rect
              :config="{
                x: viewportRect.x,
                y: viewportRect.y,
                width: viewportRect.width,
                height: viewportRect.height,
                stroke: '#1890ff',
                strokeWidth: 1,
                fill: 'rgba(24, 144, 255, 0.1)',
                dash: [5, 5],
              }"
            />
          </v-layer>
        </v-stage>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { UpOutlined, DownOutlined } from '@ant-design/icons-vue';
import { useDiagramStore } from '@/stores/diagram';
import { SymbolInstance } from '@/types/symbolInstance';
import { Connection } from '@/types/connection';
import { Position } from '@/types/symbol';

import { getConnectionPointPosition } from '@/types/symbolInstance';

// Props
const props = defineProps<{
  stageRef: any;
  width?: number;
  height?: number;
  isVisible?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'viewportChange', position: Position, scale: number): void;
  (e: 'toggleVisibility'): void;
}>();

// Store
const diagramStore = useDiagramStore();

// State
const isExpanded = ref(true);
const isVisibleInternal = ref(false); // Default hidden
const miniMapContainer = ref<HTMLElement | null>(null);
const miniStage = ref(null);
const isDragging = ref(false);
const isResizing = ref(false);
const resizeDirection = ref('');
const scale = ref(0.1); // Scale factor for the mini map
const showGrid = ref(true); // Show grid in mini map

// Computed
const isVisible = computed(() => props.isVisible || isVisibleInternal.value);

// Computed
const stageConfig = computed(() => ({
  width: props.width || 200,
  height: props.height || 150,
  draggable: false,
}));

const symbols = computed(() => {
  if (!diagramStore.currentDiagram) return [];
  return Object.values(diagramStore.currentDiagram.symbolInstances);
});

const connections = computed(() => {
  if (!diagramStore.currentDiagram) return [];

  // 过滤掉无效的连接（没有源或目标的连接）
  return Object.values(diagramStore.currentDiagram.connections).filter(
    connection => connection.source && connection.target
  );
});

const viewportRect = computed(() => {
  if (!diagramStore.currentDiagram || !props.stageRef) return { x: 0, y: 0, width: 0, height: 0 };

  const viewport = diagramStore.currentDiagram.viewport;

  // 检查 stageRef 是否有 getNode 方法
  let stageWidth = 800;
  let stageHeight = 600;

  try {
    if (typeof props.stageRef.getNode === 'function') {
      const stage = props.stageRef.getNode();
      stageWidth = stage.width();
      stageHeight = stage.height();
    } else if (props.stageRef.$el && props.stageRef.$el.clientWidth) {
      // 尝试从 $el 获取尺寸
      stageWidth = props.stageRef.$el.clientWidth;
      stageHeight = props.stageRef.$el.clientHeight;
    }
  } catch (error) {
    console.warn('Failed to get stage dimensions:', error);
  }

  // Calculate the viewport rectangle in mini map coordinates
  const x = viewport.position.x * scale.value;
  const y = viewport.position.y * scale.value;
  const width = (stageWidth / viewport.scale) * scale.value;
  const height = (stageHeight / viewport.scale) * scale.value;

  return { x, y, width, height };
});

// Methods
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const toggleVisibility = () => {
  isVisibleInternal.value = !isVisibleInternal.value;
  emit('toggleVisibility');
};

const getSymbolColor = (symbol: SymbolInstance) => {
  // Get the symbol definition
  const definition = diagramStore.getSymbolDefinition(symbol.definitionId);
  if (!definition) return '#000000';

  // Return the fill color
  return symbol.properties.fillColor ||
         definition.properties.fillColor ||
         '#000000';
};

const getConnectionColor = (connection: Connection) => {
  return connection.style.strokeColor || '#000000';
};

const getConnectionPoints = (connection: Connection) => {
  if (!diagramStore.currentDiagram) return [];

  // 检查连接是否有有效的源和目标
  if (!connection.source || !connection.target) {
    return [];
  }

  // Get the source and target positions
  const source = getEndpointPosition(connection.source);
  const target = getEndpointPosition(connection.target);

  // Scale the positions
  const scaledSource = {
    x: source.x * scale.value,
    y: source.y * scale.value,
  };

  const scaledTarget = {
    x: target.x * scale.value,
    y: target.y * scale.value,
  };

  // If it's a straight line, return the source and target points
  if (connection.lineType === 'straight') {
    return [scaledSource.x, scaledSource.y, scaledTarget.x, scaledTarget.y];
  }

  // If it has waypoints, include them
  const points: number[] = [scaledSource.x, scaledSource.y];

  if (connection.lineType === 'polyline' && connection.waypoints) {
    connection.waypoints.forEach(point => {
      points.push(point.x * scale.value, point.y * scale.value);
    });
  }

  points.push(scaledTarget.x, scaledTarget.y);

  return points;
};

const getEndpointPosition = (endpoint: { symbolInstanceId: string; connectionPointId: string } | undefined) => {
  if (!diagramStore.currentDiagram || !endpoint) return { x: 0, y: 0 };

  const symbol = diagramStore.currentDiagram.symbolInstances[endpoint.symbolInstanceId];
  if (!symbol) return { x: 0, y: 0 };

  const definition = diagramStore.getSymbolDefinition(symbol.definitionId);
  if (!definition) return { x: 0, y: 0 };

  const position = getConnectionPointPosition(symbol, definition, endpoint.connectionPointId);
  return position || { x: 0, y: 0 };
};

const handleMouseDown = (e: any) => {
  if (!diagramStore.currentDiagram || !props.stageRef) return;

  isDragging.value = true;
  updateViewport(e);
};

const handleMouseMove = (e: any) => {
  if (!isDragging.value) return;

  updateViewport(e);
};

const handleMouseUp = () => {
  isDragging.value = false;
  isResizing.value = false;
};

const startResize = (direction: string) => {
  if (!diagramStore.currentDiagram || !props.stageRef) return;

  isResizing.value = true;
  resizeDirection.value = direction;
};

const toggleGrid = () => {
  showGrid.value = !showGrid.value;
};

const fitContent = () => {
  if (!diagramStore.currentDiagram || !props.stageRef) return;

  // Get the main stage
  const stage = props.stageRef.getNode();
  const stageWidth = stage.width();
  const stageHeight = stage.height();

  // Calculate the bounds of all symbols
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  // Check if there are any symbols
  const symbols = Object.values(diagramStore.currentDiagram.symbolInstances);
  if (symbols.length === 0) {
    // Reset to default view
    diagramStore.currentDiagram.viewport = {
      position: { x: 0, y: 0 },
      scale: 1,
    };
    return;
  }

  // Calculate the bounds
  symbols.forEach(symbol => {
    const x = symbol.position.x;
    const y = symbol.position.y;
    const width = symbol.dimensions?.width || 100;
    const height = symbol.dimensions?.height || 100;

    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + width);
    maxY = Math.max(maxY, y + height);
  });

  // Include connections in the bounds calculation
  const connections = Object.values(diagramStore.currentDiagram.connections);
  connections.forEach(connection => {
    if (connection.waypoints) {
      connection.waypoints.forEach(waypoint => {
        minX = Math.min(minX, waypoint.x);
        minY = Math.min(minY, waypoint.y);
        maxX = Math.max(maxX, waypoint.x);
        maxY = Math.max(maxY, waypoint.y);
      });
    }
  });

  // Add padding
  const padding = 50;
  minX -= padding;
  minY -= padding;
  maxX += padding;
  maxY += padding;

  // Calculate the content dimensions
  const contentWidth = maxX - minX;
  const contentHeight = maxY - minY;

  // Calculate the scale to fit
  const scaleX = stageWidth / contentWidth;
  const scaleY = stageHeight / contentHeight;
  const scale = Math.min(scaleX, scaleY, 5);

  // Calculate the center position
  const centerX = (minX + maxX) / 2;
  const centerY = (minY + maxY) / 2;

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    position: {
      x: centerX - stageWidth / (2 * scale),
      y: centerY - stageHeight / (2 * scale),
    },
    scale: scale,
  };

  // Emit the viewport change event
  emit('viewportChange', diagramStore.currentDiagram.viewport.position, scale);
};

const updateViewport = (e: any) => {
  if (!diagramStore.currentDiagram || !props.stageRef) return;

  // Get the pointer position in mini map coordinates
  const pos = miniStage.value.getNode().getPointerPosition();

  // Convert to main stage coordinates
  const x = pos.x / scale.value;
  const y = pos.y / scale.value;

  // Get the main stage
  const stage = props.stageRef.getNode();

  // Calculate the new viewport position
  const viewport = diagramStore.currentDiagram.viewport;
  const newPosition = {
    x: x - stage.width() / (2 * viewport.scale),
    y: y - stage.height() / (2 * viewport.scale),
  };

  // Update the viewport
  diagramStore.currentDiagram.viewport = {
    ...viewport,
    position: newPosition,
  };

  // Emit the viewport change event
  emit('viewportChange', newPosition, viewport.scale);
};

// Lifecycle hooks
onMounted(() => {
  // Calculate the scale factor based on the diagram size
  if (diagramStore.currentDiagram && props.width && props.height) {
    const diagramWidth = diagramStore.currentDiagram.size.width;
    const diagramHeight = diagramStore.currentDiagram.size.height;

    const scaleX = props.width / diagramWidth;
    const scaleY = props.height / diagramHeight;

    scale.value = Math.min(scaleX, scaleY, 0.1);
  }
});
</script>

<style scoped>
.mini-map {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  z-index: 100;
  overflow: hidden;
  transition: all 0.3s;
  backdrop-filter: blur(4px);
  display: none; /* Hidden by default */
}

.mini-map.visible {
  display: block; /* Show when visible class is applied */
}

.mini-map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.8);
}

.mini-map-title {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.mini-map-toggle {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  padding: 0;
  font-size: 12px;
}

.mini-map-content {
  padding: 8px;
}

.mini-map-canvas {
  border: 1px solid #f0f0f0;
  background-color: #f5f5f5;
  position: relative;
}

.mini-map-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(#e0e0e0 1px, transparent 1px),
                    linear-gradient(90deg, #e0e0e0 1px, transparent 1px);
  background-size: 10px 10px;
  opacity: 0.5;
  pointer-events: none;
}

/* Resize handles */
.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #1890ff;
  border-radius: 50%;
  border: 1px solid white;
  z-index: 2;
}

.resize-handle-nw {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.resize-handle-ne {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.resize-handle-sw {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.resize-handle-se {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}

.mini-map-controls {
  display: flex;
  justify-content: center;
  padding: 8px;
  border-top: 1px solid #f0f0f0;
}

.mini-map-control-button {
  background: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 8px;
  margin: 0 4px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
}

.mini-map-control-button:hover {
  color: #1890ff;
  border-color: #1890ff;
}

/* Selected elements */
.mini-map-symbol-selected {
  border: 1px solid #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>
