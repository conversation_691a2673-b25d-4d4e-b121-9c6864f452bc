/**
 * Power system layout utilities for PowerFlow Web Designer
 * Implements specialized layout algorithms for power distribution systems
 */

import type { Position } from '@/types/symbol';
import type { SymbolInstance } from '@/types/symbolInstance';
import type { Connection } from '@/types/connection';
import { ConnectionLineType, ConnectionType } from '@/types/connection';
import { findPath, RoutingStrategy } from './pathFinding';
import { calculateBounds, calculateGroupBounds } from './alignment';

// Layout types for power systems
export enum PowerLayoutType {
  RADIAL = 'radial',       // Radial layout (for substation outgoing lines)
  CASCADE = 'cascade',     // Cascade layout (for power transmission chains)
  RING = 'ring',           // Ring layout (for ring network structures)
  BUSBAR = 'busbar',       // Busbar-branch layout (for standard distribution systems)
}

// Layout options for power systems
export interface PowerLayoutOptions {
  type: PowerLayoutType;
  spacing: number;         // Spacing between elements
  centerX?: number;        // Center X position for radial and ring layouts
  centerY?: number;        // Center Y position for radial and ring layouts
  direction?: 'horizontal' | 'vertical'; // Direction for cascade and busbar layouts
  startAngle?: number;     // Starting angle for radial layout (in degrees)
  endAngle?: number;       // Ending angle for radial layout (in degrees)
  radius?: number;         // Radius for radial and ring layouts
  preservePositions?: string[]; // IDs of symbols to preserve positions
  optimizeConnections?: boolean; // Whether to optimize connections after layout
  alignToGrid?: boolean;   // Whether to align symbols to grid
  gridSize?: number;       // Grid size for alignment
}

// Default layout options
export const defaultLayoutOptions: PowerLayoutOptions = {
  type: PowerLayoutType.RADIAL,
  spacing: 150,
  direction: 'horizontal',
  startAngle: 0,
  endAngle: 360,
  radius: 300,
  preservePositions: [],
  optimizeConnections: true,
  alignToGrid: true,
  gridSize: 20,
};

/**
 * Apply a power system layout to a set of symbols and connections
 * @param symbols Array of symbol instances
 * @param connections Record of connections
 * @param options Layout options
 * @returns New positions for symbols and updated connections
 */
export function applyPowerLayout(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>,
  options: Partial<PowerLayoutOptions> = {}
): {
  symbolPositions: { id: string, position: Position }[],
  updatedConnections: Record<string, Connection>
} {
  // Merge options with defaults
  const mergedOptions: PowerLayoutOptions = {
    ...defaultLayoutOptions,
    ...options,
  };

  // Choose layout algorithm based on type
  switch (mergedOptions.type) {
    case PowerLayoutType.RADIAL:
      return applyRadialLayout(symbols, connections, mergedOptions);
    case PowerLayoutType.CASCADE:
      return applyCascadeLayout(symbols, connections, mergedOptions);
    case PowerLayoutType.RING:
      return applyRingLayout(symbols, connections, mergedOptions);
    case PowerLayoutType.BUSBAR:
      return applyBusbarLayout(symbols, connections, mergedOptions);
    default:
      return applyRadialLayout(symbols, connections, mergedOptions);
  }
}

/**
 * Apply a cascade layout to symbols and connections
 * @param symbols Array of symbol instances
 * @param connections Record of connections
 * @param options Layout options
 * @returns New positions for symbols and updated connections
 */
function applyCascadeLayout(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>,
  options: PowerLayoutOptions
): {
  symbolPositions: { id: string, position: Position }[],
  updatedConnections: Record<string, Connection>
} {
  // Identify the source and target symbols based on connections
  const { sources, intermediates, targets } = categorizeSymbolsByFlow(symbols, connections);

  // Calculate positions based on direction
  const isHorizontal = options.direction !== 'vertical';
  const spacing = options.spacing || 150;

  // Find starting position (use the first source or the first symbol)
  const startSymbol = sources.length > 0 ? sources[0] : symbols[0];
  const startX = startSymbol.position.x;
  const startY = startSymbol.position.y;

  // Calculate new positions
  const symbolPositions: { id: string, position: Position }[] = [];

  // Position source symbols
  sources.forEach((symbol, index) => {
    if (options.preservePositions?.includes(symbol.id)) {
      symbolPositions.push({
        id: symbol.id,
        position: { ...symbol.position }
      });
      return;
    }

    const position = isHorizontal
      ? { x: startX, y: startY + index * spacing }
      : { x: startX + index * spacing, y: startY };

    symbolPositions.push({
      id: symbol.id,
      position: options.alignToGrid
        ? alignPositionToGrid(position, options.gridSize || 20)
        : position
    });
  });

  // Position intermediate symbols
  const intermediateOffset = isHorizontal ? spacing : spacing;
  intermediates.forEach((symbol, index) => {
    if (options.preservePositions?.includes(symbol.id)) {
      symbolPositions.push({
        id: symbol.id,
        position: { ...symbol.position }
      });
      return;
    }

    const position = isHorizontal
      ? { x: startX + intermediateOffset, y: startY + index * spacing }
      : { x: startX + index * spacing, y: startY + intermediateOffset };

    symbolPositions.push({
      id: symbol.id,
      position: options.alignToGrid
        ? alignPositionToGrid(position, options.gridSize || 20)
        : position
    });
  });

  // Position target symbols
  const targetOffset = isHorizontal ? spacing * 2 : spacing * 2;
  targets.forEach((symbol, index) => {
    if (options.preservePositions?.includes(symbol.id)) {
      symbolPositions.push({
        id: symbol.id,
        position: { ...symbol.position }
      });
      return;
    }

    const position = isHorizontal
      ? { x: startX + targetOffset, y: startY + index * spacing }
      : { x: startX + index * spacing, y: startY + targetOffset };

    symbolPositions.push({
      id: symbol.id,
      position: options.alignToGrid
        ? alignPositionToGrid(position, options.gridSize || 20)
        : position
    });
  });

  // Add any remaining symbols
  symbols.forEach(symbol => {
    if (!symbolPositions.some(p => p.id === symbol.id)) {
      symbolPositions.push({
        id: symbol.id,
        position: { ...symbol.position }
      });
    }
  });

  // Optimize connections if requested
  const updatedConnections = options.optimizeConnections
    ? optimizeConnections(symbols, connections, symbolPositions)
    : { ...connections };

  return { symbolPositions, updatedConnections };
}

/**
 * Apply a radial layout to symbols and connections
 * @param symbols Array of symbol instances
 * @param connections Record of connections
 * @param options Layout options
 * @returns New positions for symbols and updated connections
 */
function applyRadialLayout(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>,
  options: PowerLayoutOptions
): {
  symbolPositions: { id: string, position: Position }[],
  updatedConnections: Record<string, Connection>
} {
  // Find the center symbol (usually a busbar or transformer)
  const centerSymbol = findCenterSymbol(symbols, connections);

  // Calculate center position
  const centerX = options.centerX !== undefined ? options.centerX : centerSymbol.position.x;
  const centerY = options.centerY !== undefined ? options.centerY : centerSymbol.position.y;

  // Get symbols to arrange radially (excluding preserved positions)
  const symbolsToArrange = symbols.filter(s =>
    s.id !== centerSymbol.id &&
    !options.preservePositions?.includes(s.id)
  );

  // Calculate angles for each symbol
  const startAngle = (options.startAngle || 0) * Math.PI / 180;
  const endAngle = (options.endAngle || 360) * Math.PI / 180;
  const angleStep = (endAngle - startAngle) / Math.max(symbolsToArrange.length, 1);
  const radius = options.radius || 300;

  // Calculate new positions
  const symbolPositions: { id: string, position: Position }[] = [];

  // Keep center symbol in place
  symbolPositions.push({
    id: centerSymbol.id,
    position: { ...centerSymbol.position }
  });

  // Position other symbols in a radial pattern
  symbolsToArrange.forEach((symbol, index) => {
    const angle = startAngle + index * angleStep;
    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);

    // Align to grid if requested
    const position = options.alignToGrid
      ? alignPositionToGrid({ x, y }, options.gridSize || 20)
      : { x, y };

    symbolPositions.push({
      id: symbol.id,
      position
    });
  });

  // Add preserved positions
  symbols.forEach(symbol => {
    if (options.preservePositions?.includes(symbol.id) &&
        !symbolPositions.some(p => p.id === symbol.id)) {
      symbolPositions.push({
        id: symbol.id,
        position: { ...symbol.position }
      });
    }
  });

  // Optimize connections if requested
  const updatedConnections = options.optimizeConnections
    ? optimizeConnections(symbols, connections, symbolPositions)
    : { ...connections };

  return { symbolPositions, updatedConnections };
}

/**
 * Find the center symbol for a radial layout
 * @param symbols Array of symbol instances
 * @param connections Record of connections
 * @returns The symbol to use as center
 */
function findCenterSymbol(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>
): SymbolInstance {
  // Count connections for each symbol
  const connectionCounts: Record<string, number> = {};

  symbols.forEach(symbol => {
    connectionCounts[symbol.id] = 0;
  });

  // Count connections
  Object.values(connections).forEach(connection => {
    const sourceId = connection.source.symbolInstanceId;
    const targetId = connection.target.symbolInstanceId;

    connectionCounts[sourceId] = (connectionCounts[sourceId] || 0) + 1;
    connectionCounts[targetId] = (connectionCounts[targetId] || 0) + 1;
  });

  // Find symbol with most connections
  let maxConnections = 0;
  let centerId = symbols[0].id;

  Object.entries(connectionCounts).forEach(([id, count]) => {
    if (count > maxConnections) {
      maxConnections = count;
      centerId = id;
    }
  });

  return symbols.find(s => s.id === centerId) || symbols[0];
}

/**
 * Align a position to the nearest grid point
 * @param position Position to align
 * @param gridSize Grid size
 * @returns Aligned position
 */
function alignPositionToGrid(position: Position, gridSize: number): Position {
  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize,
  };
}

/**
 * Categorize symbols by their flow in the diagram
 * @param symbols Array of symbol instances
 * @param connections Record of connections
 * @returns Object with sources, intermediates, and targets arrays
 */
function categorizeSymbolsByFlow(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>
): {
  sources: SymbolInstance[],
  intermediates: SymbolInstance[],
  targets: SymbolInstance[]
} {
  // Count incoming and outgoing connections for each symbol
  const incomingCount: Record<string, number> = {};
  const outgoingCount: Record<string, number> = {};

  // Initialize counts
  symbols.forEach(symbol => {
    incomingCount[symbol.id] = 0;
    outgoingCount[symbol.id] = 0;
  });

  // Count connections
  Object.values(connections).forEach(connection => {
    const sourceId = connection.source.symbolInstanceId;
    const targetId = connection.target.symbolInstanceId;

    outgoingCount[sourceId] = (outgoingCount[sourceId] || 0) + 1;
    incomingCount[targetId] = (incomingCount[targetId] || 0) + 1;
  });

  // Categorize symbols
  const sources: SymbolInstance[] = [];
  const intermediates: SymbolInstance[] = [];
  const targets: SymbolInstance[] = [];

  symbols.forEach(symbol => {
    if (incomingCount[symbol.id] === 0 && outgoingCount[symbol.id] > 0) {
      // Source: has outgoing but no incoming connections
      sources.push(symbol);
    } else if (incomingCount[symbol.id] > 0 && outgoingCount[symbol.id] === 0) {
      // Target: has incoming but no outgoing connections
      targets.push(symbol);
    } else {
      // Intermediate: has both incoming and outgoing connections
      intermediates.push(symbol);
    }
  });

  return { sources, intermediates, targets };
}

/**
 * Apply a ring layout to symbols and connections
 * @param symbols Array of symbol instances
 * @param connections Record of connections
 * @param options Layout options
 * @returns New positions for symbols and updated connections
 */
function applyRingLayout(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>,
  options: PowerLayoutOptions
): {
  symbolPositions: { id: string, position: Position }[],
  updatedConnections: Record<string, Connection>
} {
  // Find the center position
  const centerSymbol = findCenterSymbol(symbols, connections);
  const centerX = options.centerX !== undefined ? options.centerX : centerSymbol.position.x;
  const centerY = options.centerY !== undefined ? options.centerY : centerSymbol.position.y;

  // Get symbols to arrange in a ring (excluding preserved positions)
  const symbolsToArrange = symbols.filter(s =>
    !options.preservePositions?.includes(s.id)
  );

  // Calculate positions in a ring
  const radius = options.radius || 300;
  const angleStep = (2 * Math.PI) / symbolsToArrange.length;

  // Calculate new positions
  const symbolPositions: { id: string, position: Position }[] = [];

  // Position symbols in a ring
  symbolsToArrange.forEach((symbol, index) => {
    const angle = index * angleStep;
    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);

    // Align to grid if requested
    const position = options.alignToGrid
      ? alignPositionToGrid({ x, y }, options.gridSize || 20)
      : { x, y };

    symbolPositions.push({
      id: symbol.id,
      position
    });
  });

  // Add preserved positions
  symbols.forEach(symbol => {
    if (options.preservePositions?.includes(symbol.id) &&
        !symbolPositions.some(p => p.id === symbol.id)) {
      symbolPositions.push({
        id: symbol.id,
        position: { ...symbol.position }
      });
    }
  });

  // Optimize connections if requested
  const updatedConnections = options.optimizeConnections
    ? optimizeConnections(symbols, connections, symbolPositions)
    : { ...connections };

  return { symbolPositions, updatedConnections };
}

// Note: categorizeSymbolsByFlow is already defined above

/**
 * Apply a ring layout to symbols and connections
 * @param symbols Array of symbol instances
 * @param connections Record of connections
 * @param options Layout options
 * @returns New positions for symbols and updated connections
 */
function applyRingLayout(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>,
  options: PowerLayoutOptions
): {
  symbolPositions: { id: string, position: Position }[],
  updatedConnections: Record<string, Connection>
} {
  // Find the center position
  const centerSymbol = findCenterSymbol(symbols, connections);
  const centerX = options.centerX !== undefined ? options.centerX : centerSymbol.position.x;
  const centerY = options.centerY !== undefined ? options.centerY : centerSymbol.position.y;

  // Get symbols to arrange in a ring (excluding preserved positions)
  const symbolsToArrange = symbols.filter(s =>
    !options.preservePositions?.includes(s.id)
  );

  // Calculate positions in a ring
  const radius = options.radius || 300;
  const angleStep = (2 * Math.PI) / symbolsToArrange.length;

  // Calculate new positions
  const symbolPositions: { id: string, position: Position }[] = [];

  // Position symbols in a ring
  symbolsToArrange.forEach((symbol, index) => {
    const angle = index * angleStep;
    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);

    // Align to grid if requested
    const position = options.alignToGrid
      ? alignPositionToGrid({ x, y }, options.gridSize || 20)
      : { x, y };

    symbolPositions.push({
      id: symbol.id,
      position
    });
  });

  // Add preserved positions
  symbols.forEach(symbol => {
    if (options.preservePositions?.includes(symbol.id) &&
        !symbolPositions.some(p => p.id === symbol.id)) {
      symbolPositions.push({
        id: symbol.id,
        position: { ...symbol.position }
      });
    }
  });

  // Optimize connections if requested
  const updatedConnections = options.optimizeConnections
    ? optimizeConnections(symbols, connections, symbolPositions)
    : { ...connections };

  return { symbolPositions, updatedConnections };
}

/**
 * Apply a busbar layout to symbols and connections
 * @param symbols Array of symbol instances
 * @param connections Record of connections
 * @param options Layout options
 * @returns New positions for symbols and updated connections
 */
function applyBusbarLayout(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>,
  options: PowerLayoutOptions
): {
  symbolPositions: { id: string, position: Position }[],
  updatedConnections: Record<string, Connection>
} {
  // Identify busbars (symbols with many connections)
  const busbars = identifyBusbars(symbols, connections);

  // If no busbars found, use the first symbol as a busbar
  if (busbars.length === 0 && symbols.length > 0) {
    busbars.push(symbols[0]);
  }

  // Get non-busbar symbols (excluding preserved positions)
  const nonBusbarSymbols = symbols.filter(s =>
    !busbars.some(b => b.id === s.id) &&
    !options.preservePositions?.includes(s.id)
  );

  // Calculate positions based on direction
  const isHorizontal = options.direction !== 'vertical';
  const spacing = options.spacing || 150;

  // Find starting position (use the first busbar)
  const startSymbol = busbars[0];
  const startX = startSymbol.position.x;
  const startY = startSymbol.position.y;

  // Calculate new positions
  const symbolPositions: { id: string, position: Position }[] = [];

  // Position busbars
  busbars.forEach((symbol, index) => {
    if (options.preservePositions?.includes(symbol.id)) {
      symbolPositions.push({
        id: symbol.id,
        position: { ...symbol.position }
      });
      return;
    }

    const position = isHorizontal
      ? { x: startX, y: startY + index * spacing * 3 }
      : { x: startX + index * spacing * 3, y: startY };

    symbolPositions.push({
      id: symbol.id,
      position: options.alignToGrid
        ? alignPositionToGrid(position, options.gridSize || 20)
        : position
    });
  });

  // Position non-busbar symbols
  const branchSpacing = spacing;
  nonBusbarSymbols.forEach((symbol, index) => {
    // Find the busbar this symbol is connected to
    const connectedBusbar = findConnectedBusbar(symbol.id, busbars, connections);
    const busbarIndex = connectedBusbar ? busbars.findIndex(b => b.id === connectedBusbar.id) : 0;
    const busbarPosition = symbolPositions.find(p => p.id === (connectedBusbar?.id || busbars[0].id))?.position ||
                          { x: startX, y: startY };

    // Calculate position based on busbar position
    const position = isHorizontal
      ? {
          x: busbarPosition.x + branchSpacing + (index % 5) * branchSpacing,
          y: busbarPosition.y + (Math.floor(index / 5) - 2) * branchSpacing
        }
      : {
          x: busbarPosition.x + (Math.floor(index / 5) - 2) * branchSpacing,
          y: busbarPosition.y + branchSpacing + (index % 5) * branchSpacing
        };

    symbolPositions.push({
      id: symbol.id,
      position: options.alignToGrid
        ? alignPositionToGrid(position, options.gridSize || 20)
        : position
    });
  });

  // Add preserved positions
  symbols.forEach(symbol => {
    if (options.preservePositions?.includes(symbol.id) &&
        !symbolPositions.some(p => p.id === symbol.id)) {
      symbolPositions.push({
        id: symbol.id,
        position: { ...symbol.position }
      });
    }
  });

  // Optimize connections if requested
  const updatedConnections = options.optimizeConnections
    ? optimizeConnections(symbols, connections, symbolPositions)
    : { ...connections };

  return { symbolPositions, updatedConnections };
}

/**
 * Identify busbars in the diagram
 * @param symbols Array of symbol instances
 * @param connections Record of connections
 * @returns Array of busbar symbols
 */
function identifyBusbars(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>
): SymbolInstance[] {
  // Count connections for each symbol
  const connectionCounts: Record<string, number> = {};

  symbols.forEach(symbol => {
    connectionCounts[symbol.id] = 0;
  });

  // Count connections
  Object.values(connections).forEach(connection => {
    const sourceId = connection.source.symbolInstanceId;
    const targetId = connection.target.symbolInstanceId;

    connectionCounts[sourceId] = (connectionCounts[sourceId] || 0) + 1;
    connectionCounts[targetId] = (connectionCounts[targetId] || 0) + 1;
  });

  // Find symbols with many connections (potential busbars)
  // or symbols with "busbar" in their definition ID
  const busbars = symbols.filter(symbol =>
    connectionCounts[symbol.id] > 3 ||
    symbol.definitionId.toLowerCase().includes('busbar') ||
    symbol.definitionId.toLowerCase().includes('switchgear')
  );

  return busbars;
}

/**
 * Find the busbar connected to a symbol
 * @param symbolId ID of the symbol
 * @param busbars Array of busbar symbols
 * @param connections Record of connections
 * @returns Connected busbar or undefined
 */
function findConnectedBusbar(
  symbolId: string,
  busbars: SymbolInstance[],
  connections: Record<string, Connection>
): SymbolInstance | undefined {
  // Find connections to/from this symbol
  const connectedSymbolIds = new Set<string>();

  Object.values(connections).forEach(connection => {
    if (connection.source.symbolInstanceId === symbolId) {
      connectedSymbolIds.add(connection.target.symbolInstanceId);
    } else if (connection.target.symbolInstanceId === symbolId) {
      connectedSymbolIds.add(connection.source.symbolInstanceId);
    }
  });

  // Find the first busbar in the connected symbols
  return busbars.find(busbar => connectedSymbolIds.has(busbar.id));
}

/**
 * Optimize connections based on new symbol positions
 * @param symbols Original symbol instances
 * @param connections Original connections
 * @param symbolPositions New symbol positions
 * @returns Updated connections
 */
function optimizeConnections(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>,
  symbolPositions: { id: string, position: Position }[]
): Record<string, Connection> {
  // Create a copy of connections
  const updatedConnections: Record<string, Connection> = {};

  // Update each connection
  Object.entries(connections).forEach(([id, connection]) => {
    // Create a copy of the connection
    updatedConnections[id] = { ...connection };

    // Set line type to SMART for automatic routing
    updatedConnections[id].lineType = ConnectionLineType.SMART;

    // Clear existing waypoints to allow recalculation
    updatedConnections[id].waypoints = [];

    // Set routing options to minimize crossings
    updatedConnections[id].routingOptions = {
      avoidObstacles: true,
      padding: 20,
      preferStraightLines: true,
      smoothingFactor: 0.5,
      routingStrategy: 'orthogonal',
      cornerRadius: 10,
      jumpOverCrossings: true,
      snapToGrid: true,
      optimizePath: true,
      gridSize: 10,
    };
  });

  // Reduce crossing connections
  reduceCrossingConnections(updatedConnections, symbolPositions);

  return updatedConnections;
}

/**
 * Reduce crossing connections by adjusting routing
 * @param connections Connections to optimize
 * @param symbolPositions Symbol positions
 */
function reduceCrossingConnections(
  connections: Record<string, Connection>,
  symbolPositions: { id: string, position: Position }[]
): void {
  // Create a map of symbol positions for quick lookup
  const positionMap = new Map<string, Position>();
  symbolPositions.forEach(sp => {
    positionMap.set(sp.id, sp.position);
  });

  // Group connections by their endpoints
  const connectionGroups = groupConnectionsByEndpoints(connections);

  // Process each group to minimize crossings
  connectionGroups.forEach(group => {
    if (group.length < 2) return;

    // Sort connections by length (shorter connections get more direct paths)
    group.sort((a, b) => {
      const aSourcePos = positionMap.get(a.source.symbolInstanceId);
      const aTargetPos = positionMap.get(a.target.symbolInstanceId);
      const bSourcePos = positionMap.get(b.source.symbolInstanceId);
      const bTargetPos = positionMap.get(b.target.symbolInstanceId);

      if (!aSourcePos || !aTargetPos || !bSourcePos || !bTargetPos) return 0;

      const aLength = calculateDistance(aSourcePos, aTargetPos);
      const bLength = calculateDistance(bSourcePos, bTargetPos);

      return aLength - bLength;
    });

    // Assign different routing strategies based on connection order
    group.forEach((connection, index) => {
      if (index === 0) {
        // First (shortest) connection gets direct path
        connections[connection.id].routingOptions = {
          ...connections[connection.id].routingOptions,
          routingStrategy: 'direct',
          preferStraightLines: true,
        };
      } else if (index === 1) {
        // Second connection gets orthogonal path
        connections[connection.id].routingOptions = {
          ...connections[connection.id].routingOptions,
          routingStrategy: 'orthogonal',
          preferStraightLines: true,
        };
      } else {
        // Other connections get manhattan or metro paths
        connections[connection.id].routingOptions = {
          ...connections[connection.id].routingOptions,
          routingStrategy: index % 2 === 0 ? 'manhattan' : 'metro',
          preferStraightLines: true,
          jumpOverCrossings: true,
        };
      }
    });
  });
}

/**
 * Group connections by their endpoints (connections between the same symbols)
 * @param connections Connections to group
 * @returns Array of connection groups
 */
function groupConnectionsByEndpoints(
  connections: Record<string, Connection>
): Connection[][] {
  // Create a map to store groups
  const groups: Map<string, Connection[]> = new Map();

  // Process each connection
  Object.values(connections).forEach(connection => {
    // Create a key for the group based on source and target
    const sourceId = connection.source.symbolInstanceId;
    const targetId = connection.target.symbolInstanceId;

    // Create a consistent key regardless of direction
    const key = [sourceId, targetId].sort().join('-');

    // Add to the group
    if (!groups.has(key)) {
      groups.set(key, []);
    }

    groups.get(key)!.push(connection);
  });

  // Convert map to array of groups
  return Array.from(groups.values());
}

/**
 * Calculate distance between two positions
 * @param pos1 First position
 * @param pos2 Second position
 * @returns Distance
 */
function calculateDistance(pos1: Position, pos2: Position): number {
  return Math.sqrt(
    Math.pow(pos2.x - pos1.x, 2) +
    Math.pow(pos2.y - pos1.y, 2)
  );
}

/**
 * Optimize a selected area of the diagram
 * @param symbols All symbol instances
 * @param connections All connections
 * @param selectedSymbolIds IDs of selected symbols to optimize
 * @param options Layout options
 * @returns New positions for symbols and updated connections
 */
export function optimizeSelectedArea(
  symbols: SymbolInstance[],
  connections: Record<string, Connection>,
  selectedSymbolIds: string[],
  options: Partial<PowerLayoutOptions> = {}
): {
  symbolPositions: { id: string, position: Position }[],
  updatedConnections: Record<string, Connection>
} {
  // Filter symbols to only include selected ones
  const selectedSymbols = symbols.filter(s => selectedSymbolIds.includes(s.id));

  // Filter connections to only include those between selected symbols
  const selectedConnections: Record<string, Connection> = {};
  Object.entries(connections).forEach(([id, connection]) => {
    const sourceSelected = selectedSymbolIds.includes(connection.source.symbolInstanceId);
    const targetSelected = selectedSymbolIds.includes(connection.target.symbolInstanceId);

    if (sourceSelected && targetSelected) {
      selectedConnections[id] = connection;
    }
  });

  // Apply layout to selected symbols
  const result = applyPowerLayout(selectedSymbols, selectedConnections, options);

  // Add non-selected symbols with unchanged positions
  const nonSelectedSymbols = symbols.filter(s => !selectedSymbolIds.includes(s.id));
  nonSelectedSymbols.forEach(symbol => {
    result.symbolPositions.push({
      id: symbol.id,
      position: { ...symbol.position }
    });
  });

  // Add non-selected connections unchanged
  Object.entries(connections).forEach(([id, connection]) => {
    if (!selectedConnections[id]) {
      result.updatedConnections[id] = { ...connection };
    }
  });

  return result;
}

/**
 * Distribute symbols evenly
 * @param symbols Symbol instances to distribute
 * @param direction Direction of distribution ('horizontal' or 'vertical')
 * @param spacing Optional fixed spacing (if not provided, will distribute evenly)
 * @returns New positions for symbols
 */
export function distributeSymbolsEvenly(
  symbols: SymbolInstance[],
  direction: 'horizontal' | 'vertical',
  spacing?: number
): { id: string, position: Position }[] {
  if (symbols.length < 2) {
    return symbols.map(s => ({ id: s.id, position: { ...s.position } }));
  }

  // Sort symbols by position
  const sortedSymbols = [...symbols].sort((a, b) => {
    return direction === 'horizontal'
      ? a.position.x - b.position.x
      : a.position.y - b.position.y;
  });

  // Calculate total distance and spacing
  const first = sortedSymbols[0];
  const last = sortedSymbols[sortedSymbols.length - 1];
  const totalDistance = direction === 'horizontal'
    ? last.position.x - first.position.x
    : last.position.y - first.position.y;

  const evenSpacing = spacing || totalDistance / (sortedSymbols.length - 1);

  // Calculate new positions
  const symbolPositions: { id: string, position: Position }[] = [];

  sortedSymbols.forEach((symbol, index) => {
    const position = { ...symbol.position };

    if (index > 0 && index < sortedSymbols.length - 1) {
      if (direction === 'horizontal') {
        position.x = first.position.x + index * evenSpacing;
      } else {
        position.y = first.position.y + index * evenSpacing;
      }
    }

    symbolPositions.push({
      id: symbol.id,
      position
    });
  });

  return symbolPositions;
}

/**
 * Align symbols to grid
 * @param symbols Symbol instances to align
 * @param gridSize Grid size
 * @returns New positions for symbols
 */
export function alignSymbolsToGrid(
  symbols: SymbolInstance[],
  gridSize: number = 20
): { id: string, position: Position }[] {
  return symbols.map(symbol => ({
    id: symbol.id,
    position: alignPositionToGrid(symbol.position, gridSize)
  }));
}
