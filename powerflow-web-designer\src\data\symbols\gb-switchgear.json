[{"id": "load-switch", "category": "switchgear", "name": "负荷开关", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"0\" y1=\"50\" x2=\"30\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"70\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"30\" y1=\"50\" x2=\"65\" y2=\"25\" stroke=\"currentColor\" stroke-width=\"4\"/><circle cx=\"30\" cy=\"50\" r=\"4\" fill=\"currentColor\"/><circle cx=\"70\" cy=\"50\" r=\"4\" fill=\"currentColor\"/><rect x=\"25\" y=\"15\" width=\"50\" height=\"10\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><text x=\"50\" y=\"22\" font-size=\"8\" text-anchor=\"middle\" fill=\"currentColor\">负荷</text></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "in", "position": {"x": 0, "y": 50}, "type": "input"}, {"id": "out", "position": {"x": 100, "y": 50}, "type": "output"}], "bindingSlots": [{"id": "status", "name": "状态", "dataType": "boolean", "defaultValue": false, "description": "开关状态 (true = 合闸, false = 分闸)"}, {"id": "current", "name": "电流", "dataType": "number", "defaultValue": 0, "description": "通过电流 (A)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "earthing-switch", "category": "switchgear", "name": "接地开关", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"0\" y1=\"50\" x2=\"30\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"70\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"30\" y1=\"50\" x2=\"65\" y2=\"25\" stroke=\"currentColor\" stroke-width=\"4\"/><circle cx=\"30\" cy=\"50\" r=\"4\" fill=\"currentColor\"/><circle cx=\"70\" cy=\"50\" r=\"4\" fill=\"currentColor\"/><line x1=\"50\" y1=\"70\" x2=\"50\" y2=\"85\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"40\" y1=\"85\" x2=\"60\" y2=\"85\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"43\" y1=\"90\" x2=\"57\" y2=\"90\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"46\" y1=\"95\" x2=\"54\" y2=\"95\" stroke=\"currentColor\" stroke-width=\"3\"/></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "in", "position": {"x": 0, "y": 50}, "type": "input"}, {"id": "out", "position": {"x": 100, "y": 50}, "type": "output"}, {"id": "earth", "position": {"x": 50, "y": 100}, "type": "output", "label": "接地"}], "bindingSlots": [{"id": "status", "name": "状态", "dataType": "boolean", "defaultValue": false, "description": "接地开关状态 (true = 合闸, false = 分闸)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "motor-operated-switch", "category": "switchgear", "name": "电动操作开关", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"0\" y1=\"50\" x2=\"30\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"70\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"30\" y1=\"50\" x2=\"65\" y2=\"25\" stroke=\"currentColor\" stroke-width=\"4\"/><circle cx=\"30\" cy=\"50\" r=\"4\" fill=\"currentColor\"/><circle cx=\"70\" cy=\"50\" r=\"4\" fill=\"currentColor\"/><circle cx=\"50\" cy=\"75\" r=\"12\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/><text x=\"50\" y=\"80\" font-size=\"10\" text-anchor=\"middle\" fill=\"currentColor\">M</text></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "in", "position": {"x": 0, "y": 50}, "type": "input"}, {"id": "out", "position": {"x": 100, "y": 50}, "type": "output"}], "bindingSlots": [{"id": "status", "name": "状态", "dataType": "boolean", "defaultValue": false, "description": "开关状态 (true = 合闸, false = 分闸)"}, {"id": "motor_status", "name": "电机状态", "dataType": "enum", "defaultValue": "stopped", "enumValues": ["stopped", "opening", "closing", "fault"], "description": "电机操作状态"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}, {"id": "sf6-circuit-breaker", "category": "switchgear", "name": "SF6断路器", "svg": "<svg viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"><line x1=\"0\" y1=\"50\" x2=\"25\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"4\"/><line x1=\"75\" y1=\"50\" x2=\"100\" y2=\"50\" stroke=\"currentColor\" stroke-width=\"4\"/><rect x=\"25\" y=\"35\" width=\"50\" height=\"30\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"3\"/><line x1=\"30\" y1=\"50\" x2=\"70\" y2=\"25\" stroke=\"currentColor\" stroke-width=\"4\"/><circle cx=\"30\" cy=\"50\" r=\"4\" fill=\"currentColor\"/><circle cx=\"70\" cy=\"50\" r=\"4\" fill=\"currentColor\"/><text x=\"50\" y=\"30\" font-size=\"8\" text-anchor=\"middle\" fill=\"currentColor\">SF6</text></svg>", "dimensions": {"width": 100, "height": 100}, "connectionPoints": [{"id": "in", "position": {"x": 0, "y": 50}, "type": "input"}, {"id": "out", "position": {"x": 100, "y": 50}, "type": "output"}], "bindingSlots": [{"id": "status", "name": "状态", "dataType": "boolean", "defaultValue": false, "description": "断路器状态 (true = 合闸, false = 分闸)"}, {"id": "sf6_pressure", "name": "SF6气压", "dataType": "number", "defaultValue": 0.6, "description": "SF6气体压力 (MPa)"}, {"id": "current", "name": "电流", "dataType": "number", "defaultValue": 0, "description": "通过电流 (A)"}], "properties": {"fillColor": "#ffffff", "strokeColor": "#000000", "lineWidth": 2}}]