/**
 * Symbol library utility for PowerFlow Web Designer
 * Contains simulated symbol definitions based on common electrical power symbols
 */

import type { SymbolDefinition } from '../types/symbol';
import {
  SymbolCategory,
  ConnectionPointType,
  BindingDataType,
  BusbarOrientation,
  createSymbolDefinition,
  generateBusbarConnectionPoints,
} from '../types/symbol';

// SVG path data for common electrical symbols
const svgPaths = {
  // Circuit breaker
  circuitBreaker: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <line x1="0" y1="50" x2="30" y2="50" stroke="currentColor" stroke-width="4"/>
      <line x1="70" y1="50" x2="100" y2="50" stroke="currentColor" stroke-width="4"/>
      <line x1="30" y1="50" x2="70" y2="20" stroke="currentColor" stroke-width="4"/>
      <circle cx="30" cy="50" r="4" fill="currentColor"/>
      <circle cx="70" cy="50" r="4" fill="currentColor"/>
    </svg>
  `,

  // Disconnector switch
  disconnector: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <line x1="0" y1="50" x2="30" y2="50" stroke="currentColor" stroke-width="4"/>
      <line x1="70" y1="50" x2="100" y2="50" stroke="currentColor" stroke-width="4"/>
      <line x1="30" y1="50" x2="70" y2="30" stroke="currentColor" stroke-width="4"/>
      <circle cx="30" cy="50" r="4" fill="currentColor"/>
      <circle cx="70" cy="50" r="4" fill="currentColor"/>
    </svg>
  `,

  // Transformer
  transformer: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <circle cx="30" cy="50" r="20" fill="none" stroke="currentColor" stroke-width="4"/>
      <circle cx="70" cy="50" r="20" fill="none" stroke="currentColor" stroke-width="4"/>
      <line x1="0" y1="50" x2="10" y2="50" stroke="currentColor" stroke-width="4"/>
      <line x1="90" y1="50" x2="100" y2="50" stroke="currentColor" stroke-width="4"/>
    </svg>
  `,

  // Horizontal Busbar - Clean line without visible connection points
  busbarHorizontal: `
    <svg viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
      <rect x="0" y="20" width="200" height="20" fill="currentColor"/>
    </svg>
  `,

  // Vertical Busbar - Clean line without visible connection points
  busbarVertical: `
    <svg viewBox="0 0 60 200" xmlns="http://www.w3.org/2000/svg">
      <rect x="20" y="0" width="20" height="200" fill="currentColor"/>
    </svg>
  `,

  // Legacy busbar (for backward compatibility)
  busbar: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <rect x="0" y="40" width="100" height="20" fill="currentColor"/>
    </svg>
  `,

  // Current transformer
  currentTransformer: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <circle cx="50" cy="50" r="30" fill="none" stroke="currentColor" stroke-width="4"/>
      <line x1="0" y1="50" x2="20" y2="50" stroke="currentColor" stroke-width="4"/>
      <line x1="80" y1="50" x2="100" y2="50" stroke="currentColor" stroke-width="4"/>
      <text x="50" y="55" font-size="20" text-anchor="middle" fill="currentColor">CT</text>
    </svg>
  `,

  // Voltage transformer
  voltageTransformer: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <circle cx="50" cy="50" r="30" fill="none" stroke="currentColor" stroke-width="4"/>
      <line x1="0" y1="50" x2="20" y2="50" stroke="currentColor" stroke-width="4"/>
      <line x1="80" y1="50" x2="100" y2="50" stroke="currentColor" stroke-width="4"/>
      <text x="50" y="55" font-size="20" text-anchor="middle" fill="currentColor">VT</text>
    </svg>
  `,

  // Generator
  generator: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <circle cx="50" cy="50" r="30" fill="none" stroke="currentColor" stroke-width="4"/>
      <text x="50" y="55" font-size="20" text-anchor="middle" fill="currentColor">G</text>
      <line x1="80" y1="50" x2="100" y2="50" stroke="currentColor" stroke-width="4"/>
    </svg>
  `,

  // Diesel Generator
  dieselGenerator: `
    <svg viewBox="0 0 120 100" xmlns="http://www.w3.org/2000/svg">
      <circle cx="60" cy="50" r="30" fill="none" stroke="currentColor" stroke-width="4"/>
      <text x="60" y="45" font-size="16" text-anchor="middle" fill="currentColor">DG</text>
      <text x="60" y="60" font-size="10" text-anchor="middle" fill="currentColor">柴油</text>
      <line x1="90" y1="50" x2="120" y2="50" stroke="currentColor" stroke-width="4"/>
      <!-- Fuel tank indicator -->
      <rect x="10" y="40" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2"/>
      <line x1="30" y1="50" x2="30" y2="50" stroke="currentColor" stroke-width="2"/>
      <!-- Connection line from tank to generator -->
      <line x1="30" y1="50" x2="30" y2="50" stroke="currentColor" stroke-width="2" stroke-dasharray="3,3"/>
    </svg>
  `,

  // Load
  load: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <polygon points="50,20 20,80 80,80" fill="none" stroke="currentColor" stroke-width="4"/>
      <line x1="50" y1="20" x2="50" y2="0" stroke="currentColor" stroke-width="4"/>
    </svg>
  `,

  // Fuse
  fuse: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <line x1="0" y1="50" x2="20" y2="50" stroke="currentColor" stroke-width="4"/>
      <line x1="80" y1="50" x2="100" y2="50" stroke="currentColor" stroke-width="4"/>
      <rect x="20" y="40" width="60" height="20" fill="none" stroke="currentColor" stroke-width="4"/>
      <line x1="20" y1="50" x2="80" y2="50" stroke="currentColor" stroke-width="2" stroke-dasharray="5,5"/>
    </svg>
  `,

  // Ground
  ground: `
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <line x1="50" y1="0" x2="50" y2="60" stroke="currentColor" stroke-width="4"/>
      <line x1="30" y1="60" x2="70" y2="60" stroke="currentColor" stroke-width="4"/>
      <line x1="35" y1="70" x2="65" y2="70" stroke="currentColor" stroke-width="4"/>
      <line x1="40" y1="80" x2="60" y2="80" stroke="currentColor" stroke-width="4"/>
    </svg>
  `,
};

// Create the symbol library
export const symbolLibrary: SymbolDefinition[] = [
  // Circuit breaker
  createSymbolDefinition(
    'circuit-breaker',
    SymbolCategory.SWITCHGEAR,
    'Circuit Breaker',
    svgPaths.circuitBreaker,
    { width: 100, height: 100 },
    [
      { id: 'in', position: { x: 0, y: 50 }, type: ConnectionPointType.INPUT },
      { id: 'out', position: { x: 100, y: 50 }, type: ConnectionPointType.OUTPUT },
    ],
    [
      {
        id: 'status',
        name: 'Status',
        dataType: BindingDataType.BOOLEAN,
        defaultValue: false,
        description: 'Circuit breaker status (true = closed, false = open)',
      },
      {
        id: 'current',
        name: 'Current',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Current flowing through the circuit breaker (A)',
      },
    ]
  ),

  // Disconnector switch
  createSymbolDefinition(
    'disconnector',
    SymbolCategory.SWITCHGEAR,
    'Disconnector Switch',
    svgPaths.disconnector,
    { width: 100, height: 100 },
    [
      { id: 'in', position: { x: 0, y: 50 }, type: ConnectionPointType.INPUT },
      { id: 'out', position: { x: 100, y: 50 }, type: ConnectionPointType.OUTPUT },
    ],
    [
      {
        id: 'status',
        name: 'Status',
        dataType: BindingDataType.BOOLEAN,
        defaultValue: false,
        description: 'Disconnector status (true = closed, false = open)',
      },
    ]
  ),

  // Transformer
  createSymbolDefinition(
    'transformer',
    SymbolCategory.TRANSFORMER,
    'Transformer',
    svgPaths.transformer,
    { width: 100, height: 100 },
    [
      { id: 'primary', position: { x: 0, y: 50 }, type: ConnectionPointType.INPUT },
      { id: 'secondary', position: { x: 100, y: 50 }, type: ConnectionPointType.OUTPUT },
    ],
    [
      {
        id: 'temperature',
        name: 'Temperature',
        dataType: BindingDataType.NUMBER,
        defaultValue: 25,
        description: 'Transformer temperature (°C)',
      },
      {
        id: 'load',
        name: 'Load',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Transformer load (%)',
      },
    ]
  ),

  // Enhanced Horizontal Busbar
  createSymbolDefinition(
    'busbar-horizontal',
    SymbolCategory.BUSBAR,
    'Horizontal Busbar',
    svgPaths.busbarHorizontal,
    { width: 200, height: 60 },
    [], // Connection points will be generated dynamically
    [
      {
        id: 'voltage',
        name: 'Voltage',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Busbar voltage (kV)',
      },
      {
        id: 'current',
        name: 'Current',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Busbar current (A)',
      },
      {
        id: 'power',
        name: 'Power',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Busbar power (MW)',
      },
    ],
    {
      fillColor: '#ff0000',
      strokeColor: '#ff0000',
      lineWidth: 2,
      busbar: {
        orientation: BusbarOrientation.HORIZONTAL,
        length: 200,
        width: 20,
        connectionPointCount: 3,
        connectionPointSpacing: 60,
        autoDistributePoints: true,
      },
    }
  ),

  // Enhanced Vertical Busbar
  createSymbolDefinition(
    'busbar-vertical',
    SymbolCategory.BUSBAR,
    'Vertical Busbar',
    svgPaths.busbarVertical,
    { width: 60, height: 200 },
    [], // Connection points will be generated dynamically
    [
      {
        id: 'voltage',
        name: 'Voltage',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Busbar voltage (kV)',
      },
      {
        id: 'current',
        name: 'Current',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Busbar current (A)',
      },
      {
        id: 'power',
        name: 'Power',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Busbar power (MW)',
      },
    ],
    {
      fillColor: '#ff0000',
      strokeColor: '#ff0000',
      lineWidth: 2,
      busbar: {
        orientation: BusbarOrientation.VERTICAL,
        length: 200,
        width: 20,
        connectionPointCount: 3,
        connectionPointSpacing: 60,
        autoDistributePoints: true,
      },
    }
  ),

  // Legacy Busbar (for backward compatibility)
  createSymbolDefinition(
    'busbar',
    SymbolCategory.BUSBAR,
    'Busbar',
    svgPaths.busbar,
    { width: 100, height: 100 },
    [
      { id: 'left', position: { x: 0, y: 50 }, type: ConnectionPointType.BIDIRECTIONAL },
      { id: 'right', position: { x: 100, y: 50 }, type: ConnectionPointType.BIDIRECTIONAL },
      { id: 'top', position: { x: 50, y: 0 }, type: ConnectionPointType.BIDIRECTIONAL },
      { id: 'bottom', position: { x: 50, y: 100 }, type: ConnectionPointType.BIDIRECTIONAL },
    ],
    [
      {
        id: 'voltage',
        name: 'Voltage',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Busbar voltage (kV)',
      },
    ],
    {
      fillColor: '#ff0000',
      strokeColor: '#ff0000',
      lineWidth: 2,
    }
  ),

  // Current transformer
  createSymbolDefinition(
    'current-transformer',
    SymbolCategory.MEASUREMENT,
    'Current Transformer',
    svgPaths.currentTransformer,
    { width: 100, height: 100 },
    [
      { id: 'in', position: { x: 0, y: 50 }, type: ConnectionPointType.INPUT },
      { id: 'out', position: { x: 100, y: 50 }, type: ConnectionPointType.OUTPUT },
    ],
    [
      {
        id: 'current',
        name: 'Current',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Measured current (A)',
      },
    ]
  ),

  // Voltage transformer
  createSymbolDefinition(
    'voltage-transformer',
    SymbolCategory.MEASUREMENT,
    'Voltage Transformer',
    svgPaths.voltageTransformer,
    { width: 100, height: 100 },
    [
      { id: 'in', position: { x: 0, y: 50 }, type: ConnectionPointType.INPUT },
      { id: 'out', position: { x: 100, y: 50 }, type: ConnectionPointType.OUTPUT },
    ],
    [
      {
        id: 'voltage',
        name: 'Voltage',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Measured voltage (V)',
      },
    ]
  ),

  // Generator
  createSymbolDefinition(
    'generator',
    SymbolCategory.GENERATOR,
    'Generator',
    svgPaths.generator,
    { width: 100, height: 100 },
    [
      { id: 'out', position: { x: 100, y: 50 }, type: ConnectionPointType.OUTPUT },
    ],
    [
      {
        id: 'power',
        name: 'Power',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Generated power (MW)',
      },
      {
        id: 'status',
        name: 'Status',
        dataType: BindingDataType.ENUM,
        defaultValue: 'offline',
        enumValues: ['offline', 'starting', 'online', 'fault'],
        description: 'Generator status',
      },
    ]
  ),

  // Diesel Generator
  createSymbolDefinition(
    'diesel-generator',
    SymbolCategory.GENERATOR,
    'Diesel Generator',
    svgPaths.dieselGenerator,
    { width: 120, height: 100 },
    [
      { id: 'out', position: { x: 120, y: 50 }, type: ConnectionPointType.OUTPUT },
    ],
    [
      {
        id: 'power',
        name: 'Power',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Generated power (MW)',
      },
      {
        id: 'status',
        name: 'Status',
        dataType: BindingDataType.ENUM,
        defaultValue: 'offline',
        enumValues: ['offline', 'starting', 'synchronizing', 'online', 'fault', 'maintenance'],
        description: 'Diesel generator status',
      },
      {
        id: 'fuel_level',
        name: 'Fuel Level',
        dataType: BindingDataType.NUMBER,
        defaultValue: 100,
        description: 'Fuel tank level (%)',
      },
      {
        id: 'runtime',
        name: 'Runtime',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Engine runtime (hours)',
      },
      {
        id: 'temperature',
        name: 'Temperature',
        dataType: BindingDataType.NUMBER,
        defaultValue: 25,
        description: 'Engine temperature (°C)',
      },
    ]
  ),

  // Load
  createSymbolDefinition(
    'load',
    SymbolCategory.LOAD,
    'Load',
    svgPaths.load,
    { width: 100, height: 100 },
    [
      { id: 'in', position: { x: 50, y: 0 }, type: ConnectionPointType.INPUT },
    ],
    [
      {
        id: 'power',
        name: 'Power',
        dataType: BindingDataType.NUMBER,
        defaultValue: 0,
        description: 'Consumed power (kW)',
      },
    ]
  ),

  // Fuse
  createSymbolDefinition(
    'fuse',
    SymbolCategory.PROTECTION,
    'Fuse',
    svgPaths.fuse,
    { width: 100, height: 100 },
    [
      { id: 'in', position: { x: 0, y: 50 }, type: ConnectionPointType.INPUT },
      { id: 'out', position: { x: 100, y: 50 }, type: ConnectionPointType.OUTPUT },
    ],
    [
      {
        id: 'status',
        name: 'Status',
        dataType: BindingDataType.BOOLEAN,
        defaultValue: true,
        description: 'Fuse status (true = intact, false = blown)',
      },
    ]
  ),

  // Ground
  createSymbolDefinition(
    'ground',
    SymbolCategory.OTHER,
    'Ground',
    svgPaths.ground,
    { width: 100, height: 100 },
    [
      { id: 'connection', position: { x: 50, y: 0 }, type: ConnectionPointType.INPUT },
    ],
    []
  ),
];

// Function to get a symbol definition by ID
export function getSymbolDefinition(id: string): SymbolDefinition | undefined {
  return symbolLibrary.find(symbol => symbol.id === id);
}

// Function to get symbol definitions by category
export function getSymbolDefinitionsByCategory(category: SymbolCategory): SymbolDefinition[] {
  return symbolLibrary.filter(symbol => symbol.category === category);
}

// Export the symbol categories for UI display - Extended for GB standards
export const symbolCategories = [
  { id: SymbolCategory.DATACENTER, name: 'Data Center' },
  { id: SymbolCategory.SWITCHGEAR, name: 'Switchgear' },
  { id: SymbolCategory.TRANSFORMER, name: 'Transformers' },
  { id: SymbolCategory.BUSBAR, name: 'Busbars' },
  { id: SymbolCategory.MEASUREMENT, name: 'Measurement' },
  { id: SymbolCategory.PROTECTION, name: 'Protection' },
  { id: SymbolCategory.GENERATOR, name: 'Generators' },
  { id: SymbolCategory.LOAD, name: 'Loads' },
  { id: SymbolCategory.MOTOR, name: 'Motors' },
  { id: SymbolCategory.TRANSMISSION, name: 'Transmission' },
  { id: SymbolCategory.CAPACITOR, name: 'Capacitors' },
  { id: SymbolCategory.REACTOR, name: 'Reactors' },
  { id: SymbolCategory.RELAY, name: 'Relays' },
  { id: SymbolCategory.OTHER, name: 'Other' },
];
